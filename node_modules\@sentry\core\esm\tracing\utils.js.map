{"version": 3, "file": "utils.js", "sources": ["../../../src/tracing/utils.ts"], "sourcesContent": ["import type { Transaction } from '@sentry/types';\nimport { extractTraceparentData as _extractTraceparentData } from '@sentry/utils';\n\nimport type { Hub } from '../hub';\nimport { getCurrentHub } from '../hub';\n\n/**\n * Grabs active transaction off scope.\n *\n * @deprecated You should not rely on the transaction, but just use `startSpan()` APIs instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport function getActiveTransaction<T extends Transaction>(maybeHub?: Hub): T | undefined {\n  // eslint-disable-next-line deprecation/deprecation\n  const hub = maybeHub || getCurrentHub();\n  // eslint-disable-next-line deprecation/deprecation\n  const scope = hub.getScope();\n  // eslint-disable-next-line deprecation/deprecation\n  return scope.getTransaction() as T | undefined;\n}\n\n// so it can be used in manual instrumentation without necessitating a hard dependency on @sentry/utils\nexport { stripUrlQueryAndFragment } from '@sentry/utils';\n\n/**\n * The `extractTraceparentData` function and `TRACEPARENT_REGEXP` constant used\n * to be declared in this file. It was later moved into `@sentry/utils` as part of a\n * move to remove `@sentry/tracing` dependencies from `@sentry/node` (`extractTraceparentData`\n * is the only tracing function used by `@sentry/node`).\n *\n * These exports are kept here for backwards compatability's sake.\n *\n * See https://github.com/getsentry/sentry-javascript/issues/4642 for more details.\n *\n * @deprecated Import this function from `@sentry/utils` instead\n */\nexport const extractTraceparentData = _extractTraceparentData;\n"], "names": ["_extractTraceparentData"], "mappings": ";;;;AAMA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,oBAAoB,CAAwB,QAAQ,EAAuB;AAC3F;AACA,EAAE,MAAM,GAAI,GAAE,YAAY,aAAa,EAAE,CAAA;AACzC;AACA,EAAE,MAAM,KAAM,GAAE,GAAG,CAAC,QAAQ,EAAE,CAAA;AAC9B;AACA,EAAE,OAAO,KAAK,CAAC,cAAc,EAAG,EAAA;AAChC,CAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,sBAAuB,GAAEA;;;;"}