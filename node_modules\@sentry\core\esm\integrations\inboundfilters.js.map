{"version": 3, "file": "inboundfilters.js", "sources": ["../../../src/integrations/inboundfilters.ts"], "sourcesContent": ["import type { Client, Event, EventHint, Integration, IntegrationClass, IntegrationFn, StackFrame } from '@sentry/types';\nimport { getEventDescription, logger, stringMatchesSomePattern } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../debug-build';\nimport { convertIntegrationFnToClass, defineIntegration } from '../integration';\n\n// \"Script error.\" is hard coded into browsers for errors that it can't read.\n// this is the result of a script being pulled in from an external domain and CORS.\nconst DEFAULT_IGNORE_ERRORS = [\n  /^Script error\\.?$/,\n  /^Javascript error: Script error\\.? on line 0$/,\n  /^ResizeObserver loop completed with undelivered notifications.$/,\n  /^Cannot redefine property: googletag$/,\n];\n\nconst DEFAULT_IGNORE_TRANSACTIONS = [\n  /^.*\\/healthcheck$/,\n  /^.*\\/healthy$/,\n  /^.*\\/live$/,\n  /^.*\\/ready$/,\n  /^.*\\/heartbeat$/,\n  /^.*\\/health$/,\n  /^.*\\/healthz$/,\n];\n\n/** Options for the InboundFilters integration */\nexport interface InboundFiltersOptions {\n  allowUrls: Array<string | RegExp>;\n  denyUrls: Array<string | RegExp>;\n  ignoreErrors: Array<string | RegExp>;\n  ignoreTransactions: Array<string | RegExp>;\n  ignoreInternal: boolean;\n  disableErrorDefaults: boolean;\n  disableTransactionDefaults: boolean;\n}\n\nconst INTEGRATION_NAME = 'InboundFilters';\nconst _inboundFiltersIntegration = ((options: Partial<InboundFiltersOptions> = {}) => {\n  return {\n    name: INTEGRATION_NAME,\n    // TODO v8: Remove this\n    setupOnce() {}, // eslint-disable-line @typescript-eslint/no-empty-function\n    processEvent(event, _hint, client) {\n      const clientOptions = client.getOptions();\n      const mergedOptions = _mergeOptions(options, clientOptions);\n      return _shouldDropEvent(event, mergedOptions) ? null : event;\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const inboundFiltersIntegration = defineIntegration(_inboundFiltersIntegration);\n\n/**\n * Inbound filters configurable by the user.\n * @deprecated Use `inboundFiltersIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const InboundFilters = convertIntegrationFnToClass(\n  INTEGRATION_NAME,\n  inboundFiltersIntegration,\n) as IntegrationClass<Integration & { preprocessEvent: (event: Event, hint: EventHint, client: Client) => void }> & {\n  new (\n    options?: Partial<{\n      allowUrls: Array<string | RegExp>;\n      denyUrls: Array<string | RegExp>;\n      ignoreErrors: Array<string | RegExp>;\n      ignoreTransactions: Array<string | RegExp>;\n      ignoreInternal: boolean;\n      disableErrorDefaults: boolean;\n      disableTransactionDefaults: boolean;\n    }>,\n  ): Integration;\n};\n\nfunction _mergeOptions(\n  internalOptions: Partial<InboundFiltersOptions> = {},\n  clientOptions: Partial<InboundFiltersOptions> = {},\n): Partial<InboundFiltersOptions> {\n  return {\n    allowUrls: [...(internalOptions.allowUrls || []), ...(clientOptions.allowUrls || [])],\n    denyUrls: [...(internalOptions.denyUrls || []), ...(clientOptions.denyUrls || [])],\n    ignoreErrors: [\n      ...(internalOptions.ignoreErrors || []),\n      ...(clientOptions.ignoreErrors || []),\n      ...(internalOptions.disableErrorDefaults ? [] : DEFAULT_IGNORE_ERRORS),\n    ],\n    ignoreTransactions: [\n      ...(internalOptions.ignoreTransactions || []),\n      ...(clientOptions.ignoreTransactions || []),\n      ...(internalOptions.disableTransactionDefaults ? [] : DEFAULT_IGNORE_TRANSACTIONS),\n    ],\n    ignoreInternal: internalOptions.ignoreInternal !== undefined ? internalOptions.ignoreInternal : true,\n  };\n}\n\nfunction _shouldDropEvent(event: Event, options: Partial<InboundFiltersOptions>): boolean {\n  if (options.ignoreInternal && _isSentryError(event)) {\n    DEBUG_BUILD &&\n      logger.warn(`Event dropped due to being internal Sentry Error.\\nEvent: ${getEventDescription(event)}`);\n    return true;\n  }\n  if (_isIgnoredError(event, options.ignoreErrors)) {\n    DEBUG_BUILD &&\n      logger.warn(\n        `Event dropped due to being matched by \\`ignoreErrors\\` option.\\nEvent: ${getEventDescription(event)}`,\n      );\n    return true;\n  }\n  if (_isIgnoredTransaction(event, options.ignoreTransactions)) {\n    DEBUG_BUILD &&\n      logger.warn(\n        `Event dropped due to being matched by \\`ignoreTransactions\\` option.\\nEvent: ${getEventDescription(event)}`,\n      );\n    return true;\n  }\n  if (_isDeniedUrl(event, options.denyUrls)) {\n    DEBUG_BUILD &&\n      logger.warn(\n        `Event dropped due to being matched by \\`denyUrls\\` option.\\nEvent: ${getEventDescription(\n          event,\n        )}.\\nUrl: ${_getEventFilterUrl(event)}`,\n      );\n    return true;\n  }\n  if (!_isAllowedUrl(event, options.allowUrls)) {\n    DEBUG_BUILD &&\n      logger.warn(\n        `Event dropped due to not being matched by \\`allowUrls\\` option.\\nEvent: ${getEventDescription(\n          event,\n        )}.\\nUrl: ${_getEventFilterUrl(event)}`,\n      );\n    return true;\n  }\n  return false;\n}\n\nfunction _isIgnoredError(event: Event, ignoreErrors?: Array<string | RegExp>): boolean {\n  // If event.type, this is not an error\n  if (event.type || !ignoreErrors || !ignoreErrors.length) {\n    return false;\n  }\n\n  return _getPossibleEventMessages(event).some(message => stringMatchesSomePattern(message, ignoreErrors));\n}\n\nfunction _isIgnoredTransaction(event: Event, ignoreTransactions?: Array<string | RegExp>): boolean {\n  if (event.type !== 'transaction' || !ignoreTransactions || !ignoreTransactions.length) {\n    return false;\n  }\n\n  const name = event.transaction;\n  return name ? stringMatchesSomePattern(name, ignoreTransactions) : false;\n}\n\nfunction _isDeniedUrl(event: Event, denyUrls?: Array<string | RegExp>): boolean {\n  // TODO: Use Glob instead?\n  if (!denyUrls || !denyUrls.length) {\n    return false;\n  }\n  const url = _getEventFilterUrl(event);\n  return !url ? false : stringMatchesSomePattern(url, denyUrls);\n}\n\nfunction _isAllowedUrl(event: Event, allowUrls?: Array<string | RegExp>): boolean {\n  // TODO: Use Glob instead?\n  if (!allowUrls || !allowUrls.length) {\n    return true;\n  }\n  const url = _getEventFilterUrl(event);\n  return !url ? true : stringMatchesSomePattern(url, allowUrls);\n}\n\nfunction _getPossibleEventMessages(event: Event): string[] {\n  const possibleMessages: string[] = [];\n\n  if (event.message) {\n    possibleMessages.push(event.message);\n  }\n\n  let lastException;\n  try {\n    // @ts-expect-error Try catching to save bundle size\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    lastException = event.exception.values[event.exception.values.length - 1];\n  } catch (e) {\n    // try catching to save bundle size checking existence of variables\n  }\n\n  if (lastException) {\n    if (lastException.value) {\n      possibleMessages.push(lastException.value);\n      if (lastException.type) {\n        possibleMessages.push(`${lastException.type}: ${lastException.value}`);\n      }\n    }\n  }\n\n  if (DEBUG_BUILD && possibleMessages.length === 0) {\n    logger.error(`Could not extract message for event ${getEventDescription(event)}`);\n  }\n\n  return possibleMessages;\n}\n\nfunction _isSentryError(event: Event): boolean {\n  try {\n    // @ts-expect-error can't be a sentry error if undefined\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    return event.exception.values[0].type === 'SentryError';\n  } catch (e) {\n    // ignore\n  }\n  return false;\n}\n\nfunction _getLastValidUrl(frames: StackFrame[] = []): string | null {\n  for (let i = frames.length - 1; i >= 0; i--) {\n    const frame = frames[i];\n\n    if (frame && frame.filename !== '<anonymous>' && frame.filename !== '[native code]') {\n      return frame.filename || null;\n    }\n  }\n\n  return null;\n}\n\nfunction _getEventFilterUrl(event: Event): string | null {\n  try {\n    let frames;\n    try {\n      // @ts-expect-error we only care about frames if the whole thing here is defined\n      frames = event.exception.values[0].stacktrace.frames;\n    } catch (e) {\n      // ignore\n    }\n    return frames ? _getLastValidUrl(frames) : null;\n  } catch (oO) {\n    DEBUG_BUILD && logger.error(`Cannot extract url for event ${getEventDescription(event)}`);\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;;AAMA;AACA;AACA,MAAM,wBAAwB;AAC9B,EAAE,mBAAmB;AACrB,EAAE,+CAA+C;AACjD,EAAE,iEAAiE;AACnE,EAAE,uCAAuC;AACzC,CAAC,CAAA;AACD;AACA,MAAM,8BAA8B;AACpC,EAAE,mBAAmB;AACrB,EAAE,eAAe;AACjB,EAAE,YAAY;AACd,EAAE,aAAa;AACf,EAAE,iBAAiB;AACnB,EAAE,cAAc;AAChB,EAAE,eAAe;AACjB,CAAC,CAAA;AACD;AACA;;AAWA,MAAM,gBAAA,GAAmB,gBAAgB,CAAA;AACzC,MAAM,0BAAA,IAA8B,CAAC,OAAO,GAAmC,EAAE,KAAK;AACtF,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B;AACA,IAAI,SAAS,GAAG,EAAE;AAClB,IAAI,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE;AACvC,MAAM,MAAM,aAAc,GAAE,MAAM,CAAC,UAAU,EAAE,CAAA;AAC/C,MAAM,MAAM,gBAAgB,aAAa,CAAC,OAAO,EAAE,aAAa,CAAC,CAAA;AACjE,MAAM,OAAO,gBAAgB,CAAC,KAAK,EAAE,aAAa,CAAE,GAAE,IAAK,GAAE,KAAK,CAAA;AAClE,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;MACa,yBAA0B,GAAE,iBAAiB,CAAC,0BAA0B,EAAC;AACtF;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,cAAe,GAAE,2BAA2B;AACzD,EAAE,gBAAgB;AAClB,EAAE,yBAAyB;AAC3B,CAAE;;CAYF;AACA;AACA,SAAS,aAAa;AACtB,EAAE,eAAe,GAAmC,EAAE;AACtD,EAAE,aAAa,GAAmC,EAAE;AACpD,EAAkC;AAClC,EAAE,OAAO;AACT,IAAI,SAAS,EAAE,CAAC,IAAI,eAAe,CAAC,SAAU,IAAG,EAAE,CAAC,EAAE,IAAI,aAAa,CAAC,aAAa,EAAE,CAAC,CAAC;AACzF,IAAI,QAAQ,EAAE,CAAC,IAAI,eAAe,CAAC,QAAS,IAAG,EAAE,CAAC,EAAE,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC,CAAC;AACtF,IAAI,YAAY,EAAE;AAClB,MAAM,IAAI,eAAe,CAAC,gBAAgB,EAAE,CAAC;AAC7C,MAAM,IAAI,aAAa,CAAC,gBAAgB,EAAE,CAAC;AAC3C,MAAM,IAAI,eAAe,CAAC,oBAAA,GAAuB,EAAC,GAAI,qBAAqB,CAAC;AAC5E,KAAK;AACL,IAAI,kBAAkB,EAAE;AACxB,MAAM,IAAI,eAAe,CAAC,sBAAsB,EAAE,CAAC;AACnD,MAAM,IAAI,aAAa,CAAC,sBAAsB,EAAE,CAAC;AACjD,MAAM,IAAI,eAAe,CAAC,0BAAA,GAA6B,EAAC,GAAI,2BAA2B,CAAC;AACxF,KAAK;AACL,IAAI,cAAc,EAAE,eAAe,CAAC,cAAe,KAAI,SAAU,GAAE,eAAe,CAAC,cAAe,GAAE,IAAI;AACxG,GAAG,CAAA;AACH,CAAA;AACA;AACA,SAAS,gBAAgB,CAAC,KAAK,EAAS,OAAO,EAA2C;AAC1F,EAAE,IAAI,OAAO,CAAC,cAAA,IAAkB,cAAc,CAAC,KAAK,CAAC,EAAE;AACvD,IAAI,WAAY;AAChB,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,0DAA0D,EAAE,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAA,CAAA,CAAA;AACA,IAAA,OAAA,IAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,eAAA,CAAA,KAAA,EAAA,OAAA,CAAA,YAAA,CAAA,EAAA;AACA,IAAA,WAAA;AACA,MAAA,MAAA,CAAA,IAAA;AACA,QAAA,CAAA,uEAAA,EAAA,mBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA,OAAA,CAAA;AACA,IAAA,OAAA,IAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,qBAAA,CAAA,KAAA,EAAA,OAAA,CAAA,kBAAA,CAAA,EAAA;AACA,IAAA,WAAA;AACA,MAAA,MAAA,CAAA,IAAA;AACA,QAAA,CAAA,6EAAA,EAAA,mBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA,OAAA,CAAA;AACA,IAAA,OAAA,IAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,YAAA,CAAA,KAAA,EAAA,OAAA,CAAA,QAAA,CAAA,EAAA;AACA,IAAA,WAAA;AACA,MAAA,MAAA,CAAA,IAAA;AACA,QAAA,CAAA,mEAAA,EAAA,mBAAA;AACA,UAAA,KAAA;AACA,SAAA,CAAA,QAAA,EAAA,kBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA,OAAA,CAAA;AACA,IAAA,OAAA,IAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,CAAA,aAAA,CAAA,KAAA,EAAA,OAAA,CAAA,SAAA,CAAA,EAAA;AACA,IAAA,WAAA;AACA,MAAA,MAAA,CAAA,IAAA;AACA,QAAA,CAAA,wEAAA,EAAA,mBAAA;AACA,UAAA,KAAA;AACA,SAAA,CAAA,QAAA,EAAA,kBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA,OAAA,CAAA;AACA,IAAA,OAAA,IAAA,CAAA;AACA,GAAA;AACA,EAAA,OAAA,KAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,eAAA,CAAA,KAAA,EAAA,YAAA,EAAA;AACA;AACA,EAAA,IAAA,KAAA,CAAA,IAAA,IAAA,CAAA,YAAA,IAAA,CAAA,YAAA,CAAA,MAAA,EAAA;AACA,IAAA,OAAA,KAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,yBAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,OAAA,IAAA,wBAAA,CAAA,OAAA,EAAA,YAAA,CAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,qBAAA,CAAA,KAAA,EAAA,kBAAA,EAAA;AACA,EAAA,IAAA,KAAA,CAAA,IAAA,KAAA,aAAA,IAAA,CAAA,kBAAA,IAAA,CAAA,kBAAA,CAAA,MAAA,EAAA;AACA,IAAA,OAAA,KAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,MAAA,IAAA,GAAA,KAAA,CAAA,WAAA,CAAA;AACA,EAAA,OAAA,IAAA,GAAA,wBAAA,CAAA,IAAA,EAAA,kBAAA,CAAA,GAAA,KAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,YAAA,CAAA,KAAA,EAAA,QAAA,EAAA;AACA;AACA,EAAA,IAAA,CAAA,QAAA,IAAA,CAAA,QAAA,CAAA,MAAA,EAAA;AACA,IAAA,OAAA,KAAA,CAAA;AACA,GAAA;AACA,EAAA,MAAA,GAAA,GAAA,kBAAA,CAAA,KAAA,CAAA,CAAA;AACA,EAAA,OAAA,CAAA,GAAA,GAAA,KAAA,GAAA,wBAAA,CAAA,GAAA,EAAA,QAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,aAAA,CAAA,KAAA,EAAA,SAAA,EAAA;AACA;AACA,EAAA,IAAA,CAAA,SAAA,IAAA,CAAA,SAAA,CAAA,MAAA,EAAA;AACA,IAAA,OAAA,IAAA,CAAA;AACA,GAAA;AACA,EAAA,MAAA,GAAA,GAAA,kBAAA,CAAA,KAAA,CAAA,CAAA;AACA,EAAA,OAAA,CAAA,GAAA,GAAA,IAAA,GAAA,wBAAA,CAAA,GAAA,EAAA,SAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,yBAAA,CAAA,KAAA,EAAA;AACA,EAAA,MAAA,gBAAA,GAAA,EAAA,CAAA;AACA;AACA,EAAA,IAAA,KAAA,CAAA,OAAA,EAAA;AACA,IAAA,gBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,aAAA,CAAA;AACA,EAAA,IAAA;AACA;AACA;AACA,IAAA,aAAA,GAAA,KAAA,CAAA,SAAA,CAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,MAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA;AACA,GAAA,CAAA,OAAA,CAAA,EAAA;AACA;AACA,GAAA;AACA;AACA,EAAA,IAAA,aAAA,EAAA;AACA,IAAA,IAAA,aAAA,CAAA,KAAA,EAAA;AACA,MAAA,gBAAA,CAAA,IAAA,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA;AACA,MAAA,IAAA,aAAA,CAAA,IAAA,EAAA;AACA,QAAA,gBAAA,CAAA,IAAA,CAAA,CAAA,EAAA,aAAA,CAAA,IAAA,CAAA,EAAA,EAAA,aAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACA,OAAA;AACA,KAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,WAAA,IAAA,gBAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,IAAA,MAAA,CAAA,KAAA,CAAA,CAAA,oCAAA,EAAA,mBAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,gBAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,cAAA,CAAA,KAAA,EAAA;AACA,EAAA,IAAA;AACA;AACA;AACA,IAAA,OAAA,KAAA,CAAA,SAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA,KAAA,aAAA,CAAA;AACA,GAAA,CAAA,OAAA,CAAA,EAAA;AACA;AACA,GAAA;AACA,EAAA,OAAA,KAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,gBAAA,CAAA,MAAA,GAAA,EAAA,EAAA;AACA,EAAA,KAAA,IAAA,CAAA,GAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,EAAA;AACA,IAAA,MAAA,KAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AACA;AACA,IAAA,IAAA,KAAA,IAAA,KAAA,CAAA,QAAA,KAAA,aAAA,IAAA,KAAA,CAAA,QAAA,KAAA,eAAA,EAAA;AACA,MAAA,OAAA,KAAA,CAAA,QAAA,IAAA,IAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,IAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,kBAAA,CAAA,KAAA,EAAA;AACA,EAAA,IAAA;AACA,IAAA,IAAA,MAAA,CAAA;AACA,IAAA,IAAA;AACA;AACA,MAAA,MAAA,GAAA,KAAA,CAAA,SAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,MAAA,CAAA;AACA,KAAA,CAAA,OAAA,CAAA,EAAA;AACA;AACA,KAAA;AACA,IAAA,OAAA,MAAA,GAAA,gBAAA,CAAA,MAAA,CAAA,GAAA,IAAA,CAAA;AACA,GAAA,CAAA,OAAA,EAAA,EAAA;AACA,IAAA,WAAA,IAAA,MAAA,CAAA,KAAA,CAAA,CAAA,6BAAA,EAAA,mBAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA,OAAA,IAAA,CAAA;AACA,GAAA;AACA;;;;"}