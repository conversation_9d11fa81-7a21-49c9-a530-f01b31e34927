{"version": 3, "file": "spanUtils.js", "sources": ["../../../src/utils/spanUtils.ts"], "sourcesContent": ["import type { Span, SpanJSON, SpanTimeInput, TraceContext } from '@sentry/types';\nimport { dropUndefinedKeys, generateSentryTraceHeader, timestampInSeconds } from '@sentry/utils';\nimport type { Span as SpanClass } from '../tracing/span';\n\n// These are aligned with OpenTelemetry trace flags\nexport const TRACE_FLAG_NONE = 0x0;\nexport const TRACE_FLAG_SAMPLED = 0x1;\n\n/**\n * Convert a span to a trace context, which can be sent as the `trace` context in an event.\n */\nexport function spanToTraceContext(span: Span): TraceContext {\n  const { spanId: span_id, traceId: trace_id } = span.spanContext();\n  const { data, op, parent_span_id, status, tags, origin } = spanToJSON(span);\n\n  return dropUndefinedKeys({\n    data,\n    op,\n    parent_span_id,\n    span_id,\n    status,\n    tags,\n    trace_id,\n    origin,\n  });\n}\n\n/**\n * Convert a Span to a Sentry trace header.\n */\nexport function spanToTraceHeader(span: Span): string {\n  const { traceId, spanId } = span.spanContext();\n  const sampled = spanIsSampled(span);\n  return generateSentryTraceHeader(traceId, spanId, sampled);\n}\n\n/**\n * Convert a span time input intp a timestamp in seconds.\n */\nexport function spanTimeInputToSeconds(input: SpanTimeInput | undefined): number {\n  if (typeof input === 'number') {\n    return ensureTimestampInSeconds(input);\n  }\n\n  if (Array.isArray(input)) {\n    // See {@link HrTime} for the array-based time format\n    return input[0] + input[1] / 1e9;\n  }\n\n  if (input instanceof Date) {\n    return ensureTimestampInSeconds(input.getTime());\n  }\n\n  return timestampInSeconds();\n}\n\n/**\n * Converts a timestamp to second, if it was in milliseconds, or keeps it as second.\n */\nfunction ensureTimestampInSeconds(timestamp: number): number {\n  const isMs = timestamp > 9999999999;\n  return isMs ? timestamp / 1000 : timestamp;\n}\n\n/**\n * Convert a span to a JSON representation.\n * Note that all fields returned here are optional and need to be guarded against.\n *\n * Note: Because of this, we currently have a circular type dependency (which we opted out of in package.json).\n * This is not avoidable as we need `spanToJSON` in `spanUtils.ts`, which in turn is needed by `span.ts` for backwards compatibility.\n * And `spanToJSON` needs the Span class from `span.ts` to check here.\n * TODO v8: When we remove the deprecated stuff from `span.ts`, we can remove the circular dependency again.\n */\nexport function spanToJSON(span: Span): Partial<SpanJSON> {\n  if (spanIsSpanClass(span)) {\n    return span.getSpanJSON();\n  }\n\n  // Fallback: We also check for `.toJSON()` here...\n  // eslint-disable-next-line deprecation/deprecation\n  if (typeof span.toJSON === 'function') {\n    // eslint-disable-next-line deprecation/deprecation\n    return span.toJSON();\n  }\n\n  return {};\n}\n\n/**\n * Sadly, due to circular dependency checks we cannot actually import the Span class here and check for instanceof.\n * :( So instead we approximate this by checking if it has the `getSpanJSON` method.\n */\nfunction spanIsSpanClass(span: Span): span is SpanClass {\n  return typeof (span as SpanClass).getSpanJSON === 'function';\n}\n\n/**\n * Returns true if a span is sampled.\n * In most cases, you should just use `span.isRecording()` instead.\n * However, this has a slightly different semantic, as it also returns false if the span is finished.\n * So in the case where this distinction is important, use this method.\n */\nexport function spanIsSampled(span: Span): boolean {\n  // We align our trace flags with the ones OpenTelemetry use\n  // So we also check for sampled the same way they do.\n  const { traceFlags } = span.spanContext();\n  // eslint-disable-next-line no-bitwise\n  return Boolean(traceFlags & TRACE_FLAG_SAMPLED);\n}\n"], "names": ["dropUndefinedKeys", "generateSentryTraceHeader", "timestampInSeconds"], "mappings": ";;;;AAIA;AACO,MAAM,eAAgB,GAAE,IAAG;AAC3B,MAAM,kBAAmB,GAAE,IAAG;AACrC;AACA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,IAAI,EAAsB;AAC7D,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAA,KAAa,IAAI,CAAC,WAAW,EAAE,CAAA;AACnE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE,IAAI,EAAE,MAAO,EAAA,GAAI,UAAU,CAAC,IAAI,CAAC,CAAA;AAC7E;AACA,EAAE,OAAOA,uBAAiB,CAAC;AAC3B,IAAI,IAAI;AACR,IAAI,EAAE;AACN,IAAI,cAAc;AAClB,IAAI,OAAO;AACX,IAAI,MAAM;AACV,IAAI,IAAI;AACR,IAAI,QAAQ;AACZ,IAAI,MAAM;AACV,GAAG,CAAC,CAAA;AACJ,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,IAAI,EAAgB;AACtD,EAAE,MAAM,EAAE,OAAO,EAAE,MAAA,EAAS,GAAE,IAAI,CAAC,WAAW,EAAE,CAAA;AAChD,EAAE,MAAM,OAAQ,GAAE,aAAa,CAAC,IAAI,CAAC,CAAA;AACrC,EAAE,OAAOC,+BAAyB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;AAC5D,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,sBAAsB,CAAC,KAAK,EAAqC;AACjF,EAAE,IAAI,OAAO,KAAM,KAAI,QAAQ,EAAE;AACjC,IAAI,OAAO,wBAAwB,CAAC,KAAK,CAAC,CAAA;AAC1C,GAAE;AACF;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC5B;AACA,IAAI,OAAO,KAAK,CAAC,CAAC,CAAA,GAAI,KAAK,CAAC,CAAC,CAAE,GAAE,GAAG,CAAA;AACpC,GAAE;AACF;AACA,EAAE,IAAI,KAAM,YAAW,IAAI,EAAE;AAC7B,IAAI,OAAO,wBAAwB,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;AACpD,GAAE;AACF;AACA,EAAE,OAAOC,wBAAkB,EAAE,CAAA;AAC7B,CAAA;AACA;AACA;AACA;AACA;AACA,SAAS,wBAAwB,CAAC,SAAS,EAAkB;AAC7D,EAAE,MAAM,IAAA,GAAO,SAAA,GAAY,UAAU,CAAA;AACrC,EAAE,OAAO,IAAK,GAAE,YAAY,IAAA,GAAO,SAAS,CAAA;AAC5C,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,CAAC,IAAI,EAA2B;AAC1D,EAAE,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE;AAC7B,IAAI,OAAO,IAAI,CAAC,WAAW,EAAE,CAAA;AAC7B,GAAE;AACF;AACA;AACA;AACA,EAAE,IAAI,OAAO,IAAI,CAAC,MAAO,KAAI,UAAU,EAAE;AACzC;AACA,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE,CAAA;AACxB,GAAE;AACF;AACA,EAAE,OAAO,EAAE,CAAA;AACX,CAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,IAAI,EAA2B;AACxD,EAAE,OAAO,OAAO,CAAC,IAAA,GAAmB,WAAA,KAAgB,UAAU,CAAA;AAC9D,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAC,IAAI,EAAiB;AACnD;AACA;AACA,EAAE,MAAM,EAAE,UAAW,EAAA,GAAI,IAAI,CAAC,WAAW,EAAE,CAAA;AAC3C;AACA,EAAE,OAAO,OAAO,CAAC,UAAW,GAAE,kBAAkB,CAAC,CAAA;AACjD;;;;;;;;;;"}