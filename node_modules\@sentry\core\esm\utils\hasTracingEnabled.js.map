{"version": 3, "file": "hasTracingEnabled.js", "sources": ["../../../src/utils/hasTracingEnabled.ts"], "sourcesContent": ["import type { Options } from '@sentry/types';\n\nimport { getClient } from '../exports';\n\n// Treeshakable guard to remove all code related to tracing\ndeclare const __SENTRY_TRACING__: boolean | undefined;\n\n/**\n * Determines if tracing is currently enabled.\n *\n * Tracing is enabled when at least one of `tracesSampleRate` and `tracesSampler` is defined in the SDK config.\n */\nexport function hasTracingEnabled(\n  maybeOptions?: Pick<Options, 'tracesSampleRate' | 'tracesSampler' | 'enableTracing'> | undefined,\n): boolean {\n  if (typeof __SENTRY_TRACING__ === 'boolean' && !__SENTRY_TRACING__) {\n    return false;\n  }\n\n  const client = getClient();\n  const options = maybeOptions || (client && client.getOptions());\n  return !!options && (options.enableTracing || 'tracesSampleRate' in options || 'tracesSampler' in options);\n}\n"], "names": [], "mappings": ";;AAIA;;AAGA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB;AACjC,EAAE,YAAY;AACd,EAAW;AACX,EAAE,IAAI,OAAO,kBAAA,KAAuB,SAAU,IAAG,CAAC,kBAAkB,EAAE;AACtE,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE,CAAA;AAC5B,EAAE,MAAM,OAAA,GAAU,YAAA,KAAiB,MAAO,IAAG,MAAM,CAAC,UAAU,EAAE,CAAC,CAAA;AACjE,EAAE,OAAO,CAAC,CAAC,OAAQ,KAAI,OAAO,CAAC,aAAc,IAAG,sBAAsB,OAAA,IAAW,eAAgB,IAAG,OAAO,CAAC,CAAA;AAC5G;;;;"}