{"version": 3, "file": "utils.js", "sources": ["../../../src/metrics/utils.ts"], "sourcesContent": ["import type { MeasurementUnit, MetricBucketItem, Primitive } from '@sentry/types';\nimport { dropUndefinedKeys } from '@sentry/utils';\nimport type { MetricType } from './types';\n\n/**\n * Generate bucket key from metric properties.\n */\nexport function getBucketKey(\n  metricType: MetricType,\n  name: string,\n  unit: MeasurementUnit,\n  tags: Record<string, string>,\n): string {\n  const stringifiedTags = Object.entries(dropUndefinedKeys(tags)).sort((a, b) => a[0].localeCompare(b[0]));\n  return `${metricType}${name}${unit}${stringifiedTags}`;\n}\n\n/* eslint-disable no-bitwise */\n/**\n * Simple hash function for strings.\n */\nexport function simpleHash(s: string): number {\n  let rv = 0;\n  for (let i = 0; i < s.length; i++) {\n    const c = s.charCodeAt(i);\n    rv = (rv << 5) - rv + c;\n    rv &= rv;\n  }\n  return rv >>> 0;\n}\n/* eslint-enable no-bitwise */\n\n/**\n * Serialize metrics buckets into a string based on statsd format.\n *\n * Example of format:\n * metric.name@second:1:1.2|d|#a:value,b:anothervalue|T12345677\n * Segments:\n * name: metric.name\n * unit: second\n * value: [1, 1.2]\n * type of metric: d (distribution)\n * tags: { a: value, b: anothervalue }\n * timestamp: 12345677\n */\nexport function serializeMetricBuckets(metricBucketItems: MetricBucketItem[]): string {\n  let out = '';\n  for (const item of metricBucketItems) {\n    const tagEntries = Object.entries(item.tags);\n    const maybeTags = tagEntries.length > 0 ? `|#${tagEntries.map(([key, value]) => `${key}:${value}`).join(',')}` : '';\n    out += `${item.name}@${item.unit}:${item.metric}|${item.metricType}${maybeTags}|T${item.timestamp}\\n`;\n  }\n  return out;\n}\n\n/** Sanitizes units */\nexport function sanitizeUnit(unit: string): string {\n  return unit.replace(/[^\\w]+/gi, '_');\n}\n\n/** Sanitizes metric keys */\nexport function sanitizeMetricKey(key: string): string {\n  return key.replace(/[^\\w\\-.]+/gi, '_');\n}\n\nfunction sanitizeTagKey(key: string): string {\n  return key.replace(/[^\\w\\-./]+/gi, '');\n}\n\nconst tagValueReplacements: [string, string][] = [\n  ['\\n', '\\\\n'],\n  ['\\r', '\\\\r'],\n  ['\\t', '\\\\t'],\n  ['\\\\', '\\\\\\\\'],\n  ['|', '\\\\u{7c}'],\n  [',', '\\\\u{2c}'],\n];\n\nfunction getCharOrReplacement(input: string): string {\n  for (const [search, replacement] of tagValueReplacements) {\n    if (input === search) {\n      return replacement;\n    }\n  }\n\n  return input;\n}\n\nfunction sanitizeTagValue(value: string): string {\n  return [...value].reduce((acc, char) => acc + getCharOrReplacement(char), '');\n}\n\n/**\n * Sanitizes tags.\n */\nexport function sanitizeTags(unsanitizedTags: Record<string, Primitive>): Record<string, string> {\n  const tags: Record<string, string> = {};\n  for (const key in unsanitizedTags) {\n    if (Object.prototype.hasOwnProperty.call(unsanitizedTags, key)) {\n      const sanitizedKey = sanitizeTagKey(key);\n      tags[sanitizedKey] = sanitizeTagValue(String(unsanitizedTags[key]));\n    }\n  }\n  return tags;\n}\n"], "names": [], "mappings": ";;AAIA;AACA;AACA;AACO,SAAS,YAAY;AAC5B,EAAE,UAAU;AACZ,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAU;AACV,EAAE,MAAM,eAAA,GAAkB,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAC1G,EAAE,OAAO,CAAC,EAAA,UAAA,CAAA,EAAA,IAAA,CAAA,EAAA,IAAA,CAAA,EAAA,eAAA,CAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,UAAA,CAAA,CAAA,EAAA;AACA,EAAA,IAAA,EAAA,GAAA,CAAA,CAAA;AACA,EAAA,KAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,IAAA,MAAA,CAAA,GAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA,EAAA,GAAA,CAAA,EAAA,IAAA,CAAA,IAAA,EAAA,GAAA,CAAA,CAAA;AACA,IAAA,EAAA,IAAA,EAAA,CAAA;AACA,GAAA;AACA,EAAA,OAAA,EAAA,KAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,sBAAA,CAAA,iBAAA,EAAA;AACA,EAAA,IAAA,GAAA,GAAA,EAAA,CAAA;AACA,EAAA,KAAA,MAAA,IAAA,IAAA,iBAAA,EAAA;AACA,IAAA,MAAA,UAAA,GAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AACA,IAAA,MAAA,SAAA,GAAA,UAAA,CAAA,MAAA,GAAA,CAAA,GAAA,CAAA,EAAA,EAAA,UAAA,CAAA,GAAA,CAAA,CAAA,CAAA,GAAA,EAAA,KAAA,CAAA,KAAA,CAAA,EAAA,GAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,GAAA,EAAA,CAAA;AACA,IAAA,GAAA,IAAA,CAAA,EAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,EAAA,SAAA,CAAA,EAAA,EAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,CAAA;AACA,GAAA;AACA,EAAA,OAAA,GAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,YAAA,CAAA,IAAA,EAAA;AACA,EAAA,OAAA,IAAA,CAAA,OAAA,CAAA,UAAA,EAAA,GAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,iBAAA,CAAA,GAAA,EAAA;AACA,EAAA,OAAA,GAAA,CAAA,OAAA,CAAA,aAAA,EAAA,GAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,cAAA,CAAA,GAAA,EAAA;AACA,EAAA,OAAA,GAAA,CAAA,OAAA,CAAA,cAAA,EAAA,EAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA,MAAA,oBAAA,GAAA;AACA,EAAA,CAAA,IAAA,EAAA,KAAA,CAAA;AACA,EAAA,CAAA,IAAA,EAAA,KAAA,CAAA;AACA,EAAA,CAAA,IAAA,EAAA,KAAA,CAAA;AACA,EAAA,CAAA,IAAA,EAAA,MAAA,CAAA;AACA,EAAA,CAAA,GAAA,EAAA,SAAA,CAAA;AACA,EAAA,CAAA,GAAA,EAAA,SAAA,CAAA;AACA,CAAA,CAAA;AACA;AACA,SAAA,oBAAA,CAAA,KAAA,EAAA;AACA,EAAA,KAAA,MAAA,CAAA,MAAA,EAAA,WAAA,CAAA,IAAA,oBAAA,EAAA;AACA,IAAA,IAAA,KAAA,KAAA,MAAA,EAAA;AACA,MAAA,OAAA,WAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,KAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,gBAAA,CAAA,KAAA,EAAA;AACA,EAAA,OAAA,CAAA,GAAA,KAAA,CAAA,CAAA,MAAA,CAAA,CAAA,GAAA,EAAA,IAAA,KAAA,GAAA,GAAA,oBAAA,CAAA,IAAA,CAAA,EAAA,EAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,YAAA,CAAA,eAAA,EAAA;AACA,EAAA,MAAA,IAAA,GAAA,EAAA,CAAA;AACA,EAAA,KAAA,MAAA,GAAA,IAAA,eAAA,EAAA;AACA,IAAA,IAAA,MAAA,CAAA,SAAA,CAAA,cAAA,CAAA,IAAA,CAAA,eAAA,EAAA,GAAA,CAAA,EAAA;AACA,MAAA,MAAA,YAAA,GAAA,cAAA,CAAA,GAAA,CAAA,CAAA;AACA,MAAA,IAAA,CAAA,YAAA,CAAA,GAAA,gBAAA,CAAA,MAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA,EAAA,OAAA,IAAA,CAAA;AACA;;;;"}