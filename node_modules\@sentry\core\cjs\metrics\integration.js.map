{"version": 3, "file": "integration.js", "sources": ["../../../src/metrics/integration.ts"], "sourcesContent": ["import type { Client, ClientOptions, Integration, IntegrationClass, IntegrationFn } from '@sentry/types';\nimport type { BaseClient } from '../baseclient';\nimport { convertIntegrationFnToClass, defineIntegration } from '../integration';\nimport { BrowserMetricsAggregator } from './browser-aggregator';\n\nconst INTEGRATION_NAME = 'MetricsAggregator';\n\nconst _metricsAggregatorIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    // TODO v8: Remove this\n    setupOnce() {}, // eslint-disable-line @typescript-eslint/no-empty-function\n    setup(client: BaseClient<ClientOptions>) {\n      client.metricsAggregator = new BrowserMetricsAggregator(client);\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const metricsAggregatorIntegration = defineIntegration(_metricsAggregatorIntegration);\n\n/**\n * Enables Sentry metrics monitoring.\n *\n * @experimental This API is experimental and might having breaking changes in the future.\n * @deprecated Use `metricsAggegratorIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const MetricsAggregator = convertIntegrationFnToClass(\n  INTEGRATION_NAME,\n  metricsAggregatorIntegration,\n) as IntegrationClass<Integration & { setup: (client: Client) => void }>;\n"], "names": ["BrowserMetricsAggregator", "defineIntegration", "convertIntegrationFnToClass"], "mappings": ";;;;;AAKA,MAAM,gBAAA,GAAmB,mBAAmB,CAAA;AAC5C;AACA,MAAM,6BAA8B,IAAG,MAAM;AAC7C,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B;AACA,IAAI,SAAS,GAAG,EAAE;AAClB,IAAI,KAAK,CAAC,MAAM,EAA6B;AAC7C,MAAM,MAAM,CAAC,iBAAkB,GAAE,IAAIA,0CAAwB,CAAC,MAAM,CAAC,CAAA;AACrE,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;MACa,4BAA6B,GAAEC,6BAAiB,CAAC,6BAA6B,EAAC;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,iBAAkB,GAAEC,uCAA2B;AAC5D,EAAE,gBAAgB;AAClB,EAAE,4BAA4B;AAC9B,CAAE;;;;;"}