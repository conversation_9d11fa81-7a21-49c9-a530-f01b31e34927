{"version": 3, "sources": ["../../../../node_modules/@sentry/src/is.ts", "../../../../node_modules/@sentry/src/worldwide.ts", "../../../../node_modules/@sentry/src/browser.ts", "../../../../node_modules/@sentry/src/error.ts", "../../../../node_modules/@sentry/src/dsn.ts", "../../../../node_modules/@sentry/src/logger.ts", "../../../../node_modules/@sentry/src/string.ts", "../../../../node_modules/@sentry/src/object.ts", "../../../../node_modules/@sentry/src/buildPolyfills/_optionalChain.ts", "../../../../node_modules/@sentry/src/stacktrace.ts", "../../../../node_modules/@sentry/src/memo.ts", "../../../../node_modules/@sentry/src/misc.ts", "../../../../node_modules/@sentry/src/env.ts", "../../../../node_modules/@sentry/src/node.ts", "../../../../node_modules/@sentry/src/normalize.ts", "../../../../node_modules/@sentry/src/path.ts", "../../../../node_modules/@sentry/src/syncpromise.ts", "../../../../node_modules/@sentry/src/promisebuffer.ts", "../../../../node_modules/@sentry/src/time.ts", "../../../../node_modules/@sentry/src/envelope.ts", "../../../../node_modules/@sentry/src/ratelimit.ts", "../../../../node_modules/@sentry/src/session.ts", "../../../../node_modules/@sentry/src/scope.ts", "../../../../node_modules/@sentry/src/hub.ts", "../../../../node_modules/@sentry/src/api.ts", "../../../../node_modules/@sentry/src/envelope.ts", "../../../../node_modules/@sentry/src/integration.ts", "../../../../node_modules/@sentry/src/baseclient.ts", "../../../../node_modules/@sentry/src/transports/base.ts", "../../../../node_modules/toucan-js/dist/index.esm.js", "../../functions/_middleware.ts", "../../.wrangler/tmp/pages-CU0o5d/functionsRoutes-0.5195048230868211.mjs", "../../../../node_modules/path-to-regexp/src/index.ts", "../../../../node_modules/wrangler/templates/pages-template-plugin.ts"], "sourceRoot": "/home/<USER>/work/pages-plugins/pages-plugins/packages/sentry/dist/functions", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n\nimport { PolymorphicEvent, Primitive } from '@sentry/types';\n\n// eslint-disable-next-line @typescript-eslint/unbound-method\nconst objectToString = Object.prototype.toString;\n\n/**\n * Checks whether given value's type is one of a few Error or Error-like\n * {@link isError}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isError(wat: unknown): wat is Error {\n  switch (objectToString.call(wat)) {\n    case '[object Error]':\n    case '[object Exception]':\n    case '[object DOMException]':\n      return true;\n    default:\n      return isInstanceOf(wat, Error);\n  }\n}\n/**\n * Checks whether given value is an instance of the given built-in class.\n *\n * @param wat The value to be checked\n * @param className\n * @returns A boolean representing the result.\n */\nfunction isBuiltin(wat: unknown, className: string): boolean {\n  return objectToString.call(wat) === `[object ${className}]`;\n}\n\n/**\n * Checks whether given value's type is ErrorEvent\n * {@link isErrorEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isErrorEvent(wat: unknown): boolean {\n  return isBuiltin(wat, 'ErrorEvent');\n}\n\n/**\n * Checks whether given value's type is DOMError\n * {@link isDOMError}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isDOMError(wat: unknown): boolean {\n  return isBuiltin(wat, 'DOMError');\n}\n\n/**\n * Checks whether given value's type is DOMException\n * {@link isDOMException}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isDOMException(wat: unknown): boolean {\n  return isBuiltin(wat, 'DOMException');\n}\n\n/**\n * Checks whether given value's type is a string\n * {@link isString}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isString(wat: unknown): wat is string {\n  return isBuiltin(wat, 'String');\n}\n\n/**\n * Checks whether given value is a primitive (undefined, null, number, boolean, string, bigint, symbol)\n * {@link isPrimitive}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isPrimitive(wat: unknown): wat is Primitive {\n  return wat === null || (typeof wat !== 'object' && typeof wat !== 'function');\n}\n\n/**\n * Checks whether given value's type is an object literal\n * {@link isPlainObject}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isPlainObject(wat: unknown): wat is Record<string, unknown> {\n  return isBuiltin(wat, 'Object');\n}\n\n/**\n * Checks whether given value's type is an Event instance\n * {@link isEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isEvent(wat: unknown): wat is PolymorphicEvent {\n  return typeof Event !== 'undefined' && isInstanceOf(wat, Event);\n}\n\n/**\n * Checks whether given value's type is an Element instance\n * {@link isElement}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isElement(wat: unknown): boolean {\n  return typeof Element !== 'undefined' && isInstanceOf(wat, Element);\n}\n\n/**\n * Checks whether given value's type is an regexp\n * {@link isRegExp}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isRegExp(wat: unknown): wat is RegExp {\n  return isBuiltin(wat, 'RegExp');\n}\n\n/**\n * Checks whether given value has a then function.\n * @param wat A value to be checked.\n */\nexport function isThenable(wat: any): wat is PromiseLike<any> {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return Boolean(wat && wat.then && typeof wat.then === 'function');\n}\n\n/**\n * Checks whether given value's type is a SyntheticEvent\n * {@link isSyntheticEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isSyntheticEvent(wat: unknown): boolean {\n  return isPlainObject(wat) && 'nativeEvent' in wat && 'preventDefault' in wat && 'stopPropagation' in wat;\n}\n\n/**\n * Checks whether given value is NaN\n * {@link isNaN}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isNaN(wat: unknown): boolean {\n  return typeof wat === 'number' && wat !== wat;\n}\n\n/**\n * Checks whether given value's type is an instance of provided constructor.\n * {@link isInstanceOf}.\n *\n * @param wat A value to be checked.\n * @param base A constructor to be used in a check.\n * @returns A boolean representing the result.\n */\nexport function isInstanceOf(wat: any, base: any): boolean {\n  try {\n    return wat instanceof base;\n  } catch (_e) {\n    return false;\n  }\n}\n", "/**\n * NOTE: In order to avoid circular dependencies, if you add a function to this module and it needs to print something,\n * you must either a) use `console.log` rather than the logger, or b) put your function elsewhere.\n *\n * Note: This file was originally called `global.ts`, but was changed to unblock users which might be doing\n * string replaces with bundlers like Vite for `global` (would break imports that rely on importing from utils/src/global).\n *\n * Why worldwide?\n *\n * Why not?\n */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport { Integration } from '@sentry/types';\n\n/** Internal global with common properties and Sentry extensions  */\nexport interface InternalGlobal {\n  navigator?: { userAgent?: string };\n  console: Console;\n  Sentry?: {\n    Integrations?: Integration[];\n  };\n  SENTRY_ENVIRONMENT?: string;\n  SENTRY_DSN?: string;\n  SENTRY_RELEASE?: {\n    id?: string;\n  };\n  __SENTRY__: {\n    globalEventProcessors: any;\n    hub: any;\n    logger: any;\n    extensions?: {\n      /** Extension methods for the hub, which are bound to the current Hub instance */\n      // eslint-disable-next-line @typescript-eslint/ban-types\n      [key: string]: Function;\n    };\n  };\n}\n\n// The code below for 'isGlobalObj' and 'GLOBAL_OBJ' was copied from core-js before modification\n// https://github.com/zloirock/core-js/blob/1b944df55282cdc99c90db5f49eb0b6eda2cc0a3/packages/core-js/internals/global.js\n// core-js has the following licence:\n//\n// Copyright (c) 2014-2022 Denis Pushkarev\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n/** Returns 'obj' if it's the global object, otherwise returns undefined */\nfunction isGlobalObj(obj: { Math?: Math }): any | undefined {\n  return obj && obj.Math == Math ? obj : undefined;\n}\n\n/** Get's the global object for the current JavaScript runtime */\nexport const GLOBAL_OBJ: InternalGlobal =\n  (typeof globalThis == 'object' && isGlobalObj(globalThis)) ||\n  // eslint-disable-next-line no-restricted-globals\n  (typeof window == 'object' && isGlobalObj(window)) ||\n  (typeof self == 'object' && isGlobalObj(self)) ||\n  (typeof global == 'object' && isGlobalObj(global)) ||\n  (function (this: any) {\n    return this;\n  })() ||\n  {};\n\n/**\n * @deprecated Use GLOBAL_OBJ instead or WINDOW from @sentry/browser. This will be removed in v8\n */\nexport function getGlobalObject<T>(): T & InternalGlobal {\n  return GLOBAL_OBJ as T & InternalGlobal;\n}\n\n/**\n * Returns a global singleton contained in the global `__SENTRY__` object.\n *\n * If the singleton doesn't already exist in `__SENTRY__`, it will be created using the given factory\n * function and added to the `__SENTRY__` object.\n *\n * @param name name of the global singleton on __SENTRY__\n * @param creator creator Factory function to create the singleton if it doesn't already exist on `__SENTRY__`\n * @param obj (Optional) The global object on which to look for `__SENTRY__`, if not `GLOBAL_OBJ`'s return value\n * @returns the singleton\n */\nexport function getGlobalSingleton<T>(name: keyof InternalGlobal['__SENTRY__'], creator: () => T, obj?: unknown): T {\n  const gbl = (obj || GLOBAL_OBJ) as InternalGlobal;\n  const __SENTRY__ = (gbl.__SENTRY__ = gbl.__SENTRY__ || {});\n  const singleton = __SENTRY__[name] || (__SENTRY__[name] = creator());\n  return singleton;\n}\n", "import { isString } from './is';\nimport { getGlobalObject } from './worldwide';\n\n// eslint-disable-next-line deprecation/deprecation\nconst WINDOW = getGlobalObject<Window>();\n\n/**\n * Given a child DOM element, returns a query-selector statement describing that\n * and its ancestors\n * e.g. [HTMLElement] => body > div > input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nexport function htmlTreeAsString(elem: unknown, keyAttrs?: string[]): string {\n  type SimpleNode = {\n    parentNode: SimpleNode;\n  } | null;\n\n  // try/catch both:\n  // - accessing event.target (see getsentry/raven-js#838, #768)\n  // - `htmlTreeAsString` because it's complex, and just accessing the DOM incorrectly\n  // - can throw an exception in some circumstances.\n  try {\n    let currentElem = elem as SimpleNode;\n    const MAX_TRAVERSE_HEIGHT = 5;\n    const MAX_OUTPUT_LEN = 80;\n    const out = [];\n    let height = 0;\n    let len = 0;\n    const separator = ' > ';\n    const sepLength = separator.length;\n    let nextStr;\n\n    while (currentElem && height++ < MAX_TRAVERSE_HEIGHT) {\n      nextStr = _htmlElementAsString(currentElem, keyAttrs);\n      // bail out if\n      // - nextStr is the 'html' element\n      // - the length of the string that would be created exceeds MAX_OUTPUT_LEN\n      //   (ignore this limit if we are on the first iteration)\n      if (nextStr === 'html' || (height > 1 && len + out.length * sepLength + nextStr.length >= MAX_OUTPUT_LEN)) {\n        break;\n      }\n\n      out.push(nextStr);\n\n      len += nextStr.length;\n      currentElem = currentElem.parentNode;\n    }\n\n    return out.reverse().join(separator);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n\n/**\n * Returns a simple, query-selector representation of a DOM element\n * e.g. [HTMLElement] => input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nfunction _htmlElementAsString(el: unknown, keyAttrs?: string[]): string {\n  const elem = el as {\n    tagName?: string;\n    id?: string;\n    className?: string;\n    getAttribute(key: string): string;\n  };\n\n  const out = [];\n  let className;\n  let classes;\n  let key;\n  let attr;\n  let i;\n\n  if (!elem || !elem.tagName) {\n    return '';\n  }\n\n  out.push(elem.tagName.toLowerCase());\n\n  // Pairs of attribute keys defined in `serializeAttribute` and their values on element.\n  const keyAttrPairs =\n    keyAttrs && keyAttrs.length\n      ? keyAttrs.filter(keyAttr => elem.getAttribute(keyAttr)).map(keyAttr => [keyAttr, elem.getAttribute(keyAttr)])\n      : null;\n\n  if (keyAttrPairs && keyAttrPairs.length) {\n    keyAttrPairs.forEach(keyAttrPair => {\n      out.push(`[${keyAttrPair[0]}=\"${keyAttrPair[1]}\"]`);\n    });\n  } else {\n    if (elem.id) {\n      out.push(`#${elem.id}`);\n    }\n\n    // eslint-disable-next-line prefer-const\n    className = elem.className;\n    if (className && isString(className)) {\n      classes = className.split(/\\s+/);\n      for (i = 0; i < classes.length; i++) {\n        out.push(`.${classes[i]}`);\n      }\n    }\n  }\n  const allowedAttrs = ['type', 'name', 'title', 'alt'];\n  for (i = 0; i < allowedAttrs.length; i++) {\n    key = allowedAttrs[i];\n    attr = elem.getAttribute(key);\n    if (attr) {\n      out.push(`[${key}=\"${attr}\"]`);\n    }\n  }\n  return out.join('');\n}\n\n/**\n * A safe form of location.href\n */\nexport function getLocationHref(): string {\n  try {\n    return WINDOW.document.location.href;\n  } catch (oO) {\n    return '';\n  }\n}\n\n/**\n * Gets a DOM element by using document.querySelector.\n *\n * This wrapper will first check for the existance of the function before\n * actually calling it so that we don't have to take care of this check,\n * every time we want to access the DOM.\n *\n * Reason: DOM/querySelector is not available in all environments.\n *\n * We have to cast to any because utils can be consumed by a variety of environments,\n * and we don't want to break TS users. If you know what element will be selected by\n * `document.querySelector`, specify it as part of the generic call. For example,\n * `const element = getDomElement<Element>('selector');`\n *\n * @param selector the selector string passed on to document.querySelector\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function getDomElement<E = any>(selector: string): E | null {\n  if (WINDOW.document && WINDOW.document.querySelector) {\n    return WINDOW.document.querySelector(selector) as unknown as E;\n  }\n  return null;\n}\n", "import type { ConsoleLevel } from './logger';\n\n/** An error emitted by Sentry SDKs and related utilities. */\nexport class SentryError extends Error {\n  /** Display name of this error instance. */\n  public name: string;\n\n  public logLevel: ConsoleLevel;\n\n  public constructor(public message: string, logLevel: ConsoleLevel = 'warn') {\n    super(message);\n\n    this.name = new.target.prototype.constructor.name;\n    // This sets the prototype to be `Error`, not `SentryError`. It's unclear why we do this, but commenting this line\n    // out causes various (seemingly totally unrelated) playwright tests consistently time out. FYI, this makes\n    // instances of `SentryError` fail `obj instanceof SentryError` checks.\n    Object.setPrototypeOf(this, new.target.prototype);\n    this.logLevel = logLevel;\n  }\n}\n", "import { DsnCom<PERSON>, DsnLike, DsnProtocol } from '@sentry/types';\n\nimport { SentryError } from './error';\n\n/** Regular expression used to parse a Dsn. */\nconst DSN_REGEX = /^(?:(\\w+):)\\/\\/(?:(\\w+)(?::(\\w+)?)?@)([\\w.-]+)(?::(\\d+))?\\/(.+)/;\n\nfunction isValidProtocol(protocol?: string): protocol is DsnProtocol {\n  return protocol === 'http' || protocol === 'https';\n}\n\n/**\n * Renders the string representation of this Dsn.\n *\n * By default, this will render the public representation without the password\n * component. To get the deprecated private representation, set `withPassword`\n * to true.\n *\n * @param withPassword When set to true, the password will be included.\n */\nexport function dsnToString(dsn: DsnComponents, withPassword: boolean = false): string {\n  const { host, path, pass, port, projectId, protocol, publicKey } = dsn;\n  return (\n    `${protocol}://${publicKey}${withPassword && pass ? `:${pass}` : ''}` +\n    `@${host}${port ? `:${port}` : ''}/${path ? `${path}/` : path}${projectId}`\n  );\n}\n\n/**\n * Parses a Dsn from a given string.\n *\n * @param str A Dsn as string\n * @returns Dsn as DsnComponents\n */\nexport function dsnFromString(str: string): DsnComponents {\n  const match = DSN_REGEX.exec(str);\n\n  if (!match) {\n    throw new SentryError(`Invalid Sentry Dsn: ${str}`);\n  }\n\n  const [protocol, publicKey, pass = '', host, port = '', lastPath] = match.slice(1);\n  let path = '';\n  let projectId = lastPath;\n\n  const split = projectId.split('/');\n  if (split.length > 1) {\n    path = split.slice(0, -1).join('/');\n    projectId = split.pop() as string;\n  }\n\n  if (projectId) {\n    const projectMatch = projectId.match(/^\\d+/);\n    if (projectMatch) {\n      projectId = projectMatch[0];\n    }\n  }\n\n  return dsnFromComponents({ host, pass, path, projectId, port, protocol: protocol as DsnProtocol, publicKey });\n}\n\nfunction dsnFromComponents(components: DsnComponents): DsnComponents {\n  return {\n    protocol: components.protocol,\n    publicKey: components.publicKey || '',\n    pass: components.pass || '',\n    host: components.host,\n    port: components.port || '',\n    path: components.path || '',\n    projectId: components.projectId,\n  };\n}\n\nfunction validateDsn(dsn: DsnComponents): boolean | void {\n  if (!__DEBUG_BUILD__) {\n    return;\n  }\n\n  const { port, projectId, protocol } = dsn;\n\n  const requiredComponents: ReadonlyArray<keyof DsnComponents> = ['protocol', 'publicKey', 'host', 'projectId'];\n  requiredComponents.forEach(component => {\n    if (!dsn[component]) {\n      throw new SentryError(`Invalid Sentry Dsn: ${component} missing`);\n    }\n  });\n\n  if (!projectId.match(/^\\d+$/)) {\n    throw new SentryError(`Invalid Sentry Dsn: Invalid projectId ${projectId}`);\n  }\n\n  if (!isValidProtocol(protocol)) {\n    throw new SentryError(`Invalid Sentry Dsn: Invalid protocol ${protocol}`);\n  }\n\n  if (port && isNaN(parseInt(port, 10))) {\n    throw new SentryError(`Invalid Sentry Dsn: Invalid port ${port}`);\n  }\n\n  return true;\n}\n\n/** The Sentry Dsn, identifying a Sentry instance and project. */\nexport function makeDsn(from: DsnLike): DsnComponents {\n  const components = typeof from === 'string' ? dsnFromString(from) : dsnFromComponents(from);\n  validateDsn(components);\n  return components;\n}\n", "import { WrappedFunction } from '@sentry/types';\n\nimport { getGlobalSingleton, GLOBAL_OBJ } from './worldwide';\n\n/** Prefix for logging strings */\nconst PREFIX = 'Sentry Logger ';\n\nexport const CONSOLE_LEVELS = ['debug', 'info', 'warn', 'error', 'log', 'assert', 'trace'] as const;\nexport type ConsoleLevel = typeof CONSOLE_LEVELS[number];\n\ntype LoggerMethod = (...args: unknown[]) => void;\ntype LoggerConsoleMethods = Record<typeof CONSOLE_LEVELS[number], LoggerMethod>;\n\n/** JSDoc */\ninterface Logger extends LoggerConsoleMethods {\n  disable(): void;\n  enable(): void;\n}\n\n/**\n * Temporarily disable sentry console instrumentations.\n *\n * @param callback The function to run against the original `console` messages\n * @returns The results of the callback\n */\nexport function consoleSandbox<T>(callback: () => T): T {\n  if (!('console' in GLOBAL_OBJ)) {\n    return callback();\n  }\n\n  const originalConsole = GLOBAL_OBJ.console as Console & Record<string, unknown>;\n  const wrappedLevels: Partial<LoggerConsoleMethods> = {};\n\n  // Restore all wrapped console methods\n  CONSOLE_LEVELS.forEach(level => {\n    // TODO(v7): Remove this check as it's only needed for Node 6\n    const originalWrappedFunc =\n      originalConsole[level] && (originalConsole[level] as WrappedFunction).__sentry_original__;\n    if (level in originalConsole && originalWrappedFunc) {\n      wrappedLevels[level] = originalConsole[level] as LoggerConsoleMethods[typeof level];\n      originalConsole[level] = originalWrappedFunc as Console[typeof level];\n    }\n  });\n\n  try {\n    return callback();\n  } finally {\n    // Revert restoration to wrapped state\n    Object.keys(wrappedLevels).forEach(level => {\n      originalConsole[level] = wrappedLevels[level as typeof CONSOLE_LEVELS[number]];\n    });\n  }\n}\n\nfunction makeLogger(): Logger {\n  let enabled = false;\n  const logger: Partial<Logger> = {\n    enable: () => {\n      enabled = true;\n    },\n    disable: () => {\n      enabled = false;\n    },\n  };\n\n  if (__DEBUG_BUILD__) {\n    CONSOLE_LEVELS.forEach(name => {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      logger[name] = (...args: any[]) => {\n        if (enabled) {\n          consoleSandbox(() => {\n            GLOBAL_OBJ.console[name](`${PREFIX}[${name}]:`, ...args);\n          });\n        }\n      };\n    });\n  } else {\n    CONSOLE_LEVELS.forEach(name => {\n      logger[name] = () => undefined;\n    });\n  }\n\n  return logger as Logger;\n}\n\n// Ensure we only have a single logger instance, even if multiple versions of @sentry/utils are being used\nlet logger: Logger;\nif (__DEBUG_BUILD__) {\n  logger = getGlobalSingleton('logger', makeLogger);\n} else {\n  logger = makeLogger();\n}\n\nexport { logger };\n", "import { isRegExp, isString } from './is';\n\n/**\n * Truncates given string to the maximum characters count\n *\n * @param str An object that contains serializable values\n * @param max Maximum number of characters in truncated string (0 = unlimited)\n * @returns string Encoded\n */\nexport function truncate(str: string, max: number = 0): string {\n  if (typeof str !== 'string' || max === 0) {\n    return str;\n  }\n  return str.length <= max ? str : `${str.substr(0, max)}...`;\n}\n\n/**\n * This is basically just `trim_line` from\n * https://github.com/getsentry/sentry/blob/master/src/sentry/lang/javascript/processor.py#L67\n *\n * @param str An object that contains serializable values\n * @param max Maximum number of characters in truncated string\n * @returns string Encoded\n */\nexport function snipLine(line: string, colno: number): string {\n  let newLine = line;\n  const lineLength = newLine.length;\n  if (lineLength <= 150) {\n    return newLine;\n  }\n  if (colno > lineLength) {\n    // eslint-disable-next-line no-param-reassign\n    colno = lineLength;\n  }\n\n  let start = Math.max(colno - 60, 0);\n  if (start < 5) {\n    start = 0;\n  }\n\n  let end = Math.min(start + 140, lineLength);\n  if (end > lineLength - 5) {\n    end = lineLength;\n  }\n  if (end === lineLength) {\n    start = Math.max(end - 140, 0);\n  }\n\n  newLine = newLine.slice(start, end);\n  if (start > 0) {\n    newLine = `'{snip} ${newLine}`;\n  }\n  if (end < lineLength) {\n    newLine += ' {snip}';\n  }\n\n  return newLine;\n}\n\n/**\n * Join values in array\n * @param input array of values to be joined together\n * @param delimiter string to be placed in-between values\n * @returns Joined values\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function safeJoin(input: any[], delimiter?: string): string {\n  if (!Array.isArray(input)) {\n    return '';\n  }\n\n  const output = [];\n  // eslint-disable-next-line @typescript-eslint/prefer-for-of\n  for (let i = 0; i < input.length; i++) {\n    const value = input[i];\n    try {\n      output.push(String(value));\n    } catch (e) {\n      output.push('[value cannot be serialized]');\n    }\n  }\n\n  return output.join(delimiter);\n}\n\n/**\n * Checks if the given value matches a regex or string\n *\n * @param value The string to test\n * @param pattern Either a regex or a string against which `value` will be matched\n * @param requireExactStringMatch If true, `value` must match `pattern` exactly. If false, `value` will match\n * `pattern` if it contains `pattern`. Only applies to string-type patterns.\n */\nexport function isMatchingPattern(\n  value: string,\n  pattern: RegExp | string,\n  requireExactStringMatch: boolean = false,\n): boolean {\n  if (!isString(value)) {\n    return false;\n  }\n\n  if (isRegExp(pattern)) {\n    return pattern.test(value);\n  }\n  if (isString(pattern)) {\n    return requireExactStringMatch ? value === pattern : value.includes(pattern);\n  }\n\n  return false;\n}\n\n/**\n * Test the given string against an array of strings and regexes. By default, string matching is done on a\n * substring-inclusion basis rather than a strict equality basis\n *\n * @param testString The string to test\n * @param patterns The patterns against which to test the string\n * @param requireExactStringMatch If true, `testString` must match one of the given string patterns exactly in order to\n * count. If false, `testString` will match a string pattern if it contains that pattern.\n * @returns\n */\nexport function stringMatchesSomePattern(\n  testString: string,\n  patterns: Array<string | RegExp> = [],\n  requireExactStringMatch: boolean = false,\n): boolean {\n  return patterns.some(pattern => isMatchingPattern(testString, pattern, requireExactStringMatch));\n}\n\n/**\n * Given a string, escape characters which have meaning in the regex grammar, such that the result is safe to feed to\n * `new RegExp()`.\n *\n * Based on https://github.com/sindresorhus/escape-string-regexp. Vendored to a) reduce the size by skipping the runtime\n * type-checking, and b) ensure it gets down-compiled for old versions of Node (the published package only supports Node\n * 12+).\n *\n * @param regexString The string to escape\n * @returns An version of the string with all special regex characters escaped\n */\nexport function escapeStringForRegex(regexString: string): string {\n  // escape the hyphen separately so we can also replace it with a unicode literal hyphen, to avoid the problems\n  // discussed in https://github.com/sindresorhus/escape-string-regexp/issues/20.\n  return regexString.replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&').replace(/-/g, '\\\\x2d');\n}\n", "/* eslint-disable max-lines */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { WrappedFunction } from '@sentry/types';\n\nimport { htmlTreeAsString } from './browser';\nimport { isElement, isError, isEvent, isInstanceOf, isPlainObject, isPrimitive } from './is';\nimport { truncate } from './string';\n\n/**\n * Replace a method in an object with a wrapped version of itself.\n *\n * @param source An object that contains a method to be wrapped.\n * @param name The name of the method to be wrapped.\n * @param replacementFactory A higher-order function that takes the original version of the given method and returns a\n * wrapped version. Note: The function returned by `replacementFactory` needs to be a non-arrow function, in order to\n * preserve the correct value of `this`, and the original method must be called using `origMethod.call(this, <other\n * args>)` or `origMethod.apply(this, [<other args>])` (rather than being called directly), again to preserve `this`.\n * @returns void\n */\nexport function fill(source: { [key: string]: any }, name: string, replacementFactory: (...args: any[]) => any): void {\n  if (!(name in source)) {\n    return;\n  }\n\n  const original = source[name] as () => any;\n  const wrapped = replacementFactory(original) as WrappedFunction;\n\n  // Make sure it's a function first, as we need to attach an empty prototype for `defineProperties` to work\n  // otherwise it'll throw \"TypeError: Object.defineProperties called on non-object\"\n  if (typeof wrapped === 'function') {\n    try {\n      markFunctionWrapped(wrapped, original);\n    } catch (_Oo) {\n      // This can throw if multiple fill happens on a global object like XMLHttpRequest\n      // Fixes https://github.com/getsentry/sentry-javascript/issues/2043\n    }\n  }\n\n  source[name] = wrapped;\n}\n\n/**\n * Defines a non-enumerable property on the given object.\n *\n * @param obj The object on which to set the property\n * @param name The name of the property to be set\n * @param value The value to which to set the property\n */\nexport function addNonEnumerableProperty(obj: { [key: string]: unknown }, name: string, value: unknown): void {\n  Object.defineProperty(obj, name, {\n    // enumerable: false, // the default, so we can save on bundle size by not explicitly setting it\n    value: value,\n    writable: true,\n    configurable: true,\n  });\n}\n\n/**\n * Remembers the original function on the wrapped function and\n * patches up the prototype.\n *\n * @param wrapped the wrapper function\n * @param original the original function that gets wrapped\n */\nexport function markFunctionWrapped(wrapped: WrappedFunction, original: WrappedFunction): void {\n  const proto = original.prototype || {};\n  wrapped.prototype = original.prototype = proto;\n  addNonEnumerableProperty(wrapped, '__sentry_original__', original);\n}\n\n/**\n * This extracts the original function if available.  See\n * `markFunctionWrapped` for more information.\n *\n * @param func the function to unwrap\n * @returns the unwrapped version of the function if available.\n */\nexport function getOriginalFunction(func: WrappedFunction): WrappedFunction | undefined {\n  return func.__sentry_original__;\n}\n\n/**\n * Encodes given object into url-friendly format\n *\n * @param object An object that contains serializable values\n * @returns string Encoded\n */\nexport function urlEncode(object: { [key: string]: any }): string {\n  return Object.keys(object)\n    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(object[key])}`)\n    .join('&');\n}\n\n/**\n * Transforms any `Error` or `Event` into a plain object with all of their enumerable properties, and some of their\n * non-enumerable properties attached.\n *\n * @param value Initial source that we have to transform in order for it to be usable by the serializer\n * @returns An Event or Error turned into an object - or the value argurment itself, when value is neither an Event nor\n *  an Error.\n */\nexport function convertToPlainObject<V extends unknown>(\n  value: V,\n):\n  | {\n      [ownProps: string]: unknown;\n      type: string;\n      target: string;\n      currentTarget: string;\n      detail?: unknown;\n    }\n  | {\n      [ownProps: string]: unknown;\n      message: string;\n      name: string;\n      stack?: string;\n    }\n  | V {\n  if (isError(value)) {\n    return {\n      message: value.message,\n      name: value.name,\n      stack: value.stack,\n      ...getOwnProperties(value),\n    };\n  } else if (isEvent(value)) {\n    const newObj: {\n      [ownProps: string]: unknown;\n      type: string;\n      target: string;\n      currentTarget: string;\n      detail?: unknown;\n    } = {\n      type: value.type,\n      target: serializeEventTarget(value.target),\n      currentTarget: serializeEventTarget(value.currentTarget),\n      ...getOwnProperties(value),\n    };\n\n    if (typeof CustomEvent !== 'undefined' && isInstanceOf(value, CustomEvent)) {\n      newObj.detail = value.detail;\n    }\n\n    return newObj;\n  } else {\n    return value;\n  }\n}\n\n/** Creates a string representation of the target of an `Event` object */\nfunction serializeEventTarget(target: unknown): string {\n  try {\n    return isElement(target) ? htmlTreeAsString(target) : Object.prototype.toString.call(target);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n\n/** Filters out all but an object's own properties */\nfunction getOwnProperties(obj: unknown): { [key: string]: unknown } {\n  if (typeof obj === 'object' && obj !== null) {\n    const extractedProps: { [key: string]: unknown } = {};\n    for (const property in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, property)) {\n        extractedProps[property] = (obj as Record<string, unknown>)[property];\n      }\n    }\n    return extractedProps;\n  } else {\n    return {};\n  }\n}\n\n/**\n * Given any captured exception, extract its keys and create a sorted\n * and truncated list that will be used inside the event message.\n * eg. `Non-error exception captured with keys: foo, bar, baz`\n */\nexport function extractExceptionKeysForMessage(exception: Record<string, unknown>, maxLength: number = 40): string {\n  const keys = Object.keys(convertToPlainObject(exception));\n  keys.sort();\n\n  if (!keys.length) {\n    return '[object has no keys]';\n  }\n\n  if (keys[0].length >= maxLength) {\n    return truncate(keys[0], maxLength);\n  }\n\n  for (let includedKeys = keys.length; includedKeys > 0; includedKeys--) {\n    const serialized = keys.slice(0, includedKeys).join(', ');\n    if (serialized.length > maxLength) {\n      continue;\n    }\n    if (includedKeys === keys.length) {\n      return serialized;\n    }\n    return truncate(serialized, maxLength);\n  }\n\n  return '';\n}\n\n/**\n * Given any object, return a new object having removed all fields whose value was `undefined`.\n * Works recursively on objects and arrays.\n *\n * Attention: This function keeps circular references in the returned object.\n */\nexport function dropUndefinedKeys<T>(inputValue: T): T {\n  // This map keeps track of what already visited nodes map to.\n  // Our Set - based memoBuilder doesn't work here because we want to the output object to have the same circular\n  // references as the input object.\n  const memoizationMap = new Map<unknown, unknown>();\n\n  // This function just proxies `_dropUndefinedKeys` to keep the `memoBuilder` out of this function's API\n  return _dropUndefinedKeys(inputValue, memoizationMap);\n}\n\nfunction _dropUndefinedKeys<T>(inputValue: T, memoizationMap: Map<unknown, unknown>): T {\n  if (isPlainObject(inputValue)) {\n    // If this node has already been visited due to a circular reference, return the object it was mapped to in the new object\n    const memoVal = memoizationMap.get(inputValue);\n    if (memoVal !== undefined) {\n      return memoVal as T;\n    }\n\n    const returnValue: { [key: string]: any } = {};\n    // Store the mapping of this value in case we visit it again, in case of circular data\n    memoizationMap.set(inputValue, returnValue);\n\n    for (const key of Object.keys(inputValue)) {\n      if (typeof inputValue[key] !== 'undefined') {\n        returnValue[key] = _dropUndefinedKeys(inputValue[key], memoizationMap);\n      }\n    }\n\n    return returnValue as T;\n  }\n\n  if (Array.isArray(inputValue)) {\n    // If this node has already been visited due to a circular reference, return the array it was mapped to in the new object\n    const memoVal = memoizationMap.get(inputValue);\n    if (memoVal !== undefined) {\n      return memoVal as T;\n    }\n\n    const returnValue: unknown[] = [];\n    // Store the mapping of this value in case we visit it again, in case of circular data\n    memoizationMap.set(inputValue, returnValue);\n\n    inputValue.forEach((item: unknown) => {\n      returnValue.push(_dropUndefinedKeys(item, memoizationMap));\n    });\n\n    return returnValue as unknown as T;\n  }\n\n  return inputValue;\n}\n\n/**\n * Ensure that something is an object.\n *\n * Turns `undefined` and `null` into `String`s and all other primitives into instances of their respective wrapper\n * classes (String, Boolean, Number, etc.). Acts as the identity function on non-primitives.\n *\n * @param wat The subject of the objectification\n * @returns A version of `wat` which can safely be used with `Object` class methods\n */\nexport function objectify(wat: unknown): typeof Object {\n  let objectified;\n  switch (true) {\n    case wat === undefined || wat === null:\n      objectified = new String(wat);\n      break;\n\n    // Though symbols and bigints do have wrapper classes (`Symbol` and `BigInt`, respectively), for whatever reason\n    // those classes don't have constructors which can be used with the `new` keyword. We therefore need to cast each as\n    // an object in order to wrap it.\n    case typeof wat === 'symbol' || typeof wat === 'bigint':\n      objectified = Object(wat);\n      break;\n\n    // this will catch the remaining primitives: `String`, `Number`, and `Boolean`\n    case isPrimitive(wat):\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      objectified = new (wat as any).constructor(wat);\n      break;\n\n    // by process of elimination, at this point we know that `wat` must already be an object\n    default:\n      objectified = wat;\n      break;\n  }\n  return objectified;\n}\n", "import { GenericFunction } from './types';\n\n/**\n * Polyfill for the optional chain operator, `?.`, given previous conversion of the expression into an array of values,\n * descriptors, and functions.\n *\n * Adapted from Sucrase (https://github.com/alangpierce/sucrase)\n * See https://github.com/alangpierce/sucrase/blob/265887868966917f3b924ce38dfad01fbab1329f/src/transformers/OptionalChainingNullishTransformer.ts#L15\n *\n * @param ops Array result of expression conversion\n * @returns The value of the expression\n */\nexport function _optionalChain(ops: unknown[]): unknown {\n  let lastAccessLHS: unknown = undefined;\n  let value = ops[0];\n  let i = 1;\n  while (i < ops.length) {\n    const op = ops[i] as string;\n    const fn = ops[i + 1] as (intermediateValue: unknown) => unknown;\n    i += 2;\n    // by checking for loose equality to `null`, we catch both `null` and `undefined`\n    if ((op === 'optionalAccess' || op === 'optionalCall') && value == null) {\n      // really we're meaning to return `undefined` as an actual value here, but it saves bytes not to write it\n      return;\n    }\n    if (op === 'access' || op === 'optionalAccess') {\n      lastAccessLHS = value;\n      value = fn(value);\n    } else if (op === 'call' || op === 'optionalCall') {\n      value = fn((...args: unknown[]) => (value as GenericFunction).call(lastAccessLHS, ...args));\n      lastAccessLHS = undefined;\n    }\n  }\n  return value;\n}\n\n// Sucrase version\n// function _optionalChain(ops) {\n//   let lastAccessLHS = undefined;\n//   let value = ops[0];\n//   let i = 1;\n//   while (i < ops.length) {\n//     const op = ops[i];\n//     const fn = ops[i + 1];\n//     i += 2;\n//     if ((op === 'optionalAccess' || op === 'optionalCall') && value == null) {\n//       return undefined;\n//     }\n//     if (op === 'access' || op === 'optionalAccess') {\n//       lastAccessLHS = value;\n//       value = fn(value);\n//     } else if (op === 'call' || op === 'optionalCall') {\n//       value = fn((...args) => value.call(lastAccessLHS, ...args));\n//       lastAccessLHS = undefined;\n//     }\n//   }\n//   return value;\n// }\n", "import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ack<PERSON>inePars<PERSON>, StackLineParserFn, StackParser } from '@sentry/types';\n\nconst STACKTRACE_LIMIT = 50;\n\n/**\n * Creates a stack parser with the supplied line parsers\n *\n * StackFrames are returned in the correct order for Sentry Exception\n * frames and with Sentry SDK internal frames removed from the top and bottom\n *\n */\nexport function createStackParser(...parsers: StackLineParser[]): StackParser {\n  const sortedParsers = parsers.sort((a, b) => a[0] - b[0]).map(p => p[1]);\n\n  return (stack: string, skipFirst: number = 0): StackFrame[] => {\n    const frames: StackFrame[] = [];\n\n    for (const line of stack.split('\\n').slice(skipFirst)) {\n      // https://github.com/getsentry/sentry-javascript/issues/5459\n      // Remove webpack (error: *) wrappers\n      const cleanedLine = line.replace(/\\(error: (.*)\\)/, '$1');\n\n      for (const parser of sortedParsers) {\n        const frame = parser(cleanedLine);\n\n        if (frame) {\n          frames.push(frame);\n          break;\n        }\n      }\n    }\n\n    return stripSentryFramesAndReverse(frames);\n  };\n}\n\n/**\n * Gets a stack parser implementation from Options.stackParser\n * @see Options\n *\n * If options contains an array of line parsers, it is converted into a parser\n */\nexport function stackParserFromStackParserOptions(stackParser: StackParser | StackLineParser[]): StackParser {\n  if (Array.isArray(stackParser)) {\n    return createStackParser(...stackParser);\n  }\n  return stackParser;\n}\n\n/**\n * @hidden\n */\nexport function stripSentryFramesAndReverse(stack: StackFrame[]): StackFrame[] {\n  if (!stack.length) {\n    return [];\n  }\n\n  let localStack = stack;\n\n  const firstFrameFunction = localStack[0].function || '';\n  const lastFrameFunction = localStack[localStack.length - 1].function || '';\n\n  // If stack starts with one of our API calls, remove it (starts, meaning it's the top of the stack - aka last call)\n  if (firstFrameFunction.indexOf('captureMessage') !== -1 || firstFrameFunction.indexOf('captureException') !== -1) {\n    localStack = localStack.slice(1);\n  }\n\n  // If stack ends with one of our internal API calls, remove it (ends, meaning it's the bottom of the stack - aka top-most call)\n  if (lastFrameFunction.indexOf('sentryWrapped') !== -1) {\n    localStack = localStack.slice(0, -1);\n  }\n\n  // The frame where the crash happened, should be the last entry in the array\n  return localStack\n    .slice(0, STACKTRACE_LIMIT)\n    .map(frame => ({\n      ...frame,\n      filename: frame.filename || localStack[0].filename,\n      function: frame.function || '?',\n    }))\n    .reverse();\n}\n\nconst defaultFunctionName = '<anonymous>';\n\n/**\n * Safely extract function name from itself\n */\nexport function getFunctionName(fn: unknown): string {\n  try {\n    if (!fn || typeof fn !== 'function') {\n      return defaultFunctionName;\n    }\n    return fn.name || defaultFunctionName;\n  } catch (e) {\n    // Just accessing custom props in some Selenium environments\n    // can cause a \"Permission denied\" exception (see raven-js#495).\n    return defaultFunctionName;\n  }\n}\n\ntype GetModuleFn = (filename: string | undefined) => string | undefined;\n\n// eslint-disable-next-line complexity\nfunction node(getModule?: GetModuleFn): StackLineParserFn {\n  const FILENAME_MATCH = /^\\s*[-]{4,}$/;\n  const FULL_MATCH = /at (?:async )?(?:(.+?)\\s+\\()?(?:(.+):(\\d+):(\\d+)?|([^)]+))\\)?/;\n\n  // eslint-disable-next-line complexity\n  return (line: string) => {\n    if (line.match(FILENAME_MATCH)) {\n      return {\n        filename: line,\n      };\n    }\n\n    const lineMatch = line.match(FULL_MATCH);\n    if (!lineMatch) {\n      return undefined;\n    }\n\n    let object: string | undefined;\n    let method: string | undefined;\n    let functionName: string | undefined;\n    let typeName: string | undefined;\n    let methodName: string | undefined;\n\n    if (lineMatch[1]) {\n      functionName = lineMatch[1];\n\n      let methodStart = functionName.lastIndexOf('.');\n      if (functionName[methodStart - 1] === '.') {\n        methodStart--;\n      }\n\n      if (methodStart > 0) {\n        object = functionName.substr(0, methodStart);\n        method = functionName.substr(methodStart + 1);\n        const objectEnd = object.indexOf('.Module');\n        if (objectEnd > 0) {\n          functionName = functionName.substr(objectEnd + 1);\n          object = object.substr(0, objectEnd);\n        }\n      }\n      typeName = undefined;\n    }\n\n    if (method) {\n      typeName = object;\n      methodName = method;\n    }\n\n    if (method === '<anonymous>') {\n      methodName = undefined;\n      functionName = undefined;\n    }\n\n    if (functionName === undefined) {\n      methodName = methodName || '<anonymous>';\n      functionName = typeName ? `${typeName}.${methodName}` : methodName;\n    }\n\n    const filename = lineMatch[2]?.startsWith('file://') ? lineMatch[2].substr(7) : lineMatch[2];\n    const isNative = lineMatch[5] === 'native';\n    const isInternal =\n      isNative || (filename && !filename.startsWith('/') && !filename.startsWith('.') && filename.indexOf(':\\\\') !== 1);\n\n    // in_app is all that's not an internal Node function or a module within node_modules\n    // note that isNative appears to return true even for node core libraries\n    // see https://github.com/getsentry/raven-node/issues/176\n    const in_app = !isInternal && filename !== undefined && !filename.includes('node_modules/');\n\n    return {\n      filename,\n      module: getModule?.(filename),\n      function: functionName,\n      lineno: parseInt(lineMatch[3], 10) || undefined,\n      colno: parseInt(lineMatch[4], 10) || undefined,\n      in_app,\n    };\n  };\n}\n\n/**\n * Node.js stack line parser\n *\n * This is in @sentry/utils so it can be used from the Electron SDK in the browser for when `nodeIntegration == true`.\n * This allows it to be used without referencing or importing any node specific code which causes bundlers to complain\n */\nexport function nodeStackLineParser(getModule?: GetModuleFn): StackLineParser {\n  return [90, node(getModule)];\n}\n", "/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\nexport type MemoFunc = [\n  // memoize\n  (obj: any) => boolean,\n  // unmemoize\n  (obj: any) => void,\n];\n\n/**\n * Helper to decycle json objects\n */\nexport function memoBuilder(): MemoFunc {\n  const hasWeakSet = typeof WeakSet === 'function';\n  const inner: any = hasWeakSet ? new WeakSet() : [];\n  function memoize(obj: any): boolean {\n    if (hasWeakSet) {\n      if (inner.has(obj)) {\n        return true;\n      }\n      inner.add(obj);\n      return false;\n    }\n    // eslint-disable-next-line @typescript-eslint/prefer-for-of\n    for (let i = 0; i < inner.length; i++) {\n      const value = inner[i];\n      if (value === obj) {\n        return true;\n      }\n    }\n    inner.push(obj);\n    return false;\n  }\n\n  function unmemoize(obj: any): void {\n    if (hasWeakSet) {\n      inner.delete(obj);\n    } else {\n      for (let i = 0; i < inner.length; i++) {\n        if (inner[i] === obj) {\n          inner.splice(i, 1);\n          break;\n        }\n      }\n    }\n  }\n  return [memoize, unmemoize];\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { Event, Exception, Mechanism, StackFrame } from '@sentry/types';\n\nimport { addNonEnumerableProperty } from './object';\nimport { snipLine } from './string';\nimport { GLOBAL_OBJ } from './worldwide';\n\ninterface CryptoInternal {\n  getRandomValues(array: Uint8Array): Uint8Array;\n  randomUUID?(): string;\n}\n\n/** An interface for common properties on global */\ninterface CryptoGlobal {\n  msCrypto?: CryptoInternal;\n  crypto?: CryptoInternal;\n}\n\n/**\n * UUID4 generator\n *\n * @returns string Generated UUID4.\n */\nexport function uuid4(): string {\n  const gbl = GLOBAL_OBJ as typeof GLOBAL_OBJ & CryptoGlobal;\n  const crypto = gbl.crypto || gbl.msCrypto;\n\n  if (crypto && crypto.randomUUID) {\n    return crypto.randomUUID().replace(/-/g, '');\n  }\n\n  const getRandomByte =\n    crypto && crypto.getRandomValues ? () => crypto.getRandomValues(new Uint8Array(1))[0] : () => Math.random() * 16;\n\n  // http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript/2117523#2117523\n  // Concatenating the following numbers as strings results in '10000000100040008000100000000000'\n  return (([1e7] as unknown as string) + 1e3 + 4e3 + 8e3 + 1e11).replace(/[018]/g, c =>\n    // eslint-disable-next-line no-bitwise\n    ((c as unknown as number) ^ ((getRandomByte() & 15) >> ((c as unknown as number) / 4))).toString(16),\n  );\n}\n\nfunction getFirstException(event: Event): Exception | undefined {\n  return event.exception && event.exception.values ? event.exception.values[0] : undefined;\n}\n\n/**\n * Extracts either message or type+value from an event that can be used for user-facing logs\n * @returns event's description\n */\nexport function getEventDescription(event: Event): string {\n  const { message, event_id: eventId } = event;\n  if (message) {\n    return message;\n  }\n\n  const firstException = getFirstException(event);\n  if (firstException) {\n    if (firstException.type && firstException.value) {\n      return `${firstException.type}: ${firstException.value}`;\n    }\n    return firstException.type || firstException.value || eventId || '<unknown>';\n  }\n  return eventId || '<unknown>';\n}\n\n/**\n * Adds exception values, type and value to an synthetic Exception.\n * @param event The event to modify.\n * @param value Value of the exception.\n * @param type Type of the exception.\n * @hidden\n */\nexport function addExceptionTypeValue(event: Event, value?: string, type?: string): void {\n  const exception = (event.exception = event.exception || {});\n  const values = (exception.values = exception.values || []);\n  const firstException = (values[0] = values[0] || {});\n  if (!firstException.value) {\n    firstException.value = value || '';\n  }\n  if (!firstException.type) {\n    firstException.type = type || 'Error';\n  }\n}\n\n/**\n * Adds exception mechanism data to a given event. Uses defaults if the second parameter is not passed.\n *\n * @param event The event to modify.\n * @param newMechanism Mechanism data to add to the event.\n * @hidden\n */\nexport function addExceptionMechanism(event: Event, newMechanism?: Partial<Mechanism>): void {\n  const firstException = getFirstException(event);\n  if (!firstException) {\n    return;\n  }\n\n  const defaultMechanism = { type: 'generic', handled: true };\n  const currentMechanism = firstException.mechanism;\n  firstException.mechanism = { ...defaultMechanism, ...currentMechanism, ...newMechanism };\n\n  if (newMechanism && 'data' in newMechanism) {\n    const mergedData = { ...(currentMechanism && currentMechanism.data), ...newMechanism.data };\n    firstException.mechanism.data = mergedData;\n  }\n}\n\n// https://semver.org/#is-there-a-suggested-regular-expression-regex-to-check-a-semver-string\nconst SEMVER_REGEXP =\n  /^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$/;\n\n/**\n * Represents Semantic Versioning object\n */\ninterface SemVer {\n  major?: number;\n  minor?: number;\n  patch?: number;\n  prerelease?: string;\n  buildmetadata?: string;\n}\n\n/**\n * Parses input into a SemVer interface\n * @param input string representation of a semver version\n */\nexport function parseSemver(input: string): SemVer {\n  const match = input.match(SEMVER_REGEXP) || [];\n  const major = parseInt(match[1], 10);\n  const minor = parseInt(match[2], 10);\n  const patch = parseInt(match[3], 10);\n  return {\n    buildmetadata: match[5],\n    major: isNaN(major) ? undefined : major,\n    minor: isNaN(minor) ? undefined : minor,\n    patch: isNaN(patch) ? undefined : patch,\n    prerelease: match[4],\n  };\n}\n\n/**\n * This function adds context (pre/post/line) lines to the provided frame\n *\n * @param lines string[] containing all lines\n * @param frame StackFrame that will be mutated\n * @param linesOfContext number of context lines we want to add pre/post\n */\nexport function addContextToFrame(lines: string[], frame: StackFrame, linesOfContext: number = 5): void {\n  const lineno = frame.lineno || 0;\n  const maxLines = lines.length;\n  const sourceLine = Math.max(Math.min(maxLines, lineno - 1), 0);\n\n  frame.pre_context = lines\n    .slice(Math.max(0, sourceLine - linesOfContext), sourceLine)\n    .map((line: string) => snipLine(line, 0));\n\n  frame.context_line = snipLine(lines[Math.min(maxLines - 1, sourceLine)], frame.colno || 0);\n\n  frame.post_context = lines\n    .slice(Math.min(sourceLine + 1, maxLines), sourceLine + 1 + linesOfContext)\n    .map((line: string) => snipLine(line, 0));\n}\n\n/**\n * Checks whether or not we've already captured the given exception (note: not an identical exception - the very object\n * in question), and marks it captured if not.\n *\n * This is useful because it's possible for an error to get captured by more than one mechanism. After we intercept and\n * record an error, we rethrow it (assuming we've intercepted it before it's reached the top-level global handlers), so\n * that we don't interfere with whatever effects the error might have had were the SDK not there. At that point, because\n * the error has been rethrown, it's possible for it to bubble up to some other code we've instrumented. If it's not\n * caught after that, it will bubble all the way up to the global handlers (which of course we also instrument). This\n * function helps us ensure that even if we encounter the same error more than once, we only record it the first time we\n * see it.\n *\n * Note: It will ignore primitives (always return `false` and not mark them as seen), as properties can't be set on\n * them. {@link: Object.objectify} can be used on exceptions to convert any that are primitives into their equivalent\n * object wrapper forms so that this check will always work. However, because we need to flag the exact object which\n * will get rethrown, and because that rethrowing happens outside of the event processing pipeline, the objectification\n * must be done before the exception captured.\n *\n * @param A thrown exception to check or flag as having been seen\n * @returns `true` if the exception has already been captured, `false` if not (with the side effect of marking it seen)\n */\nexport function checkOrSetAlreadyCaught(exception: unknown): boolean {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  if (exception && (exception as any).__sentry_captured__) {\n    return true;\n  }\n\n  try {\n    // set it this way rather than by assignment so that it's not ennumerable and therefore isn't recorded by the\n    // `ExtraErrorData` integration\n    addNonEnumerableProperty(exception as { [key: string]: unknown }, '__sentry_captured__', true);\n  } catch (err) {\n    // `exception` is a primitive, so we can't mark it seen\n  }\n\n  return false;\n}\n\n/**\n * Checks whether the given input is already an array, and if it isn't, wraps it in one.\n *\n * @param maybeArray Input to turn into an array, if necessary\n * @returns The input, if already an array, or an array with the input as the only element, if not\n */\nexport function arrayify<T = unknown>(maybeArray: T | T[]): T[] {\n  return Array.isArray(maybeArray) ? maybeArray : [maybeArray];\n}\n", "/*\n * This module exists for optimizations in the build process through rollup and terser.  We define some global\n * constants, which can be overridden during build. By guarding certain pieces of code with functions that return these\n * constants, we can control whether or not they appear in the final bundle. (Any code guarded by a false condition will\n * never run, and will hence be dropped during treeshaking.) The two primary uses for this are stripping out calls to\n * `logger` and preventing node-related code from appearing in browser bundles.\n *\n * Attention:\n * This file should not be used to define constants/flags that are intended to be used for tree-shaking conducted by\n * users. These fags should live in their respective packages, as we identified user tooling (specifically webpack)\n * having issues tree-shaking these constants across package boundaries.\n * An example for this is the __SENTRY_DEBUG__ constant. It is declared in each package individually because we want\n * users to be able to shake away expressions that it guards.\n */\n\ndeclare const __SENTRY_BROWSER_BUNDLE__: boolean | undefined;\n\n/**\n * Figures out if we're building a browser bundle.\n *\n * @returns true if this is a browser bundle build.\n */\nexport function isBrowserBundle(): boolean {\n  return typeof __SENTRY_BROWSER_BUNDLE__ !== 'undefined' && !!__SENTRY_BROWSER_BUNDLE__;\n}\n", "/**\n * NOTE: In order to avoid circular dependencies, if you add a function to this module and it needs to print something,\n * you must either a) use `console.log` rather than the logger, or b) put your function elsewhere.\n */\n\nimport { isBrowserBundle } from './env';\n\n/**\n * Checks whether we're in the Node.js or Browser environment\n *\n * @returns Answer to given question\n */\nexport function isNodeEnv(): boolean {\n  // explicitly check for browser bundles as those can be optimized statically\n  // by terser/rollup.\n  return (\n    !isBrowserBundle() &&\n    Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]'\n  );\n}\n\n/**\n * Requires a module which is protected against bundler minification.\n *\n * @param request The module path to resolve\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types, @typescript-eslint/no-explicit-any\nexport function dynamicRequire(mod: any, request: string): any {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return mod.require(request);\n}\n\n/**\n * Helper for dynamically loading module that should work with linked dependencies.\n * The problem is that we _should_ be using `require(require.resolve(moduleName, { paths: [cwd()] }))`\n * However it's _not possible_ to do that with Webpack, as it has to know all the dependencies during\n * build time. `require.resolve` is also not available in any other way, so we cannot create,\n * a fake helper like we do with `dynamicRequire`.\n *\n * We always prefer to use local package, thus the value is not returned early from each `try/catch` block.\n * That is to mimic the behavior of `require.resolve` exactly.\n *\n * @param moduleName module name to require\n * @returns possibly required module\n */\nexport function loadModule<T>(moduleName: string): T | undefined {\n  let mod: T | undefined;\n\n  try {\n    mod = dynamicRequire(module, moduleName);\n  } catch (e) {\n    // no-empty\n  }\n\n  try {\n    const { cwd } = dynamicRequire(module, 'process');\n    mod = dynamicRequire(module, `${cwd()}/node_modules/${moduleName}`) as T;\n  } catch (e) {\n    // no-empty\n  }\n\n  return mod;\n}\n", "import { Primitive } from '@sentry/types';\n\nimport { isNaN, isSyntheticEvent } from './is';\nimport { memoBuilder, MemoFunc } from './memo';\nimport { convertToPlainObject } from './object';\nimport { getFunctionName } from './stacktrace';\n\ntype Prototype = { constructor: (...args: unknown[]) => unknown };\n// This is a hack to placate TS, relying on the fact that technically, arrays are objects with integer keys. Normally we\n// think of those keys as actual numbers, but `arr['0']` turns out to work just as well as `arr[0]`, and doing it this\n// way lets us use a single type in the places where behave as if we are only dealing with objects, even if some of them\n// might be arrays.\ntype ObjOrArray<T> = { [key: string]: T };\n\n/**\n * Recursively normalizes the given object.\n *\n * - Creates a copy to prevent original input mutation\n * - Skips non-enumerable properties\n * - When stringifying, calls `toJSON` if implemented\n * - Removes circular references\n * - Translates non-serializable values (`undefined`/`NaN`/functions) to serializable format\n * - Translates known global objects/classes to a string representations\n * - Takes care of `Error` object serialization\n * - Optionally limits depth of final output\n * - Optionally limits number of properties/elements included in any single object/array\n *\n * @param input The object to be normalized.\n * @param depth The max depth to which to normalize the object. (Anything deeper stringified whole.)\n * @param maxProperties The max number of elements or properties to be included in any single array or\n * object in the normallized output.\n * @returns A normalized version of the object, or `\"**non-serializable**\"` if any errors are thrown during normalization.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function normalize(input: unknown, depth: number = +Infinity, maxProperties: number = +Infinity): any {\n  try {\n    // since we're at the outermost level, we don't provide a key\n    return visit('', input, depth, maxProperties);\n  } catch (err) {\n    return { ERROR: `**non-serializable** (${err})` };\n  }\n}\n\n/** JSDoc */\nexport function normalizeToSize<T>(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  object: { [key: string]: any },\n  // Default Node.js REPL depth\n  depth: number = 3,\n  // 100kB, as 200kB is max payload size, so half sounds reasonable\n  maxSize: number = 100 * 1024,\n): T {\n  const normalized = normalize(object, depth);\n\n  if (jsonSize(normalized) > maxSize) {\n    return normalizeToSize(object, depth - 1, maxSize);\n  }\n\n  return normalized as T;\n}\n\n/**\n * Visits a node to perform normalization on it\n *\n * @param key The key corresponding to the given node\n * @param value The node to be visited\n * @param depth Optional number indicating the maximum recursion depth\n * @param maxProperties Optional maximum number of properties/elements included in any single object/array\n * @param memo Optional Memo class handling decycling\n */\nfunction visit(\n  key: string,\n  value: unknown,\n  depth: number = +Infinity,\n  maxProperties: number = +Infinity,\n  memo: MemoFunc = memoBuilder(),\n): Primitive | ObjOrArray<unknown> {\n  const [memoize, unmemoize] = memo;\n\n  // Get the simple cases out of the way first\n  if (value === null || (['number', 'boolean', 'string'].includes(typeof value) && !isNaN(value))) {\n    return value as Primitive;\n  }\n\n  const stringified = stringifyValue(key, value);\n\n  // Anything we could potentially dig into more (objects or arrays) will have come back as `\"[object XXXX]\"`.\n  // Everything else will have already been serialized, so if we don't see that pattern, we're done.\n  if (!stringified.startsWith('[object ')) {\n    return stringified;\n  }\n\n  // From here on, we can assert that `value` is either an object or an array.\n\n  // Do not normalize objects that we know have already been normalized. As a general rule, the\n  // \"__sentry_skip_normalization__\" property should only be used sparingly and only should only be set on objects that\n  // have already been normalized.\n  if ((value as ObjOrArray<unknown>)['__sentry_skip_normalization__']) {\n    return value as ObjOrArray<unknown>;\n  }\n\n  // We're also done if we've reached the max depth\n  if (depth === 0) {\n    // At this point we know `serialized` is a string of the form `\"[object XXXX]\"`. Clean it up so it's just `\"[XXXX]\"`.\n    return stringified.replace('object ', '');\n  }\n\n  // If we've already visited this branch, bail out, as it's circular reference. If not, note that we're seeing it now.\n  if (memoize(value)) {\n    return '[Circular ~]';\n  }\n\n  // If the value has a `toJSON` method, we call it to extract more information\n  const valueWithToJSON = value as unknown & { toJSON?: () => unknown };\n  if (valueWithToJSON && typeof valueWithToJSON.toJSON === 'function') {\n    try {\n      const jsonValue = valueWithToJSON.toJSON();\n      // We need to normalize the return value of `.toJSON()` in case it has circular references\n      return visit('', jsonValue, depth - 1, maxProperties, memo);\n    } catch (err) {\n      // pass (The built-in `toJSON` failed, but we can still try to do it ourselves)\n    }\n  }\n\n  // At this point we know we either have an object or an array, we haven't seen it before, and we're going to recurse\n  // because we haven't yet reached the max depth. Create an accumulator to hold the results of visiting each\n  // property/entry, and keep track of the number of items we add to it.\n  const normalized = (Array.isArray(value) ? [] : {}) as ObjOrArray<unknown>;\n  let numAdded = 0;\n\n  // Before we begin, convert`Error` and`Event` instances into plain objects, since some of each of their relevant\n  // properties are non-enumerable and otherwise would get missed.\n  const visitable = convertToPlainObject(value as ObjOrArray<unknown>);\n\n  for (const visitKey in visitable) {\n    // Avoid iterating over fields in the prototype if they've somehow been exposed to enumeration.\n    if (!Object.prototype.hasOwnProperty.call(visitable, visitKey)) {\n      continue;\n    }\n\n    if (numAdded >= maxProperties) {\n      normalized[visitKey] = '[MaxProperties ~]';\n      break;\n    }\n\n    // Recursively visit all the child nodes\n    const visitValue = visitable[visitKey];\n    normalized[visitKey] = visit(visitKey, visitValue, depth - 1, maxProperties, memo);\n\n    numAdded++;\n  }\n\n  // Once we've visited all the branches, remove the parent from memo storage\n  unmemoize(value);\n\n  // Return accumulated values\n  return normalized;\n}\n\n// TODO remove this in v7 (this means the method will no longer be exported, under any name)\nexport { visit as walk };\n\n/**\n * Stringify the given value. Handles various known special values and types.\n *\n * Not meant to be used on simple primitives which already have a string representation, as it will, for example, turn\n * the number 1231 into \"[Object Number]\", nor on `null`, as it will throw.\n *\n * @param value The value to stringify\n * @returns A stringified representation of the given value\n */\nfunction stringifyValue(\n  key: unknown,\n  // this type is a tiny bit of a cheat, since this function does handle NaN (which is technically a number), but for\n  // our internal use, it'll do\n  value: Exclude<unknown, string | number | boolean | null>,\n): string {\n  try {\n    if (key === 'domain' && value && typeof value === 'object' && (value as { _events: unknown })._events) {\n      return '[Domain]';\n    }\n\n    if (key === 'domainEmitter') {\n      return '[DomainEmitter]';\n    }\n\n    // It's safe to use `global`, `window`, and `document` here in this manner, as we are asserting using `typeof` first\n    // which won't throw if they are not present.\n\n    if (typeof global !== 'undefined' && value === global) {\n      return '[Global]';\n    }\n\n    // eslint-disable-next-line no-restricted-globals\n    if (typeof window !== 'undefined' && value === window) {\n      return '[Window]';\n    }\n\n    // eslint-disable-next-line no-restricted-globals\n    if (typeof document !== 'undefined' && value === document) {\n      return '[Document]';\n    }\n\n    // React's SyntheticEvent thingy\n    if (isSyntheticEvent(value)) {\n      return '[SyntheticEvent]';\n    }\n\n    if (typeof value === 'number' && value !== value) {\n      return '[NaN]';\n    }\n\n    // this catches `undefined` (but not `null`, which is a primitive and can be serialized on its own)\n    if (value === void 0) {\n      return '[undefined]';\n    }\n\n    if (typeof value === 'function') {\n      return `[Function: ${getFunctionName(value)}]`;\n    }\n\n    if (typeof value === 'symbol') {\n      return `[${String(value)}]`;\n    }\n\n    // stringified BigInts are indistinguishable from regular numbers, so we need to label them to avoid confusion\n    if (typeof value === 'bigint') {\n      return `[BigInt: ${String(value)}]`;\n    }\n\n    // Now that we've knocked out all the special cases and the primitives, all we have left are objects. Simply casting\n    // them to strings means that instances of classes which haven't defined their `toStringTag` will just come out as\n    // `\"[object Object]\"`. If we instead look at the constructor's name (which is the same as the name of the class),\n    // we can make sure that only plain objects come out that way.\n    return `[object ${(Object.getPrototypeOf(value) as Prototype).constructor.name}]`;\n  } catch (err) {\n    return `**non-serializable** (${err})`;\n  }\n}\n\n/** Calculates bytes size of input string */\nfunction utf8Length(value: string): number {\n  // eslint-disable-next-line no-bitwise\n  return ~-encodeURI(value).split(/%..|./).length;\n}\n\n/** Calculates bytes size of input object */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction jsonSize(value: any): number {\n  return utf8Length(JSON.stringify(value));\n}\n", "// Slightly modified (no IE8 support, ES6) and transcribed to TypeScript\n// https://raw.githubusercontent.com/calvinmetcalf/rollup-plugin-node-builtins/master/src/es6/path.js\n\n/** JSDoc */\nfunction normalizeArray(parts: string[], allowAboveRoot?: boolean): string[] {\n  // if the path tries to go above the root, `up` ends up > 0\n  let up = 0;\n  for (let i = parts.length - 1; i >= 0; i--) {\n    const last = parts[i];\n    if (last === '.') {\n      parts.splice(i, 1);\n    } else if (last === '..') {\n      parts.splice(i, 1);\n      up++;\n    } else if (up) {\n      parts.splice(i, 1);\n      up--;\n    }\n  }\n\n  // if the path is allowed to go above the root, restore leading ..s\n  if (allowAboveRoot) {\n    for (; up--; up) {\n      parts.unshift('..');\n    }\n  }\n\n  return parts;\n}\n\n// Split a filename into [root, dir, basename, ext], unix version\n// 'root' is just a slash, or nothing.\nconst splitPathRe = /^(\\/?|)([\\s\\S]*?)((?:\\.{1,2}|[^/]+?|)(\\.[^./]*|))(?:[/]*)$/;\n/** JSDoc */\nfunction splitPath(filename: string): string[] {\n  const parts = splitPathRe.exec(filename);\n  return parts ? parts.slice(1) : [];\n}\n\n// path.resolve([from ...], to)\n// posix version\n/** JSDoc */\nexport function resolve(...args: string[]): string {\n  let resolvedPath = '';\n  let resolvedAbsolute = false;\n\n  for (let i = args.length - 1; i >= -1 && !resolvedAbsolute; i--) {\n    const path = i >= 0 ? args[i] : '/';\n\n    // Skip empty entries\n    if (!path) {\n      continue;\n    }\n\n    resolvedPath = `${path}/${resolvedPath}`;\n    resolvedAbsolute = path.charAt(0) === '/';\n  }\n\n  // At this point the path should be resolved to a full absolute path, but\n  // handle relative paths to be safe (might happen when process.cwd() fails)\n\n  // Normalize the path\n  resolvedPath = normalizeArray(\n    resolvedPath.split('/').filter(p => !!p),\n    !resolvedAbsolute,\n  ).join('/');\n\n  return (resolvedAbsolute ? '/' : '') + resolvedPath || '.';\n}\n\n/** JSDoc */\nfunction trim(arr: string[]): string[] {\n  let start = 0;\n  for (; start < arr.length; start++) {\n    if (arr[start] !== '') {\n      break;\n    }\n  }\n\n  let end = arr.length - 1;\n  for (; end >= 0; end--) {\n    if (arr[end] !== '') {\n      break;\n    }\n  }\n\n  if (start > end) {\n    return [];\n  }\n  return arr.slice(start, end - start + 1);\n}\n\n// path.relative(from, to)\n// posix version\n/** JSDoc */\nexport function relative(from: string, to: string): string {\n  /* eslint-disable no-param-reassign */\n  from = resolve(from).substr(1);\n  to = resolve(to).substr(1);\n  /* eslint-enable no-param-reassign */\n\n  const fromParts = trim(from.split('/'));\n  const toParts = trim(to.split('/'));\n\n  const length = Math.min(fromParts.length, toParts.length);\n  let samePartsLength = length;\n  for (let i = 0; i < length; i++) {\n    if (fromParts[i] !== toParts[i]) {\n      samePartsLength = i;\n      break;\n    }\n  }\n\n  let outputParts = [];\n  for (let i = samePartsLength; i < fromParts.length; i++) {\n    outputParts.push('..');\n  }\n\n  outputParts = outputParts.concat(toParts.slice(samePartsLength));\n\n  return outputParts.join('/');\n}\n\n// path.normalize(path)\n// posix version\n/** JSDoc */\nexport function normalizePath(path: string): string {\n  const isPathAbsolute = isAbsolute(path);\n  const trailingSlash = path.substr(-1) === '/';\n\n  // Normalize the path\n  let normalizedPath = normalizeArray(\n    path.split('/').filter(p => !!p),\n    !isPathAbsolute,\n  ).join('/');\n\n  if (!normalizedPath && !isPathAbsolute) {\n    normalizedPath = '.';\n  }\n  if (normalizedPath && trailingSlash) {\n    normalizedPath += '/';\n  }\n\n  return (isPathAbsolute ? '/' : '') + normalizedPath;\n}\n\n// posix version\n/** JSDoc */\nexport function isAbsolute(path: string): boolean {\n  return path.charAt(0) === '/';\n}\n\n// posix version\n/** JSDoc */\nexport function join(...args: string[]): string {\n  return normalizePath(args.join('/'));\n}\n\n/** JSDoc */\nexport function dirname(path: string): string {\n  const result = splitPath(path);\n  const root = result[0];\n  let dir = result[1];\n\n  if (!root && !dir) {\n    // No dirname whatsoever\n    return '.';\n  }\n\n  if (dir) {\n    // It has a dirname, strip trailing slash\n    dir = dir.substr(0, dir.length - 1);\n  }\n\n  return root + dir;\n}\n\n/** JSDoc */\nexport function basename(path: string, ext?: string): string {\n  let f = splitPath(path)[2];\n  if (ext && f.substr(ext.length * -1) === ext) {\n    f = f.substr(0, f.length - ext.length);\n  }\n  return f;\n}\n", "/* eslint-disable @typescript-eslint/explicit-function-return-type */\n/* eslint-disable @typescript-eslint/typedef */\n/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { isThenable } from './is';\n\n/** SyncPromise internal states */\nconst enum States {\n  /** Pending */\n  PENDING = 0,\n  /** Resolved / OK */\n  RESOLVED = 1,\n  /** Rejected / Error */\n  REJECTED = 2,\n}\n\n// Overloads so we can call resolvedSyncPromise without arguments and generic argument\nexport function resolvedSyncPromise(): PromiseLike<void>;\nexport function resolvedSyncPromise<T>(value: T | PromiseLike<T>): PromiseLike<T>;\n\n/**\n * Creates a resolved sync promise.\n *\n * @param value the value to resolve the promise with\n * @returns the resolved sync promise\n */\nexport function resolvedSyncPromise<T>(value?: T | PromiseLike<T>): PromiseLike<T> {\n  return new SyncPromise(resolve => {\n    resolve(value);\n  });\n}\n\n/**\n * Creates a rejected sync promise.\n *\n * @param value the value to reject the promise with\n * @returns the rejected sync promise\n */\nexport function rejectedSyncPromise<T = never>(reason?: any): PromiseLike<T> {\n  return new SyncPromise((_, reject) => {\n    reject(reason);\n  });\n}\n\n/**\n * Thenable class that behaves like a Promise and follows it's interface\n * but is not async internally\n */\nclass SyncPromise<T> implements PromiseLike<T> {\n  private _state: States = States.PENDING;\n  private _handlers: Array<[boolean, (value: T) => void, (reason: any) => any]> = [];\n  private _value: any;\n\n  public constructor(\n    executor: (resolve: (value?: T | PromiseLike<T> | null) => void, reject: (reason?: any) => void) => void,\n  ) {\n    try {\n      executor(this._resolve, this._reject);\n    } catch (e) {\n      this._reject(e);\n    }\n  }\n\n  /** JSDoc */\n  public then<TResult1 = T, TResult2 = never>(\n    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | null,\n    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null,\n  ): PromiseLike<TResult1 | TResult2> {\n    return new SyncPromise((resolve, reject) => {\n      this._handlers.push([\n        false,\n        result => {\n          if (!onfulfilled) {\n            // TODO: ¯\\_(ツ)_/¯\n            // TODO: FIXME\n            resolve(result as any);\n          } else {\n            try {\n              resolve(onfulfilled(result));\n            } catch (e) {\n              reject(e);\n            }\n          }\n        },\n        reason => {\n          if (!onrejected) {\n            reject(reason);\n          } else {\n            try {\n              resolve(onrejected(reason));\n            } catch (e) {\n              reject(e);\n            }\n          }\n        },\n      ]);\n      this._executeHandlers();\n    });\n  }\n\n  /** JSDoc */\n  public catch<TResult = never>(\n    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null,\n  ): PromiseLike<T | TResult> {\n    return this.then(val => val, onrejected);\n  }\n\n  /** JSDoc */\n  public finally<TResult>(onfinally?: (() => void) | null): PromiseLike<TResult> {\n    return new SyncPromise<TResult>((resolve, reject) => {\n      let val: TResult | any;\n      let isRejected: boolean;\n\n      return this.then(\n        value => {\n          isRejected = false;\n          val = value;\n          if (onfinally) {\n            onfinally();\n          }\n        },\n        reason => {\n          isRejected = true;\n          val = reason;\n          if (onfinally) {\n            onfinally();\n          }\n        },\n      ).then(() => {\n        if (isRejected) {\n          reject(val);\n          return;\n        }\n\n        resolve(val as unknown as any);\n      });\n    });\n  }\n\n  /** JSDoc */\n  private readonly _resolve = (value?: T | PromiseLike<T> | null) => {\n    this._setResult(States.RESOLVED, value);\n  };\n\n  /** JSDoc */\n  private readonly _reject = (reason?: any) => {\n    this._setResult(States.REJECTED, reason);\n  };\n\n  /** JSDoc */\n  private readonly _setResult = (state: States, value?: T | PromiseLike<T> | any) => {\n    if (this._state !== States.PENDING) {\n      return;\n    }\n\n    if (isThenable(value)) {\n      void (value as PromiseLike<T>).then(this._resolve, this._reject);\n      return;\n    }\n\n    this._state = state;\n    this._value = value;\n\n    this._executeHandlers();\n  };\n\n  /** JSDoc */\n  private readonly _executeHandlers = () => {\n    if (this._state === States.PENDING) {\n      return;\n    }\n\n    const cachedHandlers = this._handlers.slice();\n    this._handlers = [];\n\n    cachedHandlers.forEach(handler => {\n      if (handler[0]) {\n        return;\n      }\n\n      if (this._state === States.RESOLVED) {\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        handler[1](this._value as unknown as any);\n      }\n\n      if (this._state === States.REJECTED) {\n        handler[2](this._value);\n      }\n\n      handler[0] = true;\n    });\n  };\n}\n\nexport { SyncPromise };\n", "import { SentryError } from './error';\nimport { rejectedSyncPromise, resolvedSyncPromise, SyncPromise } from './syncpromise';\n\nexport interface PromiseBuffer<T> {\n  // exposes the internal array so tests can assert on the state of it.\n  // XXX: this really should not be public api.\n  $: Array<PromiseLike<T>>;\n  add(taskProducer: () => PromiseLike<T>): PromiseLike<T>;\n  drain(timeout?: number): PromiseLike<boolean>;\n}\n\n/**\n * Creates an new PromiseBuffer object with the specified limit\n * @param limit max number of promises that can be stored in the buffer\n */\nexport function makePromiseBuffer<T>(limit?: number): PromiseBuffer<T> {\n  const buffer: Array<PromiseLike<T>> = [];\n\n  function isReady(): boolean {\n    return limit === undefined || buffer.length < limit;\n  }\n\n  /**\n   * Remove a promise from the queue.\n   *\n   * @param task Can be any PromiseLike<T>\n   * @returns Removed promise.\n   */\n  function remove(task: PromiseLike<T>): PromiseLike<T> {\n    return buffer.splice(buffer.indexOf(task), 1)[0];\n  }\n\n  /**\n   * Add a promise (representing an in-flight action) to the queue, and set it to remove itself on fulfillment.\n   *\n   * @param taskProducer A function producing any PromiseLike<T>; In previous versions this used to be `task:\n   *        PromiseLike<T>`, but under that model, Promises were instantly created on the call-site and their executor\n   *        functions therefore ran immediately. Thus, even if the buffer was full, the action still happened. By\n   *        requiring the promise to be wrapped in a function, we can defer promise creation until after the buffer\n   *        limit check.\n   * @returns The original promise.\n   */\n  function add(taskProducer: () => PromiseLike<T>): PromiseLike<T> {\n    if (!isReady()) {\n      return rejectedSyncPromise(new SentryError('Not adding Promise because buffer limit was reached.'));\n    }\n\n    // start the task and add its promise to the queue\n    const task = taskProducer();\n    if (buffer.indexOf(task) === -1) {\n      buffer.push(task);\n    }\n    void task\n      .then(() => remove(task))\n      // Use `then(null, rejectionHandler)` rather than `catch(rejectionHandler)` so that we can use `PromiseLike`\n      // rather than `Promise`. `PromiseLike` doesn't have a `.catch` method, making its polyfill smaller. (ES5 didn't\n      // have promises, so TS has to polyfill when down-compiling.)\n      .then(null, () =>\n        remove(task).then(null, () => {\n          // We have to add another catch here because `remove()` starts a new promise chain.\n        }),\n      );\n    return task;\n  }\n\n  /**\n   * Wait for all promises in the queue to resolve or for timeout to expire, whichever comes first.\n   *\n   * @param timeout The time, in ms, after which to resolve to `false` if the queue is still non-empty. Passing `0` (or\n   * not passing anything) will make the promise wait as long as it takes for the queue to drain before resolving to\n   * `true`.\n   * @returns A promise which will resolve to `true` if the queue is already empty or drains before the timeout, and\n   * `false` otherwise\n   */\n  function drain(timeout?: number): PromiseLike<boolean> {\n    return new SyncPromise<boolean>((resolve, reject) => {\n      let counter = buffer.length;\n\n      if (!counter) {\n        return resolve(true);\n      }\n\n      // wait for `timeout` ms and then resolve to `false` (if not cancelled first)\n      const capturedSetTimeout = setTimeout(() => {\n        if (timeout && timeout > 0) {\n          resolve(false);\n        }\n      }, timeout);\n\n      // if all promises resolve in time, cancel the timer and resolve to `true`\n      buffer.forEach(item => {\n        void resolvedSyncPromise(item).then(() => {\n          if (!--counter) {\n            clearTimeout(capturedSetTimeout);\n            resolve(true);\n          }\n        }, reject);\n      });\n    });\n  }\n\n  return {\n    $: buffer,\n    add,\n    drain,\n  };\n}\n", "import { dynamicRequire, isNodeEnv } from './node';\nimport { getGlobalObject } from './worldwide';\n\n// eslint-disable-next-line deprecation/deprecation\nconst WINDOW = getGlobalObject<Window>();\n\n/**\n * An object that can return the current timestamp in seconds since the UNIX epoch.\n */\ninterface TimestampSource {\n  nowSeconds(): number;\n}\n\n/**\n * A TimestampSource implementation for environments that do not support the Performance Web API natively.\n *\n * Note that this TimestampSource does not use a monotonic clock. A call to `nowSeconds` may return a timestamp earlier\n * than a previously returned value. We do not try to emulate a monotonic behavior in order to facilitate debugging. It\n * is more obvious to explain \"why does my span have negative duration\" than \"why my spans have zero duration\".\n */\nconst dateTimestampSource: TimestampSource = {\n  nowSeconds: () => Date.now() / 1000,\n};\n\n/**\n * A partial definition of the [Performance Web API]{@link https://developer.mozilla.org/en-US/docs/Web/API/Performance}\n * for accessing a high-resolution monotonic clock.\n */\ninterface Performance {\n  /**\n   * The millisecond timestamp at which measurement began, measured in Unix time.\n   */\n  timeOrigin: number;\n  /**\n   * Returns the current millisecond timestamp, where 0 represents the start of measurement.\n   */\n  now(): number;\n}\n\n/**\n * Returns a wrapper around the native Performance API browser implementation, or undefined for browsers that do not\n * support the API.\n *\n * Wrapping the native API works around differences in behavior from different browsers.\n */\nfunction getBrowserPerformance(): Performance | undefined {\n  const { performance } = WINDOW;\n  if (!performance || !performance.now) {\n    return undefined;\n  }\n\n  // Replace performance.timeOrigin with our own timeOrigin based on Date.now().\n  //\n  // This is a partial workaround for browsers reporting performance.timeOrigin such that performance.timeOrigin +\n  // performance.now() gives a date arbitrarily in the past.\n  //\n  // Additionally, computing timeOrigin in this way fills the gap for browsers where performance.timeOrigin is\n  // undefined.\n  //\n  // The assumption that performance.timeOrigin + performance.now() ~= Date.now() is flawed, but we depend on it to\n  // interact with data coming out of performance entries.\n  //\n  // Note that despite recommendations against it in the spec, browsers implement the Performance API with a clock that\n  // might stop when the computer is asleep (and perhaps under other circumstances). Such behavior causes\n  // performance.timeOrigin + performance.now() to have an arbitrary skew over Date.now(). In laptop computers, we have\n  // observed skews that can be as long as days, weeks or months.\n  //\n  // See https://github.com/getsentry/sentry-javascript/issues/2590.\n  //\n  // BUG: despite our best intentions, this workaround has its limitations. It mostly addresses timings of pageload\n  // transactions, but ignores the skew built up over time that can aversely affect timestamps of navigation\n  // transactions of long-lived web pages.\n  const timeOrigin = Date.now() - performance.now();\n\n  return {\n    now: () => performance.now(),\n    timeOrigin,\n  };\n}\n\n/**\n * Returns the native Performance API implementation from Node.js. Returns undefined in old Node.js versions that don't\n * implement the API.\n */\nfunction getNodePerformance(): Performance | undefined {\n  try {\n    const perfHooks = dynamicRequire(module, 'perf_hooks') as { performance: Performance };\n    return perfHooks.performance;\n  } catch (_) {\n    return undefined;\n  }\n}\n\n/**\n * The Performance API implementation for the current platform, if available.\n */\nconst platformPerformance: Performance | undefined = isNodeEnv() ? getNodePerformance() : getBrowserPerformance();\n\nconst timestampSource: TimestampSource =\n  platformPerformance === undefined\n    ? dateTimestampSource\n    : {\n        nowSeconds: () => (platformPerformance.timeOrigin + platformPerformance.now()) / 1000,\n      };\n\n/**\n * Returns a timestamp in seconds since the UNIX epoch using the Date API.\n */\nexport const dateTimestampInSeconds: () => number = dateTimestampSource.nowSeconds.bind(dateTimestampSource);\n\n/**\n * Returns a timestamp in seconds since the UNIX epoch using either the Performance or Date APIs, depending on the\n * availability of the Performance API.\n *\n * See `usingPerformanceAPI` to test whether the Performance API is used.\n *\n * BUG: Note that because of how browsers implement the Performance API, the clock might stop when the computer is\n * asleep. This creates a skew between `dateTimestampInSeconds` and `timestampInSeconds`. The\n * skew can grow to arbitrary amounts like days, weeks or months.\n * See https://github.com/getsentry/sentry-javascript/issues/2590.\n */\nexport const timestampInSeconds: () => number = timestampSource.nowSeconds.bind(timestampSource);\n\n// Re-exported with an old name for backwards-compatibility.\nexport const timestampWithMs = timestampInSeconds;\n\n/**\n * A boolean that is true when timestampInSeconds uses the Performance API to produce monotonic timestamps.\n */\nexport const usingPerformanceAPI = platformPerformance !== undefined;\n\n/**\n * Internal helper to store what is the source of browserPerformanceTimeOrigin below. For debugging only.\n */\nexport let _browserPerformanceTimeOriginMode: string;\n\n/**\n * The number of milliseconds since the UNIX epoch. This value is only usable in a browser, and only when the\n * performance API is available.\n */\nexport const browserPerformanceTimeOrigin = ((): number | undefined => {\n  // Unfortunately browsers may report an inaccurate time origin data, through either performance.timeOrigin or\n  // performance.timing.navigationStart, which results in poor results in performance data. We only treat time origin\n  // data as reliable if they are within a reasonable threshold of the current time.\n\n  const { performance } = WINDOW;\n  if (!performance || !performance.now) {\n    _browserPerformanceTimeOriginMode = 'none';\n    return undefined;\n  }\n\n  const threshold = 3600 * 1000;\n  const performanceNow = performance.now();\n  const dateNow = Date.now();\n\n  // if timeOrigin isn't available set delta to threshold so it isn't used\n  const timeOriginDelta = performance.timeOrigin\n    ? Math.abs(performance.timeOrigin + performanceNow - dateNow)\n    : threshold;\n  const timeOriginIsReliable = timeOriginDelta < threshold;\n\n  // While performance.timing.navigationStart is deprecated in favor of performance.timeOrigin, performance.timeOrigin\n  // is not as widely supported. Namely, performance.timeOrigin is undefined in Safari as of writing.\n  // Also as of writing, performance.timing is not available in Web Workers in mainstream browsers, so it is not always\n  // a valid fallback. In the absence of an initial time provided by the browser, fallback to the current time from the\n  // Date API.\n  // eslint-disable-next-line deprecation/deprecation\n  const navigationStart = performance.timing && performance.timing.navigationStart;\n  const hasNavigationStart = typeof navigationStart === 'number';\n  // if navigationStart isn't available set delta to threshold so it isn't used\n  const navigationStartDelta = hasNavigationStart ? Math.abs(navigationStart + performanceNow - dateNow) : threshold;\n  const navigationStartIsReliable = navigationStartDelta < threshold;\n\n  if (timeOriginIsReliable || navigationStartIsReliable) {\n    // Use the more reliable time origin\n    if (timeOriginDelta <= navigationStartDelta) {\n      _browserPerformanceTimeOriginMode = 'timeOrigin';\n      return performance.timeOrigin;\n    } else {\n      _browserPerformanceTimeOriginMode = 'navigationStart';\n      return navigationStart;\n    }\n  }\n\n  // Either both timeOrigin and navigationStart are skewed or neither is available, fallback to Date.\n  _browserPerformanceTimeOriginMode = 'dateNow';\n  return dateNow;\n})();\n", "import {\n  Attachment,\n  AttachmentItem,\n  DataCategory,\n  Envelope,\n  EnvelopeItem,\n  EnvelopeItemType,\n  TextEncoderInternal,\n} from '@sentry/types';\n\nimport { normalize } from './normalize';\nimport { dropUndefinedKeys } from './object';\n\n/**\n * Creates an envelope.\n * Make sure to always explicitly provide the generic to this function\n * so that the envelope types resolve correctly.\n */\nexport function createEnvelope<E extends Envelope>(headers: E[0], items: E[1] = []): E {\n  return [headers, items] as E;\n}\n\n/**\n * Add an item to an envelope.\n * Make sure to always explicitly provide the generic to this function\n * so that the envelope types resolve correctly.\n */\nexport function addItemToEnvelope<E extends Envelope>(envelope: E, newItem: E[1][number]): E {\n  const [headers, items] = envelope;\n  return [headers, [...items, newItem]] as E;\n}\n\n/**\n * Convenience function to loop through the items and item types of an envelope.\n * (This function was mostly created because working with envelope types is painful at the moment)\n */\nexport function forEachEnvelopeItem<E extends Envelope>(\n  envelope: Envelope,\n  callback: (envelopeItem: E[1][number], envelopeItemType: E[1][number][0]['type']) => void,\n): void {\n  const envelopeItems = envelope[1];\n  envelopeItems.forEach((envelopeItem: EnvelopeItem) => {\n    const envelopeItemType = envelopeItem[0].type;\n    callback(envelopeItem, envelopeItemType);\n  });\n}\n\nfunction encodeUTF8(input: string, textEncoder?: TextEncoderInternal): Uint8Array {\n  const utf8 = textEncoder || new TextEncoder();\n  return utf8.encode(input);\n}\n\n/**\n * Serializes an envelope.\n */\nexport function serializeEnvelope(envelope: Envelope, textEncoder?: TextEncoderInternal): string | Uint8Array {\n  const [envHeaders, items] = envelope;\n\n  // Initially we construct our envelope as a string and only convert to binary chunks if we encounter binary data\n  let parts: string | Uint8Array[] = JSON.stringify(envHeaders);\n\n  function append(next: string | Uint8Array): void {\n    if (typeof parts === 'string') {\n      parts = typeof next === 'string' ? parts + next : [encodeUTF8(parts, textEncoder), next];\n    } else {\n      parts.push(typeof next === 'string' ? encodeUTF8(next, textEncoder) : next);\n    }\n  }\n\n  for (const item of items) {\n    const [itemHeaders, payload] = item;\n\n    append(`\\n${JSON.stringify(itemHeaders)}\\n`);\n\n    if (typeof payload === 'string' || payload instanceof Uint8Array) {\n      append(payload);\n    } else {\n      let stringifiedPayload: string;\n      try {\n        stringifiedPayload = JSON.stringify(payload);\n      } catch (e) {\n        // In case, despite all our efforts to keep `payload` circular-dependency-free, `JSON.strinify()` still\n        // fails, we try again after normalizing it again with infinite normalization depth. This of course has a\n        // performance impact but in this case a performance hit is better than throwing.\n        stringifiedPayload = JSON.stringify(normalize(payload));\n      }\n      append(stringifiedPayload);\n    }\n  }\n\n  return typeof parts === 'string' ? parts : concatBuffers(parts);\n}\n\nfunction concatBuffers(buffers: Uint8Array[]): Uint8Array {\n  const totalLength = buffers.reduce((acc, buf) => acc + buf.length, 0);\n\n  const merged = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const buffer of buffers) {\n    merged.set(buffer, offset);\n    offset += buffer.length;\n  }\n\n  return merged;\n}\n\n/**\n * Creates attachment envelope items\n */\nexport function createAttachmentEnvelopeItem(\n  attachment: Attachment,\n  textEncoder?: TextEncoderInternal,\n): AttachmentItem {\n  const buffer = typeof attachment.data === 'string' ? encodeUTF8(attachment.data, textEncoder) : attachment.data;\n\n  return [\n    dropUndefinedKeys({\n      type: 'attachment',\n      length: buffer.length,\n      filename: attachment.filename,\n      content_type: attachment.contentType,\n      attachment_type: attachment.attachmentType,\n    }),\n    buffer,\n  ];\n}\n\nconst ITEM_TYPE_TO_DATA_CATEGORY_MAP: Record<EnvelopeItemType, DataCategory> = {\n  session: 'session',\n  sessions: 'session',\n  attachment: 'attachment',\n  transaction: 'transaction',\n  event: 'error',\n  client_report: 'internal',\n  user_report: 'default',\n};\n\n/**\n * Maps the type of an envelope item to a data category.\n */\nexport function envelopeItemTypeToDataCategory(type: EnvelopeItemType): DataCategory {\n  return ITEM_TYPE_TO_DATA_CATEGORY_MAP[type];\n}\n", "import { TransportMakeRequestResponse } from '@sentry/types';\n\n// Intentionally keeping the key broad, as we don't know for sure what rate limit headers get returned from backend\nexport type RateLimits = Record<string, number>;\n\nexport const DEFAULT_RETRY_AFTER = 60 * 1000; // 60 seconds\n\n/**\n * Extracts Retry-After value from the request header or returns default value\n * @param header string representation of 'Retry-After' header\n * @param now current unix timestamp\n *\n */\nexport function parseRetryAfterHeader(header: string, now: number = Date.now()): number {\n  const headerDelay = parseInt(`${header}`, 10);\n  if (!isNaN(headerDelay)) {\n    return headerDelay * 1000;\n  }\n\n  const headerDate = Date.parse(`${header}`);\n  if (!isNaN(headerDate)) {\n    return headerDate - now;\n  }\n\n  return DEFAULT_RETRY_AFTER;\n}\n\n/**\n * Gets the time that given category is disabled until for rate limiting\n */\nexport function disabledUntil(limits: RateLimits, category: string): number {\n  return limits[category] || limits.all || 0;\n}\n\n/**\n * Checks if a category is rate limited\n */\nexport function isRateLimited(limits: RateLimits, category: string, now: number = Date.now()): boolean {\n  return disabledUntil(limits, category) > now;\n}\n\n/**\n * Update ratelimits from incoming headers.\n * Returns true if headers contains a non-empty rate limiting header.\n */\nexport function updateRateLimits(\n  limits: RateLimits,\n  { statusCode, headers }: TransportMakeRequestResponse,\n  now: number = Date.now(),\n): RateLimits {\n  const updatedRateLimits: RateLimits = {\n    ...limits,\n  };\n\n  // \"The name is case-insensitive.\"\n  // https://developer.mozilla.org/en-US/docs/Web/API/Headers/get\n  const rateLimitHeader = headers && headers['x-sentry-rate-limits'];\n  const retryAfterHeader = headers && headers['retry-after'];\n\n  if (rateLimitHeader) {\n    /**\n     * rate limit headers are of the form\n     *     <header>,<header>,..\n     * where each <header> is of the form\n     *     <retry_after>: <categories>: <scope>: <reason_code>\n     * where\n     *     <retry_after> is a delay in seconds\n     *     <categories> is the event type(s) (error, transaction, etc) being rate limited and is of the form\n     *         <category>;<category>;...\n     *     <scope> is what's being limited (org, project, or key) - ignored by SDK\n     *     <reason_code> is an arbitrary string like \"org_quota\" - ignored by SDK\n     */\n    for (const limit of rateLimitHeader.trim().split(',')) {\n      const [retryAfter, categories] = limit.split(':', 2);\n      const headerDelay = parseInt(retryAfter, 10);\n      const delay = (!isNaN(headerDelay) ? headerDelay : 60) * 1000; // 60sec default\n      if (!categories) {\n        updatedRateLimits.all = now + delay;\n      } else {\n        for (const category of categories.split(';')) {\n          updatedRateLimits[category] = now + delay;\n        }\n      }\n    }\n  } else if (retryAfterHeader) {\n    updatedRateLimits.all = now + parseRetryAfterHeader(retryAfterHeader, now);\n  } else if (statusCode === 429) {\n    updatedRateLimits.all = now + 60 * 1000;\n  }\n\n  return updatedRateLimits;\n}\n", "import { SerializedSession, Session, SessionContext, SessionStatus } from '@sentry/types';\nimport { dropUndefinedKeys, timestampInSeconds, uuid4 } from '@sentry/utils';\n\n/**\n * Creates a new `Session` object by setting certain default parameters. If optional @param context\n * is passed, the passed properties are applied to the session object.\n *\n * @param context (optional) additional properties to be applied to the returned session object\n *\n * @returns a new `Session` object\n */\nexport function makeSession(context?: Omit<SessionContext, 'started' | 'status'>): Session {\n  // Both timestamp and started are in seconds since the UNIX epoch.\n  const startingTime = timestampInSeconds();\n\n  const session: Session = {\n    sid: uuid4(),\n    init: true,\n    timestamp: startingTime,\n    started: startingTime,\n    duration: 0,\n    status: 'ok',\n    errors: 0,\n    ignoreDuration: false,\n    toJSON: () => sessionToJSON(session),\n  };\n\n  if (context) {\n    updateSession(session, context);\n  }\n\n  return session;\n}\n\n/**\n * Updates a session object with the properties passed in the context.\n *\n * Note that this function mutates the passed object and returns void.\n * (Had to do this instead of returning a new and updated session because closing and sending a session\n * makes an update to the session after it was passed to the sending logic.\n * @see BaseClient.captureSession )\n *\n * @param session the `Session` to update\n * @param context the `SessionContext` holding the properties that should be updated in @param session\n */\n// eslint-disable-next-line complexity\nexport function updateSession(session: Session, context: SessionContext = {}): void {\n  if (context.user) {\n    if (!session.ipAddress && context.user.ip_address) {\n      session.ipAddress = context.user.ip_address;\n    }\n\n    if (!session.did && !context.did) {\n      session.did = context.user.id || context.user.email || context.user.username;\n    }\n  }\n\n  session.timestamp = context.timestamp || timestampInSeconds();\n\n  if (context.ignoreDuration) {\n    session.ignoreDuration = context.ignoreDuration;\n  }\n  if (context.sid) {\n    // Good enough uuid validation. — Kamil\n    session.sid = context.sid.length === 32 ? context.sid : uuid4();\n  }\n  if (context.init !== undefined) {\n    session.init = context.init;\n  }\n  if (!session.did && context.did) {\n    session.did = `${context.did}`;\n  }\n  if (typeof context.started === 'number') {\n    session.started = context.started;\n  }\n  if (session.ignoreDuration) {\n    session.duration = undefined;\n  } else if (typeof context.duration === 'number') {\n    session.duration = context.duration;\n  } else {\n    const duration = session.timestamp - session.started;\n    session.duration = duration >= 0 ? duration : 0;\n  }\n  if (context.release) {\n    session.release = context.release;\n  }\n  if (context.environment) {\n    session.environment = context.environment;\n  }\n  if (!session.ipAddress && context.ipAddress) {\n    session.ipAddress = context.ipAddress;\n  }\n  if (!session.userAgent && context.userAgent) {\n    session.userAgent = context.userAgent;\n  }\n  if (typeof context.errors === 'number') {\n    session.errors = context.errors;\n  }\n  if (context.status) {\n    session.status = context.status;\n  }\n}\n\n/**\n * Closes a session by setting its status and updating the session object with it.\n * Internally calls `updateSession` to update the passed session object.\n *\n * Note that this function mutates the passed session (@see updateSession for explanation).\n *\n * @param session the `Session` object to be closed\n * @param status the `SessionStatus` with which the session was closed. If you don't pass a status,\n *               this function will keep the previously set status, unless it was `'ok'` in which case\n *               it is changed to `'exited'`.\n */\nexport function closeSession(session: Session, status?: Exclude<SessionStatus, 'ok'>): void {\n  let context = {};\n  if (status) {\n    context = { status };\n  } else if (session.status === 'ok') {\n    context = { status: 'exited' };\n  }\n\n  updateSession(session, context);\n}\n\n/**\n * Serializes a passed session object to a JSON object with a slightly different structure.\n * This is necessary because the Sentry backend requires a slightly different schema of a session\n * than the one the JS SDKs use internally.\n *\n * @param session the session to be converted\n *\n * @returns a JSON object of the passed session\n */\nfunction sessionToJSON(session: Session): SerializedSession {\n  return dropUndefinedKeys({\n    sid: `${session.sid}`,\n    init: session.init,\n    // Make sure that sec is converted to ms for date constructor\n    started: new Date(session.started * 1000).toISOString(),\n    timestamp: new Date(session.timestamp * 1000).toISOString(),\n    status: session.status,\n    errors: session.errors,\n    did: typeof session.did === 'number' || typeof session.did === 'string' ? `${session.did}` : undefined,\n    duration: session.duration,\n    attrs: {\n      release: session.release,\n      environment: session.environment,\n      ip_address: session.ipAddress,\n      user_agent: session.userAgent,\n    },\n  });\n}\n", "/* eslint-disable max-lines */\nimport {\n  Attachment,\n  Breadcrumb,\n  CaptureContext,\n  Context,\n  Contexts,\n  Event,\n  EventHint,\n  EventProcessor,\n  Extra,\n  Extras,\n  Primitive,\n  RequestSession,\n  Scope as ScopeInterface,\n  ScopeContext,\n  Session,\n  Severity,\n  SeverityLevel,\n  Span,\n  Transaction,\n  User,\n} from '@sentry/types';\nimport {\n  arrayify,\n  dateTimestampInSeconds,\n  getGlobalSingleton,\n  isPlainObject,\n  isThenable,\n  logger,\n  SyncPromise,\n} from '@sentry/utils';\n\nimport { updateSession } from './session';\n\n/**\n * Default value for maximum number of breadcrumbs added to an event.\n */\nconst DEFAULT_MAX_BREADCRUMBS = 100;\n\n/**\n * Holds additional event information. {@link Scope.applyToEvent} will be\n * called by the client before an event will be sent.\n */\nexport class Scope implements ScopeInterface {\n  /** Flag if notifying is happening. */\n  protected _notifyingListeners: boolean;\n\n  /** Callback for client to receive scope changes. */\n  protected _scopeListeners: Array<(scope: Scope) => void>;\n\n  /** Callback list that will be called after {@link applyToEvent}. */\n  protected _eventProcessors: EventProcessor[];\n\n  /** Array of breadcrumbs. */\n  protected _breadcrumbs: Breadcrumb[];\n\n  /** User */\n  protected _user: User;\n\n  /** Tags */\n  protected _tags: { [key: string]: Primitive };\n\n  /** Extra */\n  protected _extra: Extras;\n\n  /** Contexts */\n  protected _contexts: Contexts;\n\n  /** Attachments */\n  protected _attachments: Attachment[];\n\n  /**\n   * A place to stash data which is needed at some point in the SDK's event processing pipeline but which shouldn't get\n   * sent to Sentry\n   */\n  protected _sdkProcessingMetadata: { [key: string]: unknown };\n\n  /** Fingerprint */\n  protected _fingerprint?: string[];\n\n  /** Severity */\n  // eslint-disable-next-line deprecation/deprecation\n  protected _level?: Severity | SeverityLevel;\n\n  /** Transaction Name */\n  protected _transactionName?: string;\n\n  /** Span */\n  protected _span?: Span;\n\n  /** Session */\n  protected _session?: Session;\n\n  /** Request Mode Session Status */\n  protected _requestSession?: RequestSession;\n\n  // NOTE: Any field which gets added here should get added not only to the constructor but also to the `clone` method.\n\n  public constructor() {\n    this._notifyingListeners = false;\n    this._scopeListeners = [];\n    this._eventProcessors = [];\n    this._breadcrumbs = [];\n    this._attachments = [];\n    this._user = {};\n    this._tags = {};\n    this._extra = {};\n    this._contexts = {};\n    this._sdkProcessingMetadata = {};\n  }\n\n  /**\n   * Inherit values from the parent scope.\n   * @param scope to clone.\n   */\n  public static clone(scope?: Scope): Scope {\n    const newScope = new Scope();\n    if (scope) {\n      newScope._breadcrumbs = [...scope._breadcrumbs];\n      newScope._tags = { ...scope._tags };\n      newScope._extra = { ...scope._extra };\n      newScope._contexts = { ...scope._contexts };\n      newScope._user = scope._user;\n      newScope._level = scope._level;\n      newScope._span = scope._span;\n      newScope._session = scope._session;\n      newScope._transactionName = scope._transactionName;\n      newScope._fingerprint = scope._fingerprint;\n      newScope._eventProcessors = [...scope._eventProcessors];\n      newScope._requestSession = scope._requestSession;\n      newScope._attachments = [...scope._attachments];\n      newScope._sdkProcessingMetadata = { ...scope._sdkProcessingMetadata };\n    }\n    return newScope;\n  }\n\n  /**\n   * Add internal on change listener. Used for sub SDKs that need to store the scope.\n   * @hidden\n   */\n  public addScopeListener(callback: (scope: Scope) => void): void {\n    this._scopeListeners.push(callback);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addEventProcessor(callback: EventProcessor): this {\n    this._eventProcessors.push(callback);\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setUser(user: User | null): this {\n    this._user = user || {};\n    if (this._session) {\n      updateSession(this._session, { user });\n    }\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getUser(): User | undefined {\n    return this._user;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getRequestSession(): RequestSession | undefined {\n    return this._requestSession;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setRequestSession(requestSession?: RequestSession): this {\n    this._requestSession = requestSession;\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTags(tags: { [key: string]: Primitive }): this {\n    this._tags = {\n      ...this._tags,\n      ...tags,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTag(key: string, value: Primitive): this {\n    this._tags = { ...this._tags, [key]: value };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtras(extras: Extras): this {\n    this._extra = {\n      ...this._extra,\n      ...extras,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtra(key: string, extra: Extra): this {\n    this._extra = { ...this._extra, [key]: extra };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setFingerprint(fingerprint: string[]): this {\n    this._fingerprint = fingerprint;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setLevel(\n    // eslint-disable-next-line deprecation/deprecation\n    level: Severity | SeverityLevel,\n  ): this {\n    this._level = level;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTransactionName(name?: string): this {\n    this._transactionName = name;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setContext(key: string, context: Context | null): this {\n    if (context === null) {\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this._contexts[key];\n    } else {\n      this._contexts[key] = context;\n    }\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setSpan(span?: Span): this {\n    this._span = span;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getSpan(): Span | undefined {\n    return this._span;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getTransaction(): Transaction | undefined {\n    // Often, this span (if it exists at all) will be a transaction, but it's not guaranteed to be. Regardless, it will\n    // have a pointer to the currently-active transaction.\n    const span = this.getSpan();\n    return span && span.transaction;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setSession(session?: Session): this {\n    if (!session) {\n      delete this._session;\n    } else {\n      this._session = session;\n    }\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getSession(): Session | undefined {\n    return this._session;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public update(captureContext?: CaptureContext): this {\n    if (!captureContext) {\n      return this;\n    }\n\n    if (typeof captureContext === 'function') {\n      const updatedScope = (captureContext as <T>(scope: T) => T)(this);\n      return updatedScope instanceof Scope ? updatedScope : this;\n    }\n\n    if (captureContext instanceof Scope) {\n      this._tags = { ...this._tags, ...captureContext._tags };\n      this._extra = { ...this._extra, ...captureContext._extra };\n      this._contexts = { ...this._contexts, ...captureContext._contexts };\n      if (captureContext._user && Object.keys(captureContext._user).length) {\n        this._user = captureContext._user;\n      }\n      if (captureContext._level) {\n        this._level = captureContext._level;\n      }\n      if (captureContext._fingerprint) {\n        this._fingerprint = captureContext._fingerprint;\n      }\n      if (captureContext._requestSession) {\n        this._requestSession = captureContext._requestSession;\n      }\n    } else if (isPlainObject(captureContext)) {\n      // eslint-disable-next-line no-param-reassign\n      captureContext = captureContext as ScopeContext;\n      this._tags = { ...this._tags, ...captureContext.tags };\n      this._extra = { ...this._extra, ...captureContext.extra };\n      this._contexts = { ...this._contexts, ...captureContext.contexts };\n      if (captureContext.user) {\n        this._user = captureContext.user;\n      }\n      if (captureContext.level) {\n        this._level = captureContext.level;\n      }\n      if (captureContext.fingerprint) {\n        this._fingerprint = captureContext.fingerprint;\n      }\n      if (captureContext.requestSession) {\n        this._requestSession = captureContext.requestSession;\n      }\n    }\n\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clear(): this {\n    this._breadcrumbs = [];\n    this._tags = {};\n    this._extra = {};\n    this._user = {};\n    this._contexts = {};\n    this._level = undefined;\n    this._transactionName = undefined;\n    this._fingerprint = undefined;\n    this._requestSession = undefined;\n    this._span = undefined;\n    this._session = undefined;\n    this._notifyScopeListeners();\n    this._attachments = [];\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addBreadcrumb(breadcrumb: Breadcrumb, maxBreadcrumbs?: number): this {\n    const maxCrumbs = typeof maxBreadcrumbs === 'number' ? maxBreadcrumbs : DEFAULT_MAX_BREADCRUMBS;\n\n    // No data has been changed, so don't notify scope listeners\n    if (maxCrumbs <= 0) {\n      return this;\n    }\n\n    const mergedBreadcrumb = {\n      timestamp: dateTimestampInSeconds(),\n      ...breadcrumb,\n    };\n    this._breadcrumbs = [...this._breadcrumbs, mergedBreadcrumb].slice(-maxCrumbs);\n    this._notifyScopeListeners();\n\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clearBreadcrumbs(): this {\n    this._breadcrumbs = [];\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addAttachment(attachment: Attachment): this {\n    this._attachments.push(attachment);\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getAttachments(): Attachment[] {\n    return this._attachments;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clearAttachments(): this {\n    this._attachments = [];\n    return this;\n  }\n\n  /**\n   * Applies data from the scope to the event and runs all event processors on it.\n   *\n   * @param event Event\n   * @param hint Object containing additional information about the original exception, for use by the event processors.\n   * @hidden\n   */\n  public applyToEvent(event: Event, hint: EventHint = {}): PromiseLike<Event | null> {\n    if (this._extra && Object.keys(this._extra).length) {\n      event.extra = { ...this._extra, ...event.extra };\n    }\n    if (this._tags && Object.keys(this._tags).length) {\n      event.tags = { ...this._tags, ...event.tags };\n    }\n    if (this._user && Object.keys(this._user).length) {\n      event.user = { ...this._user, ...event.user };\n    }\n    if (this._contexts && Object.keys(this._contexts).length) {\n      event.contexts = { ...this._contexts, ...event.contexts };\n    }\n    if (this._level) {\n      event.level = this._level;\n    }\n    if (this._transactionName) {\n      event.transaction = this._transactionName;\n    }\n\n    // We want to set the trace context for normal events only if there isn't already\n    // a trace context on the event. There is a product feature in place where we link\n    // errors with transaction and it relies on that.\n    if (this._span) {\n      event.contexts = { trace: this._span.getTraceContext(), ...event.contexts };\n      const transactionName = this._span.transaction && this._span.transaction.name;\n      if (transactionName) {\n        event.tags = { transaction: transactionName, ...event.tags };\n      }\n    }\n\n    this._applyFingerprint(event);\n\n    event.breadcrumbs = [...(event.breadcrumbs || []), ...this._breadcrumbs];\n    event.breadcrumbs = event.breadcrumbs.length > 0 ? event.breadcrumbs : undefined;\n\n    event.sdkProcessingMetadata = { ...event.sdkProcessingMetadata, ...this._sdkProcessingMetadata };\n\n    return this._notifyEventProcessors([...getGlobalEventProcessors(), ...this._eventProcessors], event, hint);\n  }\n\n  /**\n   * Add data which will be accessible during event processing but won't get sent to Sentry\n   */\n  public setSDKProcessingMetadata(newData: { [key: string]: unknown }): this {\n    this._sdkProcessingMetadata = { ...this._sdkProcessingMetadata, ...newData };\n\n    return this;\n  }\n\n  /**\n   * This will be called after {@link applyToEvent} is finished.\n   */\n  protected _notifyEventProcessors(\n    processors: EventProcessor[],\n    event: Event | null,\n    hint: EventHint,\n    index: number = 0,\n  ): PromiseLike<Event | null> {\n    return new SyncPromise<Event | null>((resolve, reject) => {\n      const processor = processors[index];\n      if (event === null || typeof processor !== 'function') {\n        resolve(event);\n      } else {\n        const result = processor({ ...event }, hint) as Event | null;\n\n        __DEBUG_BUILD__ &&\n          processor.id &&\n          result === null &&\n          logger.log(`Event processor \"${processor.id}\" dropped event`);\n\n        if (isThenable(result)) {\n          void result\n            .then(final => this._notifyEventProcessors(processors, final, hint, index + 1).then(resolve))\n            .then(null, reject);\n        } else {\n          void this._notifyEventProcessors(processors, result, hint, index + 1)\n            .then(resolve)\n            .then(null, reject);\n        }\n      }\n    });\n  }\n\n  /**\n   * This will be called on every set call.\n   */\n  protected _notifyScopeListeners(): void {\n    // We need this check for this._notifyingListeners to be able to work on scope during updates\n    // If this check is not here we'll produce endless recursion when something is done with the scope\n    // during the callback.\n    if (!this._notifyingListeners) {\n      this._notifyingListeners = true;\n      this._scopeListeners.forEach(callback => {\n        callback(this);\n      });\n      this._notifyingListeners = false;\n    }\n  }\n\n  /**\n   * Applies fingerprint from the scope to the event if there's one,\n   * uses message if there's one instead or get rid of empty fingerprint\n   */\n  private _applyFingerprint(event: Event): void {\n    // Make sure it's an array first and we actually have something in place\n    event.fingerprint = event.fingerprint ? arrayify(event.fingerprint) : [];\n\n    // If we have something on the scope, then merge it with event\n    if (this._fingerprint) {\n      event.fingerprint = event.fingerprint.concat(this._fingerprint);\n    }\n\n    // If we have no data at all, remove empty array default\n    if (event.fingerprint && !event.fingerprint.length) {\n      delete event.fingerprint;\n    }\n  }\n}\n\n/**\n * Returns the global event processors.\n */\nfunction getGlobalEventProcessors(): EventProcessor[] {\n  return getGlobalSingleton<EventProcessor[]>('globalEventProcessors', () => []);\n}\n\n/**\n * Add a EventProcessor to be kept globally.\n * @param callback EventProcessor to add\n */\nexport function addGlobalEventProcessor(callback: EventProcessor): void {\n  getGlobalEventProcessors().push(callback);\n}\n", "/* eslint-disable max-lines */\nimport {\n  Breadcrumb,\n  BreadcrumbHint,\n  Client,\n  CustomSamplingContext,\n  Event,\n  EventHint,\n  Extra,\n  Extras,\n  Hub as HubInterface,\n  Integration,\n  IntegrationClass,\n  Primitive,\n  Session,\n  SessionContext,\n  Severity,\n  SeverityLevel,\n  Transaction,\n  TransactionContext,\n  User,\n} from '@sentry/types';\nimport {\n  consoleSandbox,\n  dateTimestampInSeconds,\n  getGlobalSingleton,\n  GLOBAL_OBJ,\n  isNodeEnv,\n  logger,\n} from '@sentry/utils';\n\nimport { Scope } from './scope';\nimport { closeSession, makeSession, updateSession } from './session';\n\nconst NIL_EVENT_ID = '00000000000000000000000000000000';\n\n/**\n * API compatibility version of this hub.\n *\n * WARNING: This number should only be increased when the global interface\n * changes and new methods are introduced.\n *\n * @hidden\n */\nexport const API_VERSION = 4;\n\n/**\n * Default maximum number of breadcrumbs added to an event. Can be overwritten\n * with {@link Options.maxBreadcrumbs}.\n */\nconst DEFAULT_BREADCRUMBS = 100;\n\n/**\n * A layer in the process stack.\n * @hidden\n */\nexport interface Layer {\n  client?: Client;\n  scope?: Scope;\n}\n\n/**\n * An object that contains a hub and maintains a scope stack.\n * @hidden\n */\nexport interface Carrier {\n  __SENTRY__?: {\n    hub?: Hub;\n    /**\n     * Extra Hub properties injected by various SDKs\n     */\n    integrations?: Integration[];\n    extensions?: {\n      /** Hack to prevent bundlers from breaking our usage of the domain package in the cross-platform Hub package */\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      domain?: { [key: string]: any };\n    } & {\n      /** Extension methods for the hub, which are bound to the current Hub instance */\n      // eslint-disable-next-line @typescript-eslint/ban-types\n      [key: string]: Function;\n    };\n  };\n}\n\n/**\n * @inheritDoc\n */\nexport class Hub implements HubInterface {\n  /** Is a {@link Layer}[] containing the client and scope */\n  private readonly _stack: Layer[] = [{}];\n\n  /** Contains the last event id of a captured event.  */\n  private _lastEventId?: string;\n\n  /**\n   * Creates a new instance of the hub, will push one {@link Layer} into the\n   * internal stack on creation.\n   *\n   * @param client bound to the hub.\n   * @param scope bound to the hub.\n   * @param version number, higher number means higher priority.\n   */\n  public constructor(client?: Client, scope: Scope = new Scope(), private readonly _version: number = API_VERSION) {\n    this.getStackTop().scope = scope;\n    if (client) {\n      this.bindClient(client);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public isOlderThan(version: number): boolean {\n    return this._version < version;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public bindClient(client?: Client): void {\n    const top = this.getStackTop();\n    top.client = client;\n    if (client && client.setupIntegrations) {\n      client.setupIntegrations();\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public pushScope(): Scope {\n    // We want to clone the content of prev scope\n    const scope = Scope.clone(this.getScope());\n    this.getStack().push({\n      client: this.getClient(),\n      scope,\n    });\n    return scope;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public popScope(): boolean {\n    if (this.getStack().length <= 1) return false;\n    return !!this.getStack().pop();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public withScope(callback: (scope: Scope) => void): void {\n    const scope = this.pushScope();\n    try {\n      callback(scope);\n    } finally {\n      this.popScope();\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getClient<C extends Client>(): C | undefined {\n    return this.getStackTop().client as C;\n  }\n\n  /** Returns the scope of the top stack. */\n  public getScope(): Scope | undefined {\n    return this.getStackTop().scope;\n  }\n\n  /** Returns the scope stack for domains or the process. */\n  public getStack(): Layer[] {\n    return this._stack;\n  }\n\n  /** Returns the topmost scope layer in the order domain > local > process. */\n  public getStackTop(): Layer {\n    return this._stack[this._stack.length - 1];\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public captureException(exception: any, hint?: EventHint): string {\n    const syntheticException = new Error('Sentry syntheticException');\n    this._lastEventId =\n      this._withClient((client, scope) => {\n        return client.captureException(\n          exception,\n          {\n            originalException: exception,\n            syntheticException,\n            ...hint,\n          },\n          scope,\n        );\n      }) || NIL_EVENT_ID;\n    return this._lastEventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureMessage(\n    message: string,\n    // eslint-disable-next-line deprecation/deprecation\n    level?: Severity | SeverityLevel,\n    hint?: EventHint,\n  ): string {\n    const syntheticException = new Error(message);\n    this._lastEventId =\n      this._withClient((client, scope) => {\n        return client.captureMessage(\n          message,\n          level,\n          {\n            originalException: message,\n            syntheticException,\n            ...hint,\n          },\n          scope,\n        );\n      }) || NIL_EVENT_ID;\n    return this._lastEventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureEvent(event: Event, hint?: EventHint): string {\n    const clientId =\n      this._withClient((client, scope) => {\n        return client.captureEvent(event, { ...hint }, scope);\n      }) || NIL_EVENT_ID;\n\n    if (event.type !== 'transaction') {\n      this._lastEventId = clientId;\n    }\n\n    return clientId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public lastEventId(): string | undefined {\n    return this._lastEventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addBreadcrumb(breadcrumb: Breadcrumb, hint?: BreadcrumbHint): void {\n    const { scope, client } = this.getStackTop();\n\n    if (!scope || !client) return;\n\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const { beforeBreadcrumb = null, maxBreadcrumbs = DEFAULT_BREADCRUMBS } =\n      (client.getOptions && client.getOptions()) || {};\n\n    if (maxBreadcrumbs <= 0) return;\n\n    const timestamp = dateTimestampInSeconds();\n    const mergedBreadcrumb = { timestamp, ...breadcrumb };\n    const finalBreadcrumb = beforeBreadcrumb\n      ? (consoleSandbox(() => beforeBreadcrumb(mergedBreadcrumb, hint)) as Breadcrumb | null)\n      : mergedBreadcrumb;\n\n    if (finalBreadcrumb === null) return;\n\n    scope.addBreadcrumb(finalBreadcrumb, maxBreadcrumbs);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setUser(user: User | null): void {\n    const scope = this.getScope();\n    if (scope) scope.setUser(user);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTags(tags: { [key: string]: Primitive }): void {\n    const scope = this.getScope();\n    if (scope) scope.setTags(tags);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtras(extras: Extras): void {\n    const scope = this.getScope();\n    if (scope) scope.setExtras(extras);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTag(key: string, value: Primitive): void {\n    const scope = this.getScope();\n    if (scope) scope.setTag(key, value);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtra(key: string, extra: Extra): void {\n    const scope = this.getScope();\n    if (scope) scope.setExtra(key, extra);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  public setContext(name: string, context: { [key: string]: any } | null): void {\n    const scope = this.getScope();\n    if (scope) scope.setContext(name, context);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public configureScope(callback: (scope: Scope) => void): void {\n    const { scope, client } = this.getStackTop();\n    if (scope && client) {\n      callback(scope);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public run(callback: (hub: Hub) => void): void {\n    const oldHub = makeMain(this);\n    try {\n      callback(this);\n    } finally {\n      makeMain(oldHub);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getIntegration<T extends Integration>(integration: IntegrationClass<T>): T | null {\n    const client = this.getClient();\n    if (!client) return null;\n    try {\n      return client.getIntegration(integration);\n    } catch (_oO) {\n      __DEBUG_BUILD__ && logger.warn(`Cannot retrieve integration ${integration.id} from the current Hub`);\n      return null;\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public startTransaction(context: TransactionContext, customSamplingContext?: CustomSamplingContext): Transaction {\n    return this._callExtensionMethod('startTransaction', context, customSamplingContext);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public traceHeaders(): { [key: string]: string } {\n    return this._callExtensionMethod<{ [key: string]: string }>('traceHeaders');\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureSession(endSession: boolean = false): void {\n    // both send the update and pull the session from the scope\n    if (endSession) {\n      return this.endSession();\n    }\n\n    // only send the update\n    this._sendSessionUpdate();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public endSession(): void {\n    const layer = this.getStackTop();\n    const scope = layer && layer.scope;\n    const session = scope && scope.getSession();\n    if (session) {\n      closeSession(session);\n    }\n    this._sendSessionUpdate();\n\n    // the session is over; take it off of the scope\n    if (scope) {\n      scope.setSession();\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public startSession(context?: SessionContext): Session {\n    const { scope, client } = this.getStackTop();\n    const { release, environment } = (client && client.getOptions()) || {};\n\n    // Will fetch userAgent if called from browser sdk\n    const { userAgent } = GLOBAL_OBJ.navigator || {};\n\n    const session = makeSession({\n      release,\n      environment,\n      ...(scope && { user: scope.getUser() }),\n      ...(userAgent && { userAgent }),\n      ...context,\n    });\n\n    if (scope) {\n      // End existing session if there's one\n      const currentSession = scope.getSession && scope.getSession();\n      if (currentSession && currentSession.status === 'ok') {\n        updateSession(currentSession, { status: 'exited' });\n      }\n      this.endSession();\n\n      // Afterwards we set the new session on the scope\n      scope.setSession(session);\n    }\n\n    return session;\n  }\n\n  /**\n   * Returns if default PII should be sent to Sentry and propagated in ourgoing requests\n   * when Tracing is used.\n   */\n  public shouldSendDefaultPii(): boolean {\n    const client = this.getClient();\n    const options = client && client.getOptions();\n    return Boolean(options && options.sendDefaultPii);\n  }\n\n  /**\n   * Sends the current Session on the scope\n   */\n  private _sendSessionUpdate(): void {\n    const { scope, client } = this.getStackTop();\n    if (!scope) return;\n\n    const session = scope.getSession();\n    if (session) {\n      if (client && client.captureSession) {\n        client.captureSession(session);\n      }\n    }\n  }\n\n  /**\n   * Internal helper function to call a method on the top client if it exists.\n   *\n   * @param method The method to call on the client.\n   * @param args Arguments to pass to the client function.\n   */\n  private _withClient<T>(callback: (client: Client, scope: Scope | undefined) => T): T | undefined {\n    const { scope, client } = this.getStackTop();\n    return client && callback(client, scope);\n  }\n\n  /**\n   * Calls global extension method and binding current instance to the function call\n   */\n  // @ts-ignore Function lacks ending return statement and return type does not include 'undefined'. ts(2366)\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _callExtensionMethod<T>(method: string, ...args: any[]): T {\n    const carrier = getMainCarrier();\n    const sentry = carrier.__SENTRY__;\n    if (sentry && sentry.extensions && typeof sentry.extensions[method] === 'function') {\n      return sentry.extensions[method].apply(this, args);\n    }\n    __DEBUG_BUILD__ && logger.warn(`Extension method ${method} couldn't be found, doing nothing.`);\n  }\n}\n\n/**\n * Returns the global shim registry.\n *\n * FIXME: This function is problematic, because despite always returning a valid Carrier,\n * it has an optional `__SENTRY__` property, which then in turn requires us to always perform an unnecessary check\n * at the call-site. We always access the carrier through this function, so we can guarantee that `__SENTRY__` is there.\n **/\nexport function getMainCarrier(): Carrier {\n  GLOBAL_OBJ.__SENTRY__ = GLOBAL_OBJ.__SENTRY__ || {\n    extensions: {},\n    hub: undefined,\n  };\n  return GLOBAL_OBJ;\n}\n\n/**\n * Replaces the current main hub with the passed one on the global object\n *\n * @returns The old replaced hub\n */\nexport function makeMain(hub: Hub): Hub {\n  const registry = getMainCarrier();\n  const oldHub = getHubFromCarrier(registry);\n  setHubOnCarrier(registry, hub);\n  return oldHub;\n}\n\n/**\n * Returns the default hub instance.\n *\n * If a hub is already registered in the global carrier but this module\n * contains a more recent version, it replaces the registered version.\n * Otherwise, the currently registered hub will be returned.\n */\nexport function getCurrentHub(): Hub {\n  // Get main carrier (global for every environment)\n  const registry = getMainCarrier();\n\n  // If there's no hub, or its an old API, assign a new one\n  if (!hasHubOnCarrier(registry) || getHubFromCarrier(registry).isOlderThan(API_VERSION)) {\n    setHubOnCarrier(registry, new Hub());\n  }\n\n  // Prefer domains over global if they are there (applicable only to Node environment)\n  if (isNodeEnv()) {\n    return getHubFromActiveDomain(registry);\n  }\n  // Return hub that lives on a global object\n  return getHubFromCarrier(registry);\n}\n\n/**\n * Try to read the hub from an active domain, and fallback to the registry if one doesn't exist\n * @returns discovered hub\n */\nfunction getHubFromActiveDomain(registry: Carrier): Hub {\n  try {\n    const sentry = getMainCarrier().__SENTRY__;\n    const activeDomain = sentry && sentry.extensions && sentry.extensions.domain && sentry.extensions.domain.active;\n\n    // If there's no active domain, just return global hub\n    if (!activeDomain) {\n      return getHubFromCarrier(registry);\n    }\n\n    // If there's no hub on current domain, or it's an old API, assign a new one\n    if (!hasHubOnCarrier(activeDomain) || getHubFromCarrier(activeDomain).isOlderThan(API_VERSION)) {\n      const registryHubTopStack = getHubFromCarrier(registry).getStackTop();\n      setHubOnCarrier(activeDomain, new Hub(registryHubTopStack.client, Scope.clone(registryHubTopStack.scope)));\n    }\n\n    // Return hub that lives on a domain\n    return getHubFromCarrier(activeDomain);\n  } catch (_Oo) {\n    // Return hub that lives on a global object\n    return getHubFromCarrier(registry);\n  }\n}\n\n/**\n * This will tell whether a carrier has a hub on it or not\n * @param carrier object\n */\nfunction hasHubOnCarrier(carrier: Carrier): boolean {\n  return !!(carrier && carrier.__SENTRY__ && carrier.__SENTRY__.hub);\n}\n\n/**\n * This will create a new {@link Hub} and add to the passed object on\n * __SENTRY__.hub.\n * @param carrier object\n * @hidden\n */\nexport function getHubFromCarrier(carrier: Carrier): Hub {\n  return getGlobalSingleton<Hub>('hub', () => new Hub(), carrier);\n}\n\n/**\n * This will set passed {@link Hub} on the passed object's __SENTRY__.hub attribute\n * @param carrier object\n * @param hub Hub\n * @returns A boolean indicating success or failure\n */\nexport function setHubOnCarrier(carrier: Carrier, hub: Hub): boolean {\n  if (!carrier) return false;\n  const __SENTRY__ = (carrier.__SENTRY__ = carrier.__SENTRY__ || {});\n  __SENTRY__.hub = hub;\n  return true;\n}\n", "import { ClientOptions, DsnComponents, Dsn<PERSON>ike, SdkInfo } from '@sentry/types';\nimport { dsnToString, makeDsn, urlEncode } from '@sentry/utils';\n\nconst SENTRY_API_VERSION = '7';\n\n/** Returns the prefix to construct Sentry ingestion API endpoints. */\nfunction getBaseApiEndpoint(dsn: DsnComponents): string {\n  const protocol = dsn.protocol ? `${dsn.protocol}:` : '';\n  const port = dsn.port ? `:${dsn.port}` : '';\n  return `${protocol}//${dsn.host}${port}${dsn.path ? `/${dsn.path}` : ''}/api/`;\n}\n\n/** Returns the ingest API endpoint for target. */\nfunction _getIngestEndpoint(dsn: DsnComponents): string {\n  return `${getBaseApiEndpoint(dsn)}${dsn.projectId}/envelope/`;\n}\n\n/** Returns a URL-encoded string with auth config suitable for a query string. */\nfunction _encodedAuth(dsn: DsnComponents, sdkInfo: SdkInfo | undefined): string {\n  return urlEncode({\n    // We send only the minimum set of required information. See\n    // https://github.com/getsentry/sentry-javascript/issues/2572.\n    sentry_key: dsn.publicKey,\n    sentry_version: SENTRY_API_VERSION,\n    ...(sdkInfo && { sentry_client: `${sdkInfo.name}/${sdkInfo.version}` }),\n  });\n}\n\n/**\n * Returns the envelope endpoint URL with auth in the query string.\n *\n * Sending auth as part of the query string and not as custom HTTP headers avoids CORS preflight requests.\n */\nexport function getEnvelopeEndpointWithUrlEncodedAuth(\n  dsn: DsnComponents,\n  // TODO (v8): Remove `tunnelOrOptions` in favor of `options`, and use the substitute code below\n  // options: ClientOptions = {} as ClientOptions,\n  tunnelOrOptions: string | ClientOptions = {} as ClientOptions,\n): string {\n  // TODO (v8): Use this code instead\n  // const { tunnel, _metadata = {} } = options;\n  // return tunnel ? tunnel : `${_getIngestEndpoint(dsn)}?${_encodedAuth(dsn, _metadata.sdk)}`;\n\n  const tunnel = typeof tunnelOrOptions === 'string' ? tunnelOrOptions : tunnelOrOptions.tunnel;\n  const sdkInfo =\n    typeof tunnelOrOptions === 'string' || !tunnelOrOptions._metadata ? undefined : tunnelOrOptions._metadata.sdk;\n\n  return tunnel ? tunnel : `${_getIngestEndpoint(dsn)}?${_encodedAuth(dsn, sdkInfo)}`;\n}\n\n/** Returns the url to the report dialog endpoint. */\nexport function getReportDialogEndpoint(\n  dsnLike: DsnLike,\n  dialogOptions: {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    [key: string]: any;\n    user?: { name?: string; email?: string };\n  },\n): string {\n  const dsn = makeDsn(dsnLike);\n  const endpoint = `${getBaseApiEndpoint(dsn)}embed/error-page/`;\n\n  let encodedOptions = `dsn=${dsnToString(dsn)}`;\n  for (const key in dialogOptions) {\n    if (key === 'dsn') {\n      continue;\n    }\n\n    if (key === 'user') {\n      const user = dialogOptions.user;\n      if (!user) {\n        continue;\n      }\n      if (user.name) {\n        encodedOptions += `&name=${encodeURIComponent(user.name)}`;\n      }\n      if (user.email) {\n        encodedOptions += `&email=${encodeURIComponent(user.email)}`;\n      }\n    } else {\n      encodedOptions += `&${encodeURIComponent(key)}=${encodeURIComponent(dialogOptions[key] as string)}`;\n    }\n  }\n\n  return `${endpoint}?${encodedOptions}`;\n}\n", "import {\n  DsnComponents,\n  Event,\n  EventEnvelope,\n  EventEnvelopeHeaders,\n  EventItem,\n  SdkInfo,\n  SdkMetadata,\n  Session,\n  SessionAggregates,\n  SessionEnvelope,\n  SessionItem,\n} from '@sentry/types';\nimport { createEnvelope, dropUndefinedKeys, dsnToString } from '@sentry/utils';\n\n/** Extract sdk info from from the API metadata */\nfunction getSdkMetadataForEnvelopeHeader(metadata?: SdkMetadata): SdkInfo | undefined {\n  if (!metadata || !metadata.sdk) {\n    return;\n  }\n  const { name, version } = metadata.sdk;\n  return { name, version };\n}\n\n/**\n * Apply SdkInfo (name, version, packages, integrations) to the corresponding event key.\n * Merge with existing data if any.\n **/\nfunction enhanceEventWithSdkInfo(event: Event, sdkInfo?: SdkInfo): Event {\n  if (!sdkInfo) {\n    return event;\n  }\n  event.sdk = event.sdk || {};\n  event.sdk.name = event.sdk.name || sdkInfo.name;\n  event.sdk.version = event.sdk.version || sdkInfo.version;\n  event.sdk.integrations = [...(event.sdk.integrations || []), ...(sdkInfo.integrations || [])];\n  event.sdk.packages = [...(event.sdk.packages || []), ...(sdkInfo.packages || [])];\n  return event;\n}\n\n/** Creates an envelope from a Session */\nexport function createSessionEnvelope(\n  session: Session | SessionAggregates,\n  dsn: DsnComponents,\n  metadata?: SdkMetadata,\n  tunnel?: string,\n): SessionEnvelope {\n  const sdkInfo = getSdkMetadataForEnvelopeHeader(metadata);\n  const envelopeHeaders = {\n    sent_at: new Date().toISOString(),\n    ...(sdkInfo && { sdk: sdkInfo }),\n    ...(!!tunnel && { dsn: dsnToString(dsn) }),\n  };\n\n  const envelopeItem: SessionItem =\n    'aggregates' in session ? [{ type: 'sessions' }, session] : [{ type: 'session' }, session];\n\n  return createEnvelope<SessionEnvelope>(envelopeHeaders, [envelopeItem]);\n}\n\n/**\n * Create an Envelope from an event.\n */\nexport function createEventEnvelope(\n  event: Event,\n  dsn: DsnComponents,\n  metadata?: SdkMetadata,\n  tunnel?: string,\n): EventEnvelope {\n  const sdkInfo = getSdkMetadataForEnvelopeHeader(metadata);\n  const eventType = event.type || 'event';\n\n  enhanceEventWithSdkInfo(event, metadata && metadata.sdk);\n\n  const envelopeHeaders = createEventEnvelopeHeaders(event, sdkInfo, tunnel, dsn);\n\n  // Prevent this data (which, if it exists, was used in earlier steps in the processing pipeline) from being sent to\n  // sentry. (Note: Our use of this property comes and goes with whatever we might be debugging, whatever hacks we may\n  // have temporarily added, etc. Even if we don't happen to be using it at some point in the future, let's not get rid\n  // of this `delete`, lest we miss putting it back in the next time the property is in use.)\n  delete event.sdkProcessingMetadata;\n\n  const eventItem: EventItem = [{ type: eventType }, event];\n  return createEnvelope<EventEnvelope>(envelopeHeaders, [eventItem]);\n}\n\nfunction createEventEnvelopeHeaders(\n  event: Event,\n  sdkInfo: SdkInfo | undefined,\n  tunnel: string | undefined,\n  dsn: DsnComponents,\n): EventEnvelopeHeaders {\n  const dynamicSamplingContext = event.sdkProcessingMetadata && event.sdkProcessingMetadata.dynamicSamplingContext;\n\n  return {\n    event_id: event.event_id as string,\n    sent_at: new Date().toISOString(),\n    ...(sdkInfo && { sdk: sdkInfo }),\n    ...(!!tunnel && { dsn: dsnToString(dsn) }),\n    ...(event.type === 'transaction' &&\n      dynamicSamplingContext && {\n        trace: dropUndefinedKeys({ ...dynamicSamplingContext }),\n      }),\n  };\n}\n", "import { Integration, Options } from '@sentry/types';\nimport { arrayify, logger } from '@sentry/utils';\n\nimport { getCurrentHub } from './hub';\nimport { addGlobalEventProcessor } from './scope';\n\ndeclare module '@sentry/types' {\n  interface Integration {\n    isDefaultInstance?: boolean;\n  }\n}\n\nexport const installedIntegrations: string[] = [];\n\n/** Map of integrations assigned to a client */\nexport type IntegrationIndex = {\n  [key: string]: Integration;\n};\n\n/**\n * Remove duplicates from the given array, preferring the last instance of any duplicate. Not guaranteed to\n * preseve the order of integrations in the array.\n *\n * @private\n */\nfunction filterDuplicates(integrations: Integration[]): Integration[] {\n  const integrationsByName: { [key: string]: Integration } = {};\n\n  integrations.forEach(currentInstance => {\n    const { name } = currentInstance;\n\n    const existingInstance = integrationsByName[name];\n\n    // We want integrations later in the array to overwrite earlier ones of the same type, except that we never want a\n    // default instance to overwrite an existing user instance\n    if (existingInstance && !existingInstance.isDefaultInstance && currentInstance.isDefaultInstance) {\n      return;\n    }\n\n    integrationsByName[name] = currentInstance;\n  });\n\n  return Object.values(integrationsByName);\n}\n\n/** Gets integrations to install */\nexport function getIntegrationsToSetup(options: Options): Integration[] {\n  const defaultIntegrations = options.defaultIntegrations || [];\n  const userIntegrations = options.integrations;\n\n  // We flag default instances, so that later we can tell them apart from any user-created instances of the same class\n  defaultIntegrations.forEach(integration => {\n    integration.isDefaultInstance = true;\n  });\n\n  let integrations: Integration[];\n\n  if (Array.isArray(userIntegrations)) {\n    integrations = [...defaultIntegrations, ...userIntegrations];\n  } else if (typeof userIntegrations === 'function') {\n    integrations = arrayify(userIntegrations(defaultIntegrations));\n  } else {\n    integrations = defaultIntegrations;\n  }\n\n  const finalIntegrations = filterDuplicates(integrations);\n\n  // The `Debug` integration prints copies of the `event` and `hint` which will be passed to `beforeSend` or\n  // `beforeSendTransaction`. It therefore has to run after all other integrations, so that the changes of all event\n  // processors will be reflected in the printed values. For lack of a more elegant way to guarantee that, we therefore\n  // locate it and, assuming it exists, pop it out of its current spot and shove it onto the end of the array.\n  const debugIndex = finalIntegrations.findIndex(integration => integration.name === 'Debug');\n  if (debugIndex !== -1) {\n    const [debugInstance] = finalIntegrations.splice(debugIndex, 1);\n    finalIntegrations.push(debugInstance);\n  }\n\n  return finalIntegrations;\n}\n\n/**\n * Given a list of integration instances this installs them all. When `withDefaults` is set to `true` then all default\n * integrations are added unless they were already provided before.\n * @param integrations array of integration instances\n * @param withDefault should enable default integrations\n */\nexport function setupIntegrations(integrations: Integration[]): IntegrationIndex {\n  const integrationIndex: IntegrationIndex = {};\n\n  integrations.forEach(integration => {\n    integrationIndex[integration.name] = integration;\n\n    if (installedIntegrations.indexOf(integration.name) === -1) {\n      integration.setupOnce(addGlobalEventProcessor, getCurrentHub);\n      installedIntegrations.push(integration.name);\n      __DEBUG_BUILD__ && logger.log(`Integration installed: ${integration.name}`);\n    }\n  });\n\n  return integrationIndex;\n}\n", "/* eslint-disable max-lines */\nimport {\n  Client,\n  ClientOptions,\n  DataCategory,\n  DsnComponents,\n  Envelope,\n  Event,\n  EventDropReason,\n  EventHint,\n  Integration,\n  IntegrationClass,\n  Outcome,\n  Session,\n  SessionAggregates,\n  Severity,\n  SeverityLevel,\n  Transport,\n} from '@sentry/types';\nimport {\n  addItemToEnvelope,\n  checkOrSetAlreadyCaught,\n  createAttachmentEnvelopeItem,\n  dateTimestampInSeconds,\n  isPlainObject,\n  isPrimitive,\n  isThenable,\n  logger,\n  makeDsn,\n  normalize,\n  rejectedSyncPromise,\n  resolvedSyncPromise,\n  SentryError,\n  SyncPromise,\n  truncate,\n  uuid4,\n} from '@sentry/utils';\n\nimport { getEnvelopeEndpointWithUrlEncodedAuth } from './api';\nimport { createEventEnvelope, createSessionEnvelope } from './envelope';\nimport { IntegrationIndex, setupIntegrations } from './integration';\nimport { Scope } from './scope';\nimport { updateSession } from './session';\n\nconst ALREADY_SEEN_ERROR = \"Not capturing exception because it's already been captured.\";\n\n/**\n * Base implementation for all JavaScript SDK clients.\n *\n * Call the constructor with the corresponding options\n * specific to the client subclass. To access these options later, use\n * {@link Client.getOptions}.\n *\n * If a Dsn is specified in the options, it will be parsed and stored. Use\n * {@link Client.getDsn} to retrieve the Dsn at any moment. In case the Dsn is\n * invalid, the constructor will throw a {@link SentryException}. Note that\n * without a valid Dsn, the SDK will not send any events to Sentry.\n *\n * Before sending an event, it is passed through\n * {@link BaseClient._prepareEvent} to add SDK information and scope data\n * (breadcrumbs and context). To add more custom information, override this\n * method and extend the resulting prepared event.\n *\n * To issue automatically created events (e.g. via instrumentation), use\n * {@link Client.captureEvent}. It will prepare the event and pass it through\n * the callback lifecycle. To issue auto-breadcrumbs, use\n * {@link Client.addBreadcrumb}.\n *\n * @example\n * class NodeClient extends BaseClient<NodeOptions> {\n *   public constructor(options: NodeOptions) {\n *     super(options);\n *   }\n *\n *   // ...\n * }\n */\nexport abstract class BaseClient<O extends ClientOptions> implements Client<O> {\n  /** Options passed to the SDK. */\n  protected readonly _options: O;\n\n  /** The client Dsn, if specified in options. Without this Dsn, the SDK will be disabled. */\n  protected readonly _dsn?: DsnComponents;\n\n  protected readonly _transport?: Transport;\n\n  /** Array of set up integrations. */\n  protected _integrations: IntegrationIndex = {};\n\n  /** Indicates whether this client's integrations have been set up. */\n  protected _integrationsInitialized: boolean = false;\n\n  /** Number of calls being processed */\n  protected _numProcessing: number = 0;\n\n  /** Holds flushable  */\n  private _outcomes: { [key: string]: number } = {};\n\n  /**\n   * Initializes this client instance.\n   *\n   * @param options Options for the client.\n   */\n  protected constructor(options: O) {\n    this._options = options;\n    if (options.dsn) {\n      this._dsn = makeDsn(options.dsn);\n      const url = getEnvelopeEndpointWithUrlEncodedAuth(this._dsn, options);\n      this._transport = options.transport({\n        recordDroppedEvent: this.recordDroppedEvent.bind(this),\n        ...options.transportOptions,\n        url,\n      });\n    } else {\n      __DEBUG_BUILD__ && logger.warn('No DSN provided, client will not do anything.');\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public captureException(exception: any, hint?: EventHint, scope?: Scope): string | undefined {\n    // ensure we haven't captured this very object before\n    if (checkOrSetAlreadyCaught(exception)) {\n      __DEBUG_BUILD__ && logger.log(ALREADY_SEEN_ERROR);\n      return;\n    }\n\n    let eventId: string | undefined;\n    this._process(\n      this.eventFromException(exception, hint)\n        .then(event => this._captureEvent(event, hint, scope))\n        .then(result => {\n          eventId = result;\n        }),\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureMessage(\n    message: string,\n    // eslint-disable-next-line deprecation/deprecation\n    level?: Severity | SeverityLevel,\n    hint?: EventHint,\n    scope?: Scope,\n  ): string | undefined {\n    let eventId: string | undefined;\n\n    const promisedEvent = isPrimitive(message)\n      ? this.eventFromMessage(String(message), level, hint)\n      : this.eventFromException(message, hint);\n\n    this._process(\n      promisedEvent\n        .then(event => this._captureEvent(event, hint, scope))\n        .then(result => {\n          eventId = result;\n        }),\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureEvent(event: Event, hint?: EventHint, scope?: Scope): string | undefined {\n    // ensure we haven't captured this very object before\n    if (hint && hint.originalException && checkOrSetAlreadyCaught(hint.originalException)) {\n      __DEBUG_BUILD__ && logger.log(ALREADY_SEEN_ERROR);\n      return;\n    }\n\n    let eventId: string | undefined;\n\n    this._process(\n      this._captureEvent(event, hint, scope).then(result => {\n        eventId = result;\n      }),\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureSession(session: Session): void {\n    if (!this._isEnabled()) {\n      __DEBUG_BUILD__ && logger.warn('SDK not enabled, will not capture session.');\n      return;\n    }\n\n    if (!(typeof session.release === 'string')) {\n      __DEBUG_BUILD__ && logger.warn('Discarded session because of missing or non-string release');\n    } else {\n      this.sendSession(session);\n      // After sending, we set init false to indicate it's not the first occurrence\n      updateSession(session, { init: false });\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getDsn(): DsnComponents | undefined {\n    return this._dsn;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getOptions(): O {\n    return this._options;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getTransport(): Transport | undefined {\n    return this._transport;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public flush(timeout?: number): PromiseLike<boolean> {\n    const transport = this._transport;\n    if (transport) {\n      return this._isClientDoneProcessing(timeout).then(clientFinished => {\n        return transport.flush(timeout).then(transportFlushed => clientFinished && transportFlushed);\n      });\n    } else {\n      return resolvedSyncPromise(true);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public close(timeout?: number): PromiseLike<boolean> {\n    return this.flush(timeout).then(result => {\n      this.getOptions().enabled = false;\n      return result;\n    });\n  }\n\n  /**\n   * Sets up the integrations\n   */\n  public setupIntegrations(): void {\n    if (this._isEnabled() && !this._integrationsInitialized) {\n      this._integrations = setupIntegrations(this._options.integrations);\n      this._integrationsInitialized = true;\n    }\n  }\n\n  /**\n   * Gets an installed integration by its `id`.\n   *\n   * @returns The installed integration or `undefined` if no integration with that `id` was installed.\n   */\n  public getIntegrationById(integrationId: string): Integration | undefined {\n    return this._integrations[integrationId];\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getIntegration<T extends Integration>(integration: IntegrationClass<T>): T | null {\n    try {\n      return (this._integrations[integration.id] as T) || null;\n    } catch (_oO) {\n      __DEBUG_BUILD__ && logger.warn(`Cannot retrieve integration ${integration.id} from the current Client`);\n      return null;\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public sendEvent(event: Event, hint: EventHint = {}): void {\n    if (this._dsn) {\n      let env = createEventEnvelope(event, this._dsn, this._options._metadata, this._options.tunnel);\n\n      for (const attachment of hint.attachments || []) {\n        env = addItemToEnvelope(\n          env,\n          createAttachmentEnvelopeItem(\n            attachment,\n            this._options.transportOptions && this._options.transportOptions.textEncoder,\n          ),\n        );\n      }\n\n      this._sendEnvelope(env);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public sendSession(session: Session | SessionAggregates): void {\n    if (this._dsn) {\n      const env = createSessionEnvelope(session, this._dsn, this._options._metadata, this._options.tunnel);\n      this._sendEnvelope(env);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public recordDroppedEvent(reason: EventDropReason, category: DataCategory, _event?: Event): void {\n    // Note: we use `event` in replay, where we overwrite this hook.\n\n    if (this._options.sendClientReports) {\n      // We want to track each category (error, transaction, session) separately\n      // but still keep the distinction between different type of outcomes.\n      // We could use nested maps, but it's much easier to read and type this way.\n      // A correct type for map-based implementation if we want to go that route\n      // would be `Partial<Record<SentryRequestType, Partial<Record<Outcome, number>>>>`\n      // With typescript 4.1 we could even use template literal types\n      const key = `${reason}:${category}`;\n      __DEBUG_BUILD__ && logger.log(`Adding outcome: \"${key}\"`);\n\n      // The following works because undefined + 1 === NaN and NaN is falsy\n      this._outcomes[key] = this._outcomes[key] + 1 || 1;\n    }\n  }\n\n  /** Updates existing session based on the provided event */\n  protected _updateSessionFromEvent(session: Session, event: Event): void {\n    let crashed = false;\n    let errored = false;\n    const exceptions = event.exception && event.exception.values;\n\n    if (exceptions) {\n      errored = true;\n\n      for (const ex of exceptions) {\n        const mechanism = ex.mechanism;\n        if (mechanism && mechanism.handled === false) {\n          crashed = true;\n          break;\n        }\n      }\n    }\n\n    // A session is updated and that session update is sent in only one of the two following scenarios:\n    // 1. Session with non terminal status and 0 errors + an error occurred -> Will set error count to 1 and send update\n    // 2. Session with non terminal status and 1 error + a crash occurred -> Will set status crashed and send update\n    const sessionNonTerminal = session.status === 'ok';\n    const shouldUpdateAndSend = (sessionNonTerminal && session.errors === 0) || (sessionNonTerminal && crashed);\n\n    if (shouldUpdateAndSend) {\n      updateSession(session, {\n        ...(crashed && { status: 'crashed' }),\n        errors: session.errors || Number(errored || crashed),\n      });\n      this.captureSession(session);\n    }\n  }\n\n  /**\n   * Determine if the client is finished processing. Returns a promise because it will wait `timeout` ms before saying\n   * \"no\" (resolving to `false`) in order to give the client a chance to potentially finish first.\n   *\n   * @param timeout The time, in ms, after which to resolve to `false` if the client is still busy. Passing `0` (or not\n   * passing anything) will make the promise wait as long as it takes for processing to finish before resolving to\n   * `true`.\n   * @returns A promise which will resolve to `true` if processing is already done or finishes before the timeout, and\n   * `false` otherwise\n   */\n  protected _isClientDoneProcessing(timeout?: number): PromiseLike<boolean> {\n    return new SyncPromise(resolve => {\n      let ticked: number = 0;\n      const tick: number = 1;\n\n      const interval = setInterval(() => {\n        if (this._numProcessing == 0) {\n          clearInterval(interval);\n          resolve(true);\n        } else {\n          ticked += tick;\n          if (timeout && ticked >= timeout) {\n            clearInterval(interval);\n            resolve(false);\n          }\n        }\n      }, tick);\n    });\n  }\n\n  /** Determines whether this SDK is enabled and a valid Dsn is present. */\n  protected _isEnabled(): boolean {\n    return this.getOptions().enabled !== false && this._dsn !== undefined;\n  }\n\n  /**\n   * Adds common information to events.\n   *\n   * The information includes release and environment from `options`,\n   * breadcrumbs and context (extra, tags and user) from the scope.\n   *\n   * Information that is already present in the event is never overwritten. For\n   * nested objects, such as the context, keys are merged.\n   *\n   * @param event The original event.\n   * @param hint May contain additional information about the original exception.\n   * @param scope A scope containing event metadata.\n   * @returns A new event with more information.\n   */\n  protected _prepareEvent(event: Event, hint: EventHint, scope?: Scope): PromiseLike<Event | null> {\n    const { normalizeDepth = 3, normalizeMaxBreadth = 1_000 } = this.getOptions();\n    const prepared: Event = {\n      ...event,\n      event_id: event.event_id || hint.event_id || uuid4(),\n      timestamp: event.timestamp || dateTimestampInSeconds(),\n    };\n\n    this._applyClientOptions(prepared);\n    this._applyIntegrationsMetadata(prepared);\n\n    // If we have scope given to us, use it as the base for further modifications.\n    // This allows us to prevent unnecessary copying of data if `captureContext` is not provided.\n    let finalScope = scope;\n    if (hint.captureContext) {\n      finalScope = Scope.clone(finalScope).update(hint.captureContext);\n    }\n\n    // We prepare the result here with a resolved Event.\n    let result = resolvedSyncPromise<Event | null>(prepared);\n\n    // This should be the last thing called, since we want that\n    // {@link Hub.addEventProcessor} gets the finished prepared event.\n    //\n    // We need to check for the existence of `finalScope.getAttachments`\n    // because `getAttachments` can be undefined if users are using an older version\n    // of `@sentry/core` that does not have the `getAttachments` method.\n    // See: https://github.com/getsentry/sentry-javascript/issues/5229\n    if (finalScope && finalScope.getAttachments) {\n      // Collect attachments from the hint and scope\n      const attachments = [...(hint.attachments || []), ...finalScope.getAttachments()];\n\n      if (attachments.length) {\n        hint.attachments = attachments;\n      }\n\n      // In case we have a hub we reassign it.\n      result = finalScope.applyToEvent(prepared, hint);\n    }\n\n    return result.then(evt => {\n      if (typeof normalizeDepth === 'number' && normalizeDepth > 0) {\n        return this._normalizeEvent(evt, normalizeDepth, normalizeMaxBreadth);\n      }\n      return evt;\n    });\n  }\n\n  /**\n   * Applies `normalize` function on necessary `Event` attributes to make them safe for serialization.\n   * Normalized keys:\n   * - `breadcrumbs.data`\n   * - `user`\n   * - `contexts`\n   * - `extra`\n   * @param event Event\n   * @returns Normalized event\n   */\n  protected _normalizeEvent(event: Event | null, depth: number, maxBreadth: number): Event | null {\n    if (!event) {\n      return null;\n    }\n\n    const normalized: Event = {\n      ...event,\n      ...(event.breadcrumbs && {\n        breadcrumbs: event.breadcrumbs.map(b => ({\n          ...b,\n          ...(b.data && {\n            data: normalize(b.data, depth, maxBreadth),\n          }),\n        })),\n      }),\n      ...(event.user && {\n        user: normalize(event.user, depth, maxBreadth),\n      }),\n      ...(event.contexts && {\n        contexts: normalize(event.contexts, depth, maxBreadth),\n      }),\n      ...(event.extra && {\n        extra: normalize(event.extra, depth, maxBreadth),\n      }),\n    };\n\n    // event.contexts.trace stores information about a Transaction. Similarly,\n    // event.spans[] stores information about child Spans. Given that a\n    // Transaction is conceptually a Span, normalization should apply to both\n    // Transactions and Spans consistently.\n    // For now the decision is to skip normalization of Transactions and Spans,\n    // so this block overwrites the normalized event to add back the original\n    // Transaction information prior to normalization.\n    if (event.contexts && event.contexts.trace && normalized.contexts) {\n      normalized.contexts.trace = event.contexts.trace;\n\n      // event.contexts.trace.data may contain circular/dangerous data so we need to normalize it\n      if (event.contexts.trace.data) {\n        normalized.contexts.trace.data = normalize(event.contexts.trace.data, depth, maxBreadth);\n      }\n    }\n\n    // event.spans[].data may contain circular/dangerous data so we need to normalize it\n    if (event.spans) {\n      normalized.spans = event.spans.map(span => {\n        // We cannot use the spread operator here because `toJSON` on `span` is non-enumerable\n        if (span.data) {\n          span.data = normalize(span.data, depth, maxBreadth);\n        }\n        return span;\n      });\n    }\n\n    return normalized;\n  }\n\n  /**\n   *  Enhances event using the client configuration.\n   *  It takes care of all \"static\" values like environment, release and `dist`,\n   *  as well as truncating overly long values.\n   * @param event event instance to be enhanced\n   */\n  protected _applyClientOptions(event: Event): void {\n    const options = this.getOptions();\n    const { environment, release, dist, maxValueLength = 250 } = options;\n\n    if (!('environment' in event)) {\n      event.environment = 'environment' in options ? environment : 'production';\n    }\n\n    if (event.release === undefined && release !== undefined) {\n      event.release = release;\n    }\n\n    if (event.dist === undefined && dist !== undefined) {\n      event.dist = dist;\n    }\n\n    if (event.message) {\n      event.message = truncate(event.message, maxValueLength);\n    }\n\n    const exception = event.exception && event.exception.values && event.exception.values[0];\n    if (exception && exception.value) {\n      exception.value = truncate(exception.value, maxValueLength);\n    }\n\n    const request = event.request;\n    if (request && request.url) {\n      request.url = truncate(request.url, maxValueLength);\n    }\n  }\n\n  /**\n   * This function adds all used integrations to the SDK info in the event.\n   * @param event The event that will be filled with all integrations.\n   */\n  protected _applyIntegrationsMetadata(event: Event): void {\n    const integrationsArray = Object.keys(this._integrations);\n    if (integrationsArray.length > 0) {\n      event.sdk = event.sdk || {};\n      event.sdk.integrations = [...(event.sdk.integrations || []), ...integrationsArray];\n    }\n  }\n\n  /**\n   * Processes the event and logs an error in case of rejection\n   * @param event\n   * @param hint\n   * @param scope\n   */\n  protected _captureEvent(event: Event, hint: EventHint = {}, scope?: Scope): PromiseLike<string | undefined> {\n    return this._processEvent(event, hint, scope).then(\n      finalEvent => {\n        return finalEvent.event_id;\n      },\n      reason => {\n        if (__DEBUG_BUILD__) {\n          // If something's gone wrong, log the error as a warning. If it's just us having used a `SentryError` for\n          // control flow, log just the message (no stack) as a log-level log.\n          const sentryError = reason as SentryError;\n          if (sentryError.logLevel === 'log') {\n            logger.log(sentryError.message);\n          } else {\n            logger.warn(sentryError);\n          }\n        }\n        return undefined;\n      },\n    );\n  }\n\n  /**\n   * Processes an event (either error or message) and sends it to Sentry.\n   *\n   * This also adds breadcrumbs and context information to the event. However,\n   * platform specific meta data (such as the User's IP address) must be added\n   * by the SDK implementor.\n   *\n   *\n   * @param event The event to send to Sentry.\n   * @param hint May contain additional information about the original exception.\n   * @param scope A scope containing event metadata.\n   * @returns A SyncPromise that resolves with the event or rejects in case event was/will not be send.\n   */\n  protected _processEvent(event: Event, hint: EventHint, scope?: Scope): PromiseLike<Event> {\n    const options = this.getOptions();\n    const { sampleRate } = options;\n\n    if (!this._isEnabled()) {\n      return rejectedSyncPromise(new SentryError('SDK not enabled, will not capture event.', 'log'));\n    }\n\n    const isTransaction = event.type === 'transaction';\n    const beforeSendProcessorName = isTransaction ? 'beforeSendTransaction' : 'beforeSend';\n    const beforeSendProcessor = options[beforeSendProcessorName];\n\n    // 1.0 === 100% events are sent\n    // 0.0 === 0% events are sent\n    // Sampling for transaction happens somewhere else\n    if (!isTransaction && typeof sampleRate === 'number' && Math.random() > sampleRate) {\n      this.recordDroppedEvent('sample_rate', 'error', event);\n      return rejectedSyncPromise(\n        new SentryError(\n          `Discarding event because it's not included in the random sample (sampling rate = ${sampleRate})`,\n          'log',\n        ),\n      );\n    }\n\n    return this._prepareEvent(event, hint, scope)\n      .then(prepared => {\n        if (prepared === null) {\n          this.recordDroppedEvent('event_processor', event.type || 'error', event);\n          throw new SentryError('An event processor returned `null`, will not send event.', 'log');\n        }\n\n        const isInternalException = hint.data && (hint.data as { __sentry__: boolean }).__sentry__ === true;\n        if (isInternalException || !beforeSendProcessor) {\n          return prepared;\n        }\n\n        const beforeSendResult = beforeSendProcessor(prepared, hint);\n        return _validateBeforeSendResult(beforeSendResult, beforeSendProcessorName);\n      })\n      .then(processedEvent => {\n        if (processedEvent === null) {\n          this.recordDroppedEvent('before_send', event.type || 'error', event);\n          throw new SentryError(`\\`${beforeSendProcessorName}\\` returned \\`null\\`, will not send event.`, 'log');\n        }\n\n        const session = scope && scope.getSession();\n        if (!isTransaction && session) {\n          this._updateSessionFromEvent(session, processedEvent);\n        }\n\n        // None of the Sentry built event processor will update transaction name,\n        // so if the transaction name has been changed by an event processor, we know\n        // it has to come from custom event processor added by a user\n        const transactionInfo = processedEvent.transaction_info;\n        if (isTransaction && transactionInfo && processedEvent.transaction !== event.transaction) {\n          const source = 'custom';\n          processedEvent.transaction_info = {\n            ...transactionInfo,\n            source,\n            changes: [\n              ...transactionInfo.changes,\n              {\n                source,\n                // use the same timestamp as the processed event.\n                timestamp: processedEvent.timestamp as number,\n                propagations: transactionInfo.propagations,\n              },\n            ],\n          };\n        }\n\n        this.sendEvent(processedEvent, hint);\n        return processedEvent;\n      })\n      .then(null, reason => {\n        if (reason instanceof SentryError) {\n          throw reason;\n        }\n\n        this.captureException(reason, {\n          data: {\n            __sentry__: true,\n          },\n          originalException: reason as Error,\n        });\n        throw new SentryError(\n          `Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\\nReason: ${reason}`,\n        );\n      });\n  }\n\n  /**\n   * Occupies the client with processing and event\n   */\n  protected _process<T>(promise: PromiseLike<T>): void {\n    this._numProcessing++;\n    void promise.then(\n      value => {\n        this._numProcessing--;\n        return value;\n      },\n      reason => {\n        this._numProcessing--;\n        return reason;\n      },\n    );\n  }\n\n  /**\n   * @inheritdoc\n   */\n  protected _sendEnvelope(envelope: Envelope): void {\n    if (this._transport && this._dsn) {\n      this._transport.send(envelope).then(null, reason => {\n        __DEBUG_BUILD__ && logger.error('Error while sending event:', reason);\n      });\n    } else {\n      __DEBUG_BUILD__ && logger.error('Transport disabled');\n    }\n  }\n\n  /**\n   * Clears outcomes on this client and returns them.\n   */\n  protected _clearOutcomes(): Outcome[] {\n    const outcomes = this._outcomes;\n    this._outcomes = {};\n    return Object.keys(outcomes).map(key => {\n      const [reason, category] = key.split(':') as [EventDropReason, DataCategory];\n      return {\n        reason,\n        category,\n        quantity: outcomes[key],\n      };\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public abstract eventFromException(_exception: any, _hint?: EventHint): PromiseLike<Event>;\n\n  /**\n   * @inheritDoc\n   */\n  public abstract eventFromMessage(\n    _message: string,\n    // eslint-disable-next-line deprecation/deprecation\n    _level?: Severity | SeverityLevel,\n    _hint?: EventHint,\n  ): PromiseLike<Event>;\n}\n\n/**\n * Verifies that return value of configured `beforeSend` or `beforeSendTransaction` is of expected type, and returns the value if so.\n */\nfunction _validateBeforeSendResult(\n  beforeSendResult: PromiseLike<Event | null> | Event | null,\n  beforeSendProcessorName: 'beforeSend' | 'beforeSendTransaction',\n): PromiseLike<Event | null> | Event | null {\n  const invalidValueError = `\\`${beforeSendProcessorName}\\` must return \\`null\\` or a valid event.`;\n  if (isThenable(beforeSendResult)) {\n    return beforeSendResult.then(\n      event => {\n        if (!isPlainObject(event) && event !== null) {\n          throw new SentryError(invalidValueError);\n        }\n        return event;\n      },\n      e => {\n        throw new SentryError(`\\`${beforeSendProcessorName}\\` rejected with ${e}`);\n      },\n    );\n  } else if (!isPlainObject(beforeSendResult) && beforeSendResult !== null) {\n    throw new SentryError(invalidValueError);\n  }\n  return beforeSendResult;\n}\n", "import {\n  Envelope,\n  EnvelopeItem,\n  EnvelopeItemType,\n  Event,\n  EventDropReason,\n  EventItem,\n  InternalBaseTransportOptions,\n  Transport,\n  TransportRequestExecutor,\n} from '@sentry/types';\nimport {\n  createEnvelope,\n  envelopeItemTypeToDataCategory,\n  forEachEnvelopeItem,\n  isRateLimited,\n  logger,\n  makePromiseBuffer,\n  PromiseBuffer,\n  RateLimits,\n  resolvedSyncPromise,\n  SentryError,\n  serializeEnvelope,\n  updateRateLimits,\n} from '@sentry/utils';\n\nexport const DEFAULT_TRANSPORT_BUFFER_SIZE = 30;\n\n/**\n * Creates an instance of a Sentry `Transport`\n *\n * @param options\n * @param makeRequest\n */\nexport function createTransport(\n  options: InternalBaseTransportOptions,\n  makeRequest: TransportRequestExecutor,\n  buffer: PromiseBuffer<void> = makePromiseBuffer(options.bufferSize || DEFAULT_TRANSPORT_BUFFER_SIZE),\n): Transport {\n  let rateLimits: RateLimits = {};\n\n  const flush = (timeout?: number): PromiseLike<boolean> => buffer.drain(timeout);\n\n  function send(envelope: Envelope): PromiseLike<void> {\n    const filteredEnvelopeItems: EnvelopeItem[] = [];\n\n    // Drop rate limited items from envelope\n    forEachEnvelopeItem(envelope, (item, type) => {\n      const envelopeItemDataCategory = envelopeItemTypeToDataCategory(type);\n      if (isRateLimited(rateLimits, envelopeItemDataCategory)) {\n        const event: Event | undefined = getEventForEnvelopeItem(item, type);\n        options.recordDroppedEvent('ratelimit_backoff', envelopeItemDataCategory, event);\n      } else {\n        filteredEnvelopeItems.push(item);\n      }\n    });\n\n    // Skip sending if envelope is empty after filtering out rate limited events\n    if (filteredEnvelopeItems.length === 0) {\n      return resolvedSyncPromise();\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const filteredEnvelope: Envelope = createEnvelope(envelope[0], filteredEnvelopeItems as any);\n\n    // Creates client report for each item in an envelope\n    const recordEnvelopeLoss = (reason: EventDropReason): void => {\n      forEachEnvelopeItem(filteredEnvelope, (item, type) => {\n        const event: Event | undefined = getEventForEnvelopeItem(item, type);\n        options.recordDroppedEvent(reason, envelopeItemTypeToDataCategory(type), event);\n      });\n    };\n\n    const requestTask = (): PromiseLike<void> =>\n      makeRequest({ body: serializeEnvelope(filteredEnvelope, options.textEncoder) }).then(\n        response => {\n          // We don't want to throw on NOK responses, but we want to at least log them\n          if (response.statusCode !== undefined && (response.statusCode < 200 || response.statusCode >= 300)) {\n            __DEBUG_BUILD__ && logger.warn(`Sentry responded with status code ${response.statusCode} to sent event.`);\n          }\n\n          rateLimits = updateRateLimits(rateLimits, response);\n        },\n        error => {\n          __DEBUG_BUILD__ && logger.error('Failed while sending event:', error);\n          recordEnvelopeLoss('network_error');\n        },\n      );\n\n    return buffer.add(requestTask).then(\n      result => result,\n      error => {\n        if (error instanceof SentryError) {\n          __DEBUG_BUILD__ && logger.error('Skipped sending event because buffer is full.');\n          recordEnvelopeLoss('queue_overflow');\n          return resolvedSyncPromise();\n        } else {\n          throw error;\n        }\n      },\n    );\n  }\n\n  return {\n    send,\n    flush,\n  };\n}\n\nfunction getEventForEnvelopeItem(item: Envelope[1][number], type: EnvelopeItemType): Event | undefined {\n  if (type !== 'event' && type !== 'transaction') {\n    return undefined;\n  }\n\n  return Array.isArray(item) ? (item as EventItem)[1] : undefined;\n}\n", "import { G<PERSON><PERSON><PERSON><PERSON>_OBJ, isError, isPlainObject, extractExceptionKeysForMessage, normalizeToSize, addExceptionTypeValue, addExceptionMechanism, isInstanceOf, resolvedSyncPromise, createStack<PERSON>arser, nodeStackLineParser, basename, rejectedSyncPromise, stackParserFromStackParserOptions } from '@sentry/utils';\nimport { BaseClient, createTransport, Hub, getIntegrationsToSetup } from '@sentry/core';\n\nfunction isObject(value) {\r\n    return typeof value === 'object' && value !== null;\r\n}\r\nfunction isMechanism(value) {\r\n    return (isObject(value) &&\r\n        'handled' in value &&\r\n        typeof value.handled === 'boolean' &&\r\n        'type' in value &&\r\n        typeof value.type === 'string');\r\n}\r\nfunction containsMechanism(value) {\r\n    return (isObject(value) && 'mechanism' in value && isMechanism(value['mechanism']));\r\n}\r\n/**\r\n * Tries to find release in a global\r\n */\r\nfunction getSentryRelease() {\r\n    // Most of the plugins from https://docs.sentry.io/platforms/javascript/sourcemaps/uploading/ inject SENTRY_RELEASE global to the bundle\r\n    if (GLOBAL_OBJ.SENTRY_RELEASE && GLOBAL_OBJ.SENTRY_RELEASE.id) {\r\n        return GLOBAL_OBJ.SENTRY_RELEASE.id;\r\n    }\r\n}\r\n/**\r\n * Creates an entry on existing object and returns it, or creates a new object with the entry if it doesn't exist.\r\n *\r\n * @param target\r\n * @param entry\r\n * @returns Object with new entry.\r\n */\r\nfunction setOnOptional(target, entry) {\r\n    if (target !== undefined) {\r\n        target[entry[0]] = entry[1];\r\n        return target;\r\n    }\r\n    else {\r\n        return { [entry[0]]: entry[1] };\r\n    }\r\n}\n\n/**\r\n * Extracts stack frames from the error.stack string\r\n */\r\nfunction parseStackFrames(stackParser, error) {\r\n    return stackParser(error.stack || '', 1);\r\n}\r\n/**\r\n * There are cases where stacktrace.message is an Event object\r\n * https://github.com/getsentry/sentry-javascript/issues/1949\r\n * In this specific case we try to extract stacktrace.message.error.message\r\n */\r\nfunction extractMessage(ex) {\r\n    const message = ex && ex.message;\r\n    if (!message) {\r\n        return 'No error message';\r\n    }\r\n    if (message.error && typeof message.error.message === 'string') {\r\n        return message.error.message;\r\n    }\r\n    return message;\r\n}\r\n/**\r\n * Extracts stack frames from the error and builds a Sentry Exception\r\n */\r\nfunction exceptionFromError(stackParser, error) {\r\n    const exception = {\r\n        type: error.name || error.constructor.name,\r\n        value: extractMessage(error),\r\n    };\r\n    const frames = parseStackFrames(stackParser, error);\r\n    if (frames.length) {\r\n        exception.stacktrace = { frames };\r\n    }\r\n    if (exception.type === undefined && exception.value === '') {\r\n        exception.value = 'Unrecoverable error caught';\r\n    }\r\n    return exception;\r\n}\r\n/**\r\n * Builds and Event from a Exception\r\n */\r\nfunction eventFromUnknownInput(sdk, stackParser, exception, hint) {\r\n    let ex;\r\n    const providedMechanism = hint && hint.data && containsMechanism(hint.data)\r\n        ? hint.data.mechanism\r\n        : undefined;\r\n    const mechanism = providedMechanism ?? {\r\n        handled: true,\r\n        type: 'generic',\r\n    };\r\n    if (!isError(exception)) {\r\n        if (isPlainObject(exception)) {\r\n            // This will allow us to group events based on top-level keys\r\n            // which is much better than creating new group when any key/value change\r\n            const message = `Non-Error exception captured with keys: ${extractExceptionKeysForMessage(exception)}`;\r\n            const client = sdk?.getClient();\r\n            const normalizeDepth = client && client.getOptions().normalizeDepth;\r\n            sdk?.configureScope((scope) => {\r\n                scope.setExtra('__serialized__', normalizeToSize(exception, normalizeDepth));\r\n            });\r\n            ex = (hint && hint.syntheticException) || new Error(message);\r\n            ex.message = message;\r\n        }\r\n        else {\r\n            // This handles when someone does: `throw \"something awesome\";`\r\n            // We use synthesized Error here so we can extract a (rough) stack trace.\r\n            ex = (hint && hint.syntheticException) || new Error(exception);\r\n            ex.message = exception;\r\n        }\r\n        mechanism.synthetic = true;\r\n    }\r\n    else {\r\n        ex = exception;\r\n    }\r\n    const event = {\r\n        exception: {\r\n            values: [exceptionFromError(stackParser, ex)],\r\n        },\r\n    };\r\n    addExceptionTypeValue(event, undefined, undefined);\r\n    addExceptionMechanism(event, mechanism);\r\n    return {\r\n        ...event,\r\n        event_id: hint && hint.event_id,\r\n    };\r\n}\r\n/**\r\n * Builds and Event from a Message\r\n */\r\nfunction eventFromMessage(stackParser, message, level = 'info', hint, attachStacktrace) {\r\n    const event = {\r\n        event_id: hint && hint.event_id,\r\n        level,\r\n        message,\r\n    };\r\n    if (attachStacktrace && hint && hint.syntheticException) {\r\n        const frames = parseStackFrames(stackParser, hint.syntheticException);\r\n        if (frames.length) {\r\n            event.exception = {\r\n                values: [\r\n                    {\r\n                        value: message,\r\n                        stacktrace: { frames },\r\n                    },\r\n                ],\r\n            };\r\n        }\r\n    }\r\n    return event;\r\n}\n\nconst DEFAULT_LIMIT = 5;\r\nclass LinkedErrors {\r\n    static id = 'LinkedErrors';\r\n    name = LinkedErrors.id;\r\n    limit;\r\n    constructor(options = {}) {\r\n        this.limit = options.limit || DEFAULT_LIMIT;\r\n    }\r\n    setupOnce(addGlobalEventProcessor, getCurrentHub) {\r\n        const client = getCurrentHub().getClient();\r\n        if (!client) {\r\n            return;\r\n        }\r\n        addGlobalEventProcessor((event, hint) => {\r\n            const self = getCurrentHub().getIntegration(LinkedErrors);\r\n            if (!self) {\r\n                return event;\r\n            }\r\n            return handler(client.getOptions().stackParser, self.limit, event, hint);\r\n        });\r\n    }\r\n}\r\nfunction handler(parser, limit, event, hint) {\r\n    if (!event.exception ||\r\n        !event.exception.values ||\r\n        !hint ||\r\n        !isInstanceOf(hint.originalException, Error)) {\r\n        return event;\r\n    }\r\n    const linkedErrors = walkErrorTree(parser, limit, hint.originalException);\r\n    event.exception.values = [...linkedErrors, ...event.exception.values];\r\n    return event;\r\n}\r\nfunction walkErrorTree(parser, limit, error, stack = []) {\r\n    if (!isInstanceOf(error.cause, Error) || stack.length + 1 >= limit) {\r\n        return stack;\r\n    }\r\n    const exception = exceptionFromError(parser, error.cause);\r\n    return walkErrorTree(parser, limit, error.cause, [\r\n        exception,\r\n        ...stack,\r\n    ]);\r\n}\n\nconst defaultRequestDataOptions = {\r\n    allowedHeaders: ['CF-RAY', 'CF-Worker'],\r\n};\r\nclass RequestData {\r\n    static id = 'RequestData';\r\n    name = RequestData.id;\r\n    #options;\r\n    constructor(options = {}) {\r\n        this.#options = { ...defaultRequestDataOptions, ...options };\r\n    }\r\n    setupOnce(addGlobalEventProcessor, getCurrentHub) {\r\n        const client = getCurrentHub().getClient();\r\n        if (!client) {\r\n            return;\r\n        }\r\n        addGlobalEventProcessor((event) => {\r\n            const { sdkProcessingMetadata } = event;\r\n            const self = getCurrentHub().getIntegration(RequestData);\r\n            if (!self || !sdkProcessingMetadata) {\r\n                return event;\r\n            }\r\n            if ('request' in sdkProcessingMetadata &&\r\n                sdkProcessingMetadata.request instanceof Request) {\r\n                event.request = toEventRequest(sdkProcessingMetadata.request, this.#options);\r\n                event.user = toEventUser(event.user ?? {}, sdkProcessingMetadata.request, this.#options);\r\n            }\r\n            if ('requestData' in sdkProcessingMetadata) {\r\n                if (event.request) {\r\n                    event.request.data = sdkProcessingMetadata.requestData;\r\n                }\r\n                else {\r\n                    event.request = {\r\n                        data: sdkProcessingMetadata.requestData,\r\n                    };\r\n                }\r\n            }\r\n            return event;\r\n        });\r\n    }\r\n}\r\n/**\r\n * Applies allowlists on existing user object.\r\n *\r\n * @param user\r\n * @param request\r\n * @param options\r\n * @returns New copy of user\r\n */\r\nfunction toEventUser(user, request, options) {\r\n    const ip_address = request.headers.get('CF-Connecting-IP');\r\n    const { allowedIps } = options;\r\n    const newUser = { ...user };\r\n    if (!('ip_address' in user) && // If ip_address is already set from explicitly called setUser, we don't want to overwrite it\r\n        ip_address &&\r\n        allowedIps !== undefined &&\r\n        testAllowlist(ip_address, allowedIps)) {\r\n        newUser.ip_address = ip_address;\r\n    }\r\n    return Object.keys(newUser).length > 0 ? newUser : undefined;\r\n}\r\n/**\r\n * Converts data from fetch event's Request to Sentry Request used in Sentry Event\r\n *\r\n * @param request Native Request object\r\n * @param options Integration options\r\n * @returns Sentry Request object\r\n */\r\nfunction toEventRequest(request, options) {\r\n    // Build cookies\r\n    const cookieString = request.headers.get('cookie');\r\n    let cookies = undefined;\r\n    if (cookieString) {\r\n        try {\r\n            cookies = parseCookie(cookieString);\r\n        }\r\n        catch (e) {\r\n            // Cookie string failed to parse, no need to do anything\r\n        }\r\n    }\r\n    const headers = {};\r\n    // Build headers (omit cookie header, because we used it in the previous step)\r\n    for (const [k, v] of request.headers.entries()) {\r\n        if (k !== 'cookie') {\r\n            headers[k] = v;\r\n        }\r\n    }\r\n    const eventRequest = {\r\n        method: request.method,\r\n        cookies,\r\n        headers,\r\n    };\r\n    try {\r\n        const url = new URL(request.url);\r\n        eventRequest.url = `${url.protocol}//${url.hostname}${url.pathname}`;\r\n        eventRequest.query_string = url.search;\r\n    }\r\n    catch (e) {\r\n        // `new URL` failed, let's try to split URL the primitive way\r\n        const qi = request.url.indexOf('?');\r\n        if (qi < 0) {\r\n            // no query string\r\n            eventRequest.url = request.url;\r\n        }\r\n        else {\r\n            eventRequest.url = request.url.substr(0, qi);\r\n            eventRequest.query_string = request.url.substr(qi + 1);\r\n        }\r\n    }\r\n    // Let's try to remove sensitive data from incoming Request\r\n    const { allowedHeaders, allowedCookies, allowedSearchParams } = options;\r\n    if (allowedHeaders !== undefined && eventRequest.headers) {\r\n        eventRequest.headers = applyAllowlistToObject(eventRequest.headers, allowedHeaders);\r\n        if (Object.keys(eventRequest.headers).length === 0) {\r\n            delete eventRequest.headers;\r\n        }\r\n    }\r\n    else {\r\n        delete eventRequest.headers;\r\n    }\r\n    if (allowedCookies !== undefined && eventRequest.cookies) {\r\n        eventRequest.cookies = applyAllowlistToObject(eventRequest.cookies, allowedCookies);\r\n        if (Object.keys(eventRequest.cookies).length === 0) {\r\n            delete eventRequest.cookies;\r\n        }\r\n    }\r\n    else {\r\n        delete eventRequest.cookies;\r\n    }\r\n    if (allowedSearchParams !== undefined) {\r\n        const params = Object.fromEntries(new URLSearchParams(eventRequest.query_string));\r\n        const allowedParams = new URLSearchParams();\r\n        Object.keys(applyAllowlistToObject(params, allowedSearchParams)).forEach((allowedKey) => {\r\n            allowedParams.set(allowedKey, params[allowedKey]);\r\n        });\r\n        eventRequest.query_string = allowedParams.toString();\r\n    }\r\n    else {\r\n        delete eventRequest.query_string;\r\n    }\r\n    return eventRequest;\r\n}\r\n/**\r\n * Helper function that tests 'allowlist' on string.\r\n *\r\n * @param target\r\n * @param allowlist\r\n * @returns True if target is allowed.\r\n */\r\nfunction testAllowlist(target, allowlist) {\r\n    if (typeof allowlist === 'boolean') {\r\n        return allowlist;\r\n    }\r\n    else if (allowlist instanceof RegExp) {\r\n        return allowlist.test(target);\r\n    }\r\n    else if (Array.isArray(allowlist)) {\r\n        const allowlistLowercased = allowlist.map((item) => item.toLowerCase());\r\n        return allowlistLowercased.includes(target);\r\n    }\r\n    else {\r\n        return false;\r\n    }\r\n}\r\n/**\r\n * Helper function that applies 'allowlist' to target's entries.\r\n *\r\n * @param target\r\n * @param allowlist\r\n * @returns New object with allowed keys.\r\n */\r\nfunction applyAllowlistToObject(target, allowlist) {\r\n    let predicate = () => false;\r\n    if (typeof allowlist === 'boolean') {\r\n        return allowlist ? target : {};\r\n    }\r\n    else if (allowlist instanceof RegExp) {\r\n        predicate = (item) => allowlist.test(item);\r\n    }\r\n    else if (Array.isArray(allowlist)) {\r\n        const allowlistLowercased = allowlist.map((item) => item.toLowerCase());\r\n        predicate = (item) => allowlistLowercased.includes(item);\r\n    }\r\n    else {\r\n        return {};\r\n    }\r\n    return Object.keys(target)\r\n        .map((key) => key.toLowerCase())\r\n        .filter((key) => predicate(key))\r\n        .reduce((allowed, key) => {\r\n        allowed[key] = target[key];\r\n        return allowed;\r\n    }, {});\r\n}\r\n/**\r\n * Converts cookie string to an object.\r\n *\r\n * @param cookieString\r\n * @returns Object of cookie entries, or empty object if something went wrong during the conversion.\r\n */\r\nfunction parseCookie(cookieString) {\r\n    if (typeof cookieString !== 'string') {\r\n        return {};\r\n    }\r\n    try {\r\n        return cookieString\r\n            .split(';')\r\n            .map((part) => part.split('='))\r\n            .reduce((acc, [cookieKey, cookieValue]) => {\r\n            acc[decodeURIComponent(cookieKey.trim())] = decodeURIComponent(cookieValue.trim());\r\n            return acc;\r\n        }, {});\r\n    }\r\n    catch {\r\n        return {};\r\n    }\r\n}\n\n/**\r\n * Installs integrations on the current scope.\r\n *\r\n * @param integrations array of integration instances\r\n */\r\nfunction setupIntegrations(integrations, sdk) {\r\n    const integrationIndex = {};\r\n    integrations.forEach((integration) => {\r\n        integrationIndex[integration.name] = integration;\r\n        integration.setupOnce((callback) => {\r\n            sdk.getScope()?.addEventProcessor(callback);\r\n        }, () => sdk);\r\n    });\r\n    return integrationIndex;\r\n}\n\n/**\r\n * The Cloudflare Workers SDK Client.\r\n */\r\nclass ToucanClient extends BaseClient {\r\n    /**\r\n     * Some functions need to access the Hub (Toucan instance) this client is bound to,\r\n     * but calling 'getCurrentHub()' is unsafe because it uses globals.\r\n     * So we store a reference to the Hub after binding to it and provide it to methods that need it.\r\n     */\r\n    #sdk = null;\r\n    /**\r\n     * Creates a new Toucan SDK instance.\r\n     * @param options Configuration options for this SDK.\r\n     */\r\n    constructor(options) {\r\n        options._metadata = options._metadata || {};\r\n        options._metadata.sdk = options._metadata.sdk || {\r\n            name: 'toucan-js',\r\n            packages: [\r\n                {\r\n                    name: 'npm:' + 'toucan-js',\r\n                    version: '3.0.0',\r\n                },\r\n            ],\r\n            version: '3.0.0',\r\n        };\r\n        super(options);\r\n    }\r\n    /**\r\n     * By default, integrations are stored in a global. We want to store them in a local instance because they may have contextual data, such as event request.\r\n     */\r\n    setupIntegrations() {\r\n        if (this._isEnabled() && !this._integrationsInitialized && this.#sdk) {\r\n            this._integrations = setupIntegrations(this._options.integrations, this.#sdk);\r\n            this._integrationsInitialized = true;\r\n        }\r\n    }\r\n    eventFromException(exception, hint) {\r\n        return resolvedSyncPromise(eventFromUnknownInput(this.#sdk, this._options.stackParser, exception, hint));\r\n    }\r\n    eventFromMessage(message, level = 'info', hint) {\r\n        return resolvedSyncPromise(eventFromMessage(this._options.stackParser, message, level, hint, this._options.attachStacktrace));\r\n    }\r\n    _prepareEvent(event, hint, scope) {\r\n        event.platform = event.platform || 'javascript';\r\n        if (this.getOptions().request) {\r\n            // Set 'request' on sdkProcessingMetadata to be later processed by RequestData integration\r\n            event.sdkProcessingMetadata = setOnOptional(event.sdkProcessingMetadata, [\r\n                'request',\r\n                this.getOptions().request,\r\n            ]);\r\n        }\r\n        if (this.getOptions().requestData) {\r\n            // Set 'requestData' on sdkProcessingMetadata to be later processed by RequestData integration\r\n            event.sdkProcessingMetadata = setOnOptional(event.sdkProcessingMetadata, [\r\n                'requestData',\r\n                this.getOptions().requestData,\r\n            ]);\r\n        }\r\n        return super._prepareEvent(event, hint, scope);\r\n    }\r\n    getSdk() {\r\n        return this.#sdk;\r\n    }\r\n    setSdk(sdk) {\r\n        this.#sdk = sdk;\r\n    }\r\n    /**\r\n     * Sets the request body context on all future events.\r\n     *\r\n     * @param body Request body.\r\n     * @example\r\n     * const body = await request.text();\r\n     * toucan.setRequestBody(body);\r\n     */\r\n    setRequestBody(body) {\r\n        this.getOptions().requestData = body;\r\n    }\r\n}\n\n/**\r\n * Stack line parser for Cloudflare Workers.\r\n * This wraps node stack parser and adjusts root paths to match with source maps.\r\n *\r\n */\r\nfunction workersStackLineParser(getModule) {\r\n    const [arg1, arg2] = nodeStackLineParser(getModule);\r\n    const fn = (line) => {\r\n        const result = arg2(line);\r\n        if (result) {\r\n            const filename = result.filename;\r\n            // Workers runtime runs a single bundled file that is always in a virtual root\r\n            result.abs_path =\r\n                filename !== undefined && !filename.startsWith('/')\r\n                    ? `/${filename}`\r\n                    : filename;\r\n            // There is no way to tell what code is in_app and what comes from dependencies (node_modules), since we have one bundled file.\r\n            // So everything is in_app, unless an error comes from runtime function (ie. JSON.parse), which is determined by the presence of filename.\r\n            result.in_app = filename !== undefined;\r\n        }\r\n        return result;\r\n    };\r\n    return [arg1, fn];\r\n}\r\n/**\r\n * Gets the module from filename.\r\n *\r\n * @param filename\r\n * @returns Module name\r\n */\r\nfunction getModule(filename) {\r\n    if (!filename) {\r\n        return;\r\n    }\r\n    // In Cloudflare Workers there is always only one bundled file\r\n    return basename(filename, '.js');\r\n}\r\n/** Cloudflare Workers stack parser */\r\nconst defaultStackParser = createStackParser(workersStackLineParser(getModule));\n\n/**\r\n * Creates a Transport that uses native fetch. This transport automatically extends the Workers lifetime with 'waitUntil'.\r\n */\r\nfunction makeFetchTransport(options) {\r\n    function makeRequest({ body, }) {\r\n        try {\r\n            const request = fetch(options.url, {\r\n                method: 'POST',\r\n                headers: options.headers,\r\n                body,\r\n            }).then((response) => {\r\n                return {\r\n                    statusCode: response.status,\r\n                    headers: {\r\n                        'retry-after': response.headers.get('Retry-After'),\r\n                        'x-sentry-rate-limits': response.headers.get('X-Sentry-Rate-Limits'),\r\n                    },\r\n                };\r\n            });\r\n            /**\r\n             * Call waitUntil to extend Workers Event lifetime\r\n             */\r\n            if (options.context) {\r\n                options.context.waitUntil(request);\r\n            }\r\n            return request;\r\n        }\r\n        catch (e) {\r\n            return rejectedSyncPromise(e);\r\n        }\r\n    }\r\n    return createTransport(options, makeRequest);\r\n}\n\n/**\r\n * The Cloudflare Workers SDK.\r\n */\r\nclass Toucan extends Hub {\r\n    constructor(options) {\r\n        options.defaultIntegrations =\r\n            options.defaultIntegrations === false\r\n                ? []\r\n                : [\r\n                    ...(Array.isArray(options.defaultIntegrations)\r\n                        ? options.defaultIntegrations\r\n                        : [\r\n                            new RequestData(options.requestDataOptions),\r\n                            new LinkedErrors(),\r\n                        ]),\r\n                ];\r\n        if (options.release === undefined) {\r\n            const detectedRelease = getSentryRelease();\r\n            if (detectedRelease !== undefined) {\r\n                options.release = detectedRelease;\r\n            }\r\n        }\r\n        const client = new ToucanClient({\r\n            ...options,\r\n            transport: makeFetchTransport,\r\n            integrations: getIntegrationsToSetup(options),\r\n            stackParser: stackParserFromStackParserOptions(options.stackParser || defaultStackParser),\r\n            transportOptions: {\r\n                ...options.transportOptions,\r\n                context: options.context,\r\n            },\r\n        });\r\n        super(client);\r\n        client.setSdk(this);\r\n        client.setupIntegrations();\r\n    }\r\n    /**\r\n     * Sets the request body context on all future events.\r\n     *\r\n     * @param body Request body.\r\n     * @example\r\n     * const body = await request.text();\r\n     * toucan.setRequestBody(body);\r\n     */\r\n    setRequestBody(body) {\r\n        this.getClient()?.setRequestBody(body);\r\n    }\r\n}\n\nexport { LinkedErrors, RequestData, Toucan };\n", "import type { PluginArgs, PluginData } from \"@cloudflare/pages-plugin-sentry\";\nimport { Toucan } from \"toucan-js\";\n\ntype SentryPagesPluginFunction<\n  Env = unknown,\n  Params extends string = any,\n  Data extends Record<string, unknown> = Record<string, unknown>,\n> = PagesPluginFunction<Env, Params, Data & PluginData, PluginArgs>;\n\nexport const onRequest: SentryPagesPluginFunction = async (context) => {\n  context.data.sentry = new Toucan({\n    context,\n    ...context.pluginArgs,\n  });\n\n  try {\n    return await context.next();\n  } catch (thrown) {\n    context.data.sentry.captureException(thrown);\n    throw thrown;\n  }\n};\n", "import { onRequest as ___middleware_ts_onRequest } from \"/home/<USER>/work/pages-plugins/pages-plugins/packages/sentry/functions/_middleware.ts\"\n\nexport const routes = [\n    {\n      routePath: \"/\",\n      mountPath: \"/\",\n      method: \"\",\n      middlewares: [___middleware_ts_onRequest],\n      modules: [],\n    },\n  ]", "/**\n * Tokenizer results.\n */\ninterface LexToken {\n  type:\n    | \"OPEN\"\n    | \"CLOSE\"\n    | \"PATTERN\"\n    | \"NAME\"\n    | \"CHAR\"\n    | \"ESCAPED_CHAR\"\n    | \"MODIFIER\"\n    | \"END\";\n  index: number;\n  value: string;\n}\n\n/**\n * Tokenize input string.\n */\nfunction lexer(str: string): LexToken[] {\n  const tokens: LexToken[] = [];\n  let i = 0;\n\n  while (i < str.length) {\n    const char = str[i];\n\n    if (char === \"*\" || char === \"+\" || char === \"?\") {\n      tokens.push({ type: \"MODIFIER\", index: i, value: str[i++] });\n      continue;\n    }\n\n    if (char === \"\\\\\") {\n      tokens.push({ type: \"ESCAPED_CHAR\", index: i++, value: str[i++] });\n      continue;\n    }\n\n    if (char === \"{\") {\n      tokens.push({ type: \"OPEN\", index: i, value: str[i++] });\n      continue;\n    }\n\n    if (char === \"}\") {\n      tokens.push({ type: \"CLOSE\", index: i, value: str[i++] });\n      continue;\n    }\n\n    if (char === \":\") {\n      let name = \"\";\n      let j = i + 1;\n\n      while (j < str.length) {\n        const code = str.charCodeAt(j);\n\n        if (\n          // `0-9`\n          (code >= 48 && code <= 57) ||\n          // `A-Z`\n          (code >= 65 && code <= 90) ||\n          // `a-z`\n          (code >= 97 && code <= 122) ||\n          // `_`\n          code === 95\n        ) {\n          name += str[j++];\n          continue;\n        }\n\n        break;\n      }\n\n      if (!name) throw new TypeError(`Missing parameter name at ${i}`);\n\n      tokens.push({ type: \"NAME\", index: i, value: name });\n      i = j;\n      continue;\n    }\n\n    if (char === \"(\") {\n      let count = 1;\n      let pattern = \"\";\n      let j = i + 1;\n\n      if (str[j] === \"?\") {\n        throw new TypeError(`Pattern cannot start with \"?\" at ${j}`);\n      }\n\n      while (j < str.length) {\n        if (str[j] === \"\\\\\") {\n          pattern += str[j++] + str[j++];\n          continue;\n        }\n\n        if (str[j] === \")\") {\n          count--;\n          if (count === 0) {\n            j++;\n            break;\n          }\n        } else if (str[j] === \"(\") {\n          count++;\n          if (str[j + 1] !== \"?\") {\n            throw new TypeError(`Capturing groups are not allowed at ${j}`);\n          }\n        }\n\n        pattern += str[j++];\n      }\n\n      if (count) throw new TypeError(`Unbalanced pattern at ${i}`);\n      if (!pattern) throw new TypeError(`Missing pattern at ${i}`);\n\n      tokens.push({ type: \"PATTERN\", index: i, value: pattern });\n      i = j;\n      continue;\n    }\n\n    tokens.push({ type: \"CHAR\", index: i, value: str[i++] });\n  }\n\n  tokens.push({ type: \"END\", index: i, value: \"\" });\n\n  return tokens;\n}\n\nexport interface ParseOptions {\n  /**\n   * Set the default delimiter for repeat parameters. (default: `'/'`)\n   */\n  delimiter?: string;\n  /**\n   * List of characters to automatically consider prefixes when parsing.\n   */\n  prefixes?: string;\n}\n\n/**\n * Parse a string for the raw tokens.\n */\nexport function parse(str: string, options: ParseOptions = {}): Token[] {\n  const tokens = lexer(str);\n  const { prefixes = \"./\" } = options;\n  const defaultPattern = `[^${escapeString(options.delimiter || \"/#?\")}]+?`;\n  const result: Token[] = [];\n  let key = 0;\n  let i = 0;\n  let path = \"\";\n\n  const tryConsume = (type: LexToken[\"type\"]): string | undefined => {\n    if (i < tokens.length && tokens[i].type === type) return tokens[i++].value;\n  };\n\n  const mustConsume = (type: LexToken[\"type\"]): string => {\n    const value = tryConsume(type);\n    if (value !== undefined) return value;\n    const { type: nextType, index } = tokens[i];\n    throw new TypeError(`Unexpected ${nextType} at ${index}, expected ${type}`);\n  };\n\n  const consumeText = (): string => {\n    let result = \"\";\n    let value: string | undefined;\n    while ((value = tryConsume(\"CHAR\") || tryConsume(\"ESCAPED_CHAR\"))) {\n      result += value;\n    }\n    return result;\n  };\n\n  while (i < tokens.length) {\n    const char = tryConsume(\"CHAR\");\n    const name = tryConsume(\"NAME\");\n    const pattern = tryConsume(\"PATTERN\");\n\n    if (name || pattern) {\n      let prefix = char || \"\";\n\n      if (prefixes.indexOf(prefix) === -1) {\n        path += prefix;\n        prefix = \"\";\n      }\n\n      if (path) {\n        result.push(path);\n        path = \"\";\n      }\n\n      result.push({\n        name: name || key++,\n        prefix,\n        suffix: \"\",\n        pattern: pattern || defaultPattern,\n        modifier: tryConsume(\"MODIFIER\") || \"\",\n      });\n      continue;\n    }\n\n    const value = char || tryConsume(\"ESCAPED_CHAR\");\n    if (value) {\n      path += value;\n      continue;\n    }\n\n    if (path) {\n      result.push(path);\n      path = \"\";\n    }\n\n    const open = tryConsume(\"OPEN\");\n    if (open) {\n      const prefix = consumeText();\n      const name = tryConsume(\"NAME\") || \"\";\n      const pattern = tryConsume(\"PATTERN\") || \"\";\n      const suffix = consumeText();\n\n      mustConsume(\"CLOSE\");\n\n      result.push({\n        name: name || (pattern ? key++ : \"\"),\n        pattern: name && !pattern ? defaultPattern : pattern,\n        prefix,\n        suffix,\n        modifier: tryConsume(\"MODIFIER\") || \"\",\n      });\n      continue;\n    }\n\n    mustConsume(\"END\");\n  }\n\n  return result;\n}\n\nexport interface TokensToFunctionOptions {\n  /**\n   * When `true` the regexp will be case sensitive. (default: `false`)\n   */\n  sensitive?: boolean;\n  /**\n   * Function for encoding input strings for output.\n   */\n  encode?: (value: string, token: Key) => string;\n  /**\n   * When `false` the function can produce an invalid (unmatched) path. (default: `true`)\n   */\n  validate?: boolean;\n}\n\n/**\n * Compile a string to a template function for the path.\n */\nexport function compile<P extends object = object>(\n  str: string,\n  options?: ParseOptions & TokensToFunctionOptions\n) {\n  return tokensToFunction<P>(parse(str, options), options);\n}\n\nexport type PathFunction<P extends object = object> = (data?: P) => string;\n\n/**\n * Expose a method for transforming tokens into the path function.\n */\nexport function tokensToFunction<P extends object = object>(\n  tokens: Token[],\n  options: TokensToFunctionOptions = {}\n): PathFunction<P> {\n  const reFlags = flags(options);\n  const { encode = (x: string) => x, validate = true } = options;\n\n  // Compile all the tokens into regexps.\n  const matches = tokens.map((token) => {\n    if (typeof token === \"object\") {\n      return new RegExp(`^(?:${token.pattern})$`, reFlags);\n    }\n  });\n\n  return (data: Record<string, any> | null | undefined) => {\n    let path = \"\";\n\n    for (let i = 0; i < tokens.length; i++) {\n      const token = tokens[i];\n\n      if (typeof token === \"string\") {\n        path += token;\n        continue;\n      }\n\n      const value = data ? data[token.name] : undefined;\n      const optional = token.modifier === \"?\" || token.modifier === \"*\";\n      const repeat = token.modifier === \"*\" || token.modifier === \"+\";\n\n      if (Array.isArray(value)) {\n        if (!repeat) {\n          throw new TypeError(\n            `Expected \"${token.name}\" to not repeat, but got an array`\n          );\n        }\n\n        if (value.length === 0) {\n          if (optional) continue;\n\n          throw new TypeError(`Expected \"${token.name}\" to not be empty`);\n        }\n\n        for (let j = 0; j < value.length; j++) {\n          const segment = encode(value[j], token);\n\n          if (validate && !(matches[i] as RegExp).test(segment)) {\n            throw new TypeError(\n              `Expected all \"${token.name}\" to match \"${token.pattern}\", but got \"${segment}\"`\n            );\n          }\n\n          path += token.prefix + segment + token.suffix;\n        }\n\n        continue;\n      }\n\n      if (typeof value === \"string\" || typeof value === \"number\") {\n        const segment = encode(String(value), token);\n\n        if (validate && !(matches[i] as RegExp).test(segment)) {\n          throw new TypeError(\n            `Expected \"${token.name}\" to match \"${token.pattern}\", but got \"${segment}\"`\n          );\n        }\n\n        path += token.prefix + segment + token.suffix;\n        continue;\n      }\n\n      if (optional) continue;\n\n      const typeOfMessage = repeat ? \"an array\" : \"a string\";\n      throw new TypeError(`Expected \"${token.name}\" to be ${typeOfMessage}`);\n    }\n\n    return path;\n  };\n}\n\nexport interface RegexpToFunctionOptions {\n  /**\n   * Function for decoding strings for params.\n   */\n  decode?: (value: string, token: Key) => string;\n}\n\n/**\n * A match result contains data about the path match.\n */\nexport interface MatchResult<P extends object = object> {\n  path: string;\n  index: number;\n  params: P;\n}\n\n/**\n * A match is either `false` (no match) or a match result.\n */\nexport type Match<P extends object = object> = false | MatchResult<P>;\n\n/**\n * The match function takes a string and returns whether it matched the path.\n */\nexport type MatchFunction<P extends object = object> = (\n  path: string\n) => Match<P>;\n\n/**\n * Create path match function from `path-to-regexp` spec.\n */\nexport function match<P extends object = object>(\n  str: Path,\n  options?: ParseOptions & TokensToRegexpOptions & RegexpToFunctionOptions\n) {\n  const keys: Key[] = [];\n  const re = pathToRegexp(str, keys, options);\n  return regexpToFunction<P>(re, keys, options);\n}\n\n/**\n * Create a path match function from `path-to-regexp` output.\n */\nexport function regexpToFunction<P extends object = object>(\n  re: RegExp,\n  keys: Key[],\n  options: RegexpToFunctionOptions = {}\n): MatchFunction<P> {\n  const { decode = (x: string) => x } = options;\n\n  return function (pathname: string) {\n    const m = re.exec(pathname);\n    if (!m) return false;\n\n    const { 0: path, index } = m;\n    const params = Object.create(null);\n\n    for (let i = 1; i < m.length; i++) {\n      if (m[i] === undefined) continue;\n\n      const key = keys[i - 1];\n\n      if (key.modifier === \"*\" || key.modifier === \"+\") {\n        params[key.name] = m[i].split(key.prefix + key.suffix).map((value) => {\n          return decode(value, key);\n        });\n      } else {\n        params[key.name] = decode(m[i], key);\n      }\n    }\n\n    return { path, index, params };\n  };\n}\n\n/**\n * Escape a regular expression string.\n */\nfunction escapeString(str: string) {\n  return str.replace(/([.+*?=^!:${}()[\\]|/\\\\])/g, \"\\\\$1\");\n}\n\n/**\n * Get the flags for a regexp from the options.\n */\nfunction flags(options?: { sensitive?: boolean }) {\n  return options && options.sensitive ? \"\" : \"i\";\n}\n\n/**\n * Metadata about a key.\n */\nexport interface Key {\n  name: string | number;\n  prefix: string;\n  suffix: string;\n  pattern: string;\n  modifier: string;\n}\n\n/**\n * A token is a string (nothing special) or key metadata (capture group).\n */\nexport type Token = string | Key;\n\n/**\n * Pull out keys from a regexp.\n */\nfunction regexpToRegexp(path: RegExp, keys?: Key[]): RegExp {\n  if (!keys) return path;\n\n  const groupsRegex = /\\((?:\\?<(.*?)>)?(?!\\?)/g;\n\n  let index = 0;\n  let execResult = groupsRegex.exec(path.source);\n  while (execResult) {\n    keys.push({\n      // Use parenthesized substring match if available, index otherwise\n      name: execResult[1] || index++,\n      prefix: \"\",\n      suffix: \"\",\n      modifier: \"\",\n      pattern: \"\",\n    });\n    execResult = groupsRegex.exec(path.source);\n  }\n\n  return path;\n}\n\n/**\n * Transform an array into a regexp.\n */\nfunction arrayToRegexp(\n  paths: Array<string | RegExp>,\n  keys?: Key[],\n  options?: TokensToRegexpOptions & ParseOptions\n): RegExp {\n  const parts = paths.map((path) => pathToRegexp(path, keys, options).source);\n  return new RegExp(`(?:${parts.join(\"|\")})`, flags(options));\n}\n\n/**\n * Create a path regexp from string input.\n */\nfunction stringToRegexp(\n  path: string,\n  keys?: Key[],\n  options?: TokensToRegexpOptions & ParseOptions\n) {\n  return tokensToRegexp(parse(path, options), keys, options);\n}\n\nexport interface TokensToRegexpOptions {\n  /**\n   * When `true` the regexp will be case sensitive. (default: `false`)\n   */\n  sensitive?: boolean;\n  /**\n   * When `true` the regexp won't allow an optional trailing delimiter to match. (default: `false`)\n   */\n  strict?: boolean;\n  /**\n   * When `true` the regexp will match to the end of the string. (default: `true`)\n   */\n  end?: boolean;\n  /**\n   * When `true` the regexp will match from the beginning of the string. (default: `true`)\n   */\n  start?: boolean;\n  /**\n   * Sets the final character for non-ending optimistic matches. (default: `/`)\n   */\n  delimiter?: string;\n  /**\n   * List of characters that can also be \"end\" characters.\n   */\n  endsWith?: string;\n  /**\n   * Encode path tokens for use in the `RegExp`.\n   */\n  encode?: (value: string) => string;\n}\n\n/**\n * Expose a function for taking tokens and returning a RegExp.\n */\nexport function tokensToRegexp(\n  tokens: Token[],\n  keys?: Key[],\n  options: TokensToRegexpOptions = {}\n) {\n  const {\n    strict = false,\n    start = true,\n    end = true,\n    encode = (x: string) => x,\n    delimiter = \"/#?\",\n    endsWith = \"\",\n  } = options;\n  const endsWithRe = `[${escapeString(endsWith)}]|$`;\n  const delimiterRe = `[${escapeString(delimiter)}]`;\n  let route = start ? \"^\" : \"\";\n\n  // Iterate over the tokens and create our regexp string.\n  for (const token of tokens) {\n    if (typeof token === \"string\") {\n      route += escapeString(encode(token));\n    } else {\n      const prefix = escapeString(encode(token.prefix));\n      const suffix = escapeString(encode(token.suffix));\n\n      if (token.pattern) {\n        if (keys) keys.push(token);\n\n        if (prefix || suffix) {\n          if (token.modifier === \"+\" || token.modifier === \"*\") {\n            const mod = token.modifier === \"*\" ? \"?\" : \"\";\n            route += `(?:${prefix}((?:${token.pattern})(?:${suffix}${prefix}(?:${token.pattern}))*)${suffix})${mod}`;\n          } else {\n            route += `(?:${prefix}(${token.pattern})${suffix})${token.modifier}`;\n          }\n        } else {\n          if (token.modifier === \"+\" || token.modifier === \"*\") {\n            route += `((?:${token.pattern})${token.modifier})`;\n          } else {\n            route += `(${token.pattern})${token.modifier}`;\n          }\n        }\n      } else {\n        route += `(?:${prefix}${suffix})${token.modifier}`;\n      }\n    }\n  }\n\n  if (end) {\n    if (!strict) route += `${delimiterRe}?`;\n\n    route += !options.endsWith ? \"$\" : `(?=${endsWithRe})`;\n  } else {\n    const endToken = tokens[tokens.length - 1];\n    const isEndDelimited =\n      typeof endToken === \"string\"\n        ? delimiterRe.indexOf(endToken[endToken.length - 1]) > -1\n        : endToken === undefined;\n\n    if (!strict) {\n      route += `(?:${delimiterRe}(?=${endsWithRe}))?`;\n    }\n\n    if (!isEndDelimited) {\n      route += `(?=${delimiterRe}|${endsWithRe})`;\n    }\n  }\n\n  return new RegExp(route, flags(options));\n}\n\n/**\n * Supported `path-to-regexp` input types.\n */\nexport type Path = string | RegExp | Array<string | RegExp>;\n\n/**\n * Normalize the given path string, returning a regular expression.\n *\n * An empty array can be passed in for the keys, which will hold the\n * placeholder key descriptions. For example, using `/user/:id`, `keys` will\n * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.\n */\nexport function pathToRegexp(\n  path: Path,\n  keys?: Key[],\n  options?: TokensToRegexpOptions & ParseOptions\n) {\n  if (path instanceof RegExp) return regexpToRegexp(path, keys);\n  if (Array.isArray(path)) return arrayToRegexp(path, keys, options);\n  return stringToRegexp(path, keys, options);\n}\n", "import { match } from \"path-to-regexp\";\n\n//note: this explicitly does not include the * character, as pages requires this\nconst escapeRegex = /[.+?^${}()|[\\]\\\\]/g;\n\ntype HTTPMethod =\n\t| \"HEAD\"\n\t| \"OPTIONS\"\n\t| \"GET\"\n\t| \"POST\"\n\t| \"PUT\"\n\t| \"PATCH\"\n\t| \"DELETE\";\n\n/* TODO: Grab these from @cloudflare/workers-types instead */\ntype Params<P extends string = string> = Record<P, string | string[]>;\n\ntype EventContext<Env, P extends string, Data> = {\n\trequest: Request;\n\tfunctionPath: string;\n\twaitUntil: (promise: Promise<unknown>) => void;\n\tpassThroughOnException: () => void;\n\tnext: (input?: Request | string, init?: RequestInit) => Promise<Response>;\n\tenv: Env & { ASSETS: { fetch: typeof fetch } };\n\tparams: Params<P>;\n\tdata: Data;\n};\n\ntype EventPluginContext<Env, P extends string, Data, PluginArgs> = {\n\trequest: Request;\n\tfunctionPath: string;\n\twaitUntil: (promise: Promise<unknown>) => void;\n\tpassThroughOnException: () => void;\n\tnext: (input?: Request | string, init?: RequestInit) => Promise<Response>;\n\tenv: Env & { ASSETS: { fetch: typeof fetch } };\n\tparams: Params<P>;\n\tdata: Data;\n\tpluginArgs: PluginArgs;\n};\n\ndeclare type PagesFunction<\n\tEnv = unknown,\n\tP extends string = string,\n\tData extends Record<string, unknown> = Record<string, unknown>\n> = (context: EventContext<Env, P, Data>) => Response | Promise<Response>;\n\ndeclare type PagesPluginFunction<\n\tEnv = unknown,\n\tP extends string = string,\n\tData extends Record<string, unknown> = Record<string, unknown>,\n\tPluginArgs = unknown\n> = (\n\tcontext: EventPluginContext<Env, P, Data, PluginArgs>\n) => Response | Promise<Response>;\n/* end @cloudflare/workers-types */\n\ntype RouteHandler = {\n\troutePath: string;\n\tmountPath: string;\n\tmethod?: HTTPMethod;\n\tmodules: PagesFunction[];\n\tmiddlewares: PagesFunction[];\n};\n\n// inject `routes` via ESBuild\ndeclare const routes: RouteHandler[];\n\nfunction* executeRequest(request: Request, relativePathname: string) {\n\t// First, iterate through the routes (backwards) and execute \"middlewares\" on partial route matches\n\tfor (const route of [...routes].reverse()) {\n\t\tif (route.method && route.method !== request.method) {\n\t\t\tcontinue;\n\t\t}\n\n\t\t// replaces with \"\\\\$&\", this prepends a backslash to the matched string, e.g. \"[\" becomes \"\\[\"\n\t\tconst routeMatcher = match(route.routePath.replace(escapeRegex, \"\\\\$&\"), {\n\t\t\tend: false,\n\t\t});\n\t\tconst mountMatcher = match(route.mountPath.replace(escapeRegex, \"\\\\$&\"), {\n\t\t\tend: false,\n\t\t});\n\t\tconst matchResult = routeMatcher(relativePathname);\n\t\tconst mountMatchResult = mountMatcher(relativePathname);\n\t\tif (matchResult && mountMatchResult) {\n\t\t\tfor (const handler of route.middlewares.flat()) {\n\t\t\t\tyield {\n\t\t\t\t\thandler,\n\t\t\t\t\tparams: matchResult.params as Params,\n\t\t\t\t\tpath: mountMatchResult.path,\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\t// Then look for the first exact route match and execute its \"modules\"\n\tfor (const route of routes) {\n\t\tif (route.method && route.method !== request.method) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tconst routeMatcher = match(route.routePath.replace(escapeRegex, \"\\\\$&\"), {\n\t\t\tend: true,\n\t\t});\n\t\tconst mountMatcher = match(route.mountPath.replace(escapeRegex, \"\\\\$&\"), {\n\t\t\tend: false,\n\t\t});\n\t\tconst matchResult = routeMatcher(relativePathname);\n\t\tconst mountMatchResult = mountMatcher(relativePathname);\n\t\tif (matchResult && mountMatchResult && route.modules.length) {\n\t\t\tfor (const handler of route.modules.flat()) {\n\t\t\t\tyield {\n\t\t\t\t\thandler,\n\t\t\t\t\tparams: matchResult.params as Params,\n\t\t\t\t\tpath: matchResult.path,\n\t\t\t\t};\n\t\t\t}\n\t\t\tbreak;\n\t\t}\n\t}\n}\n\nexport default function (pluginArgs: unknown) {\n\tconst onRequest: PagesPluginFunction = async (workerContext) => {\n\t\tlet { request } = workerContext;\n\t\tconst { env, next } = workerContext;\n\t\tlet { data } = workerContext;\n\n\t\tconst url = new URL(request.url);\n\t\t// TODO: Replace this with something actually legible.\n\t\tconst relativePathname = `/${\n\t\t\turl.pathname.replace(workerContext.functionPath, \"\") || \"\"\n\t\t}`.replace(/^\\/\\//, \"/\");\n\n\t\tconst handlerIterator = executeRequest(request, relativePathname);\n\t\tconst pluginNext = async (input?: RequestInfo, init?: RequestInit) => {\n\t\t\tif (input !== undefined) {\n\t\t\t\tlet url = input;\n\t\t\t\tif (typeof input === \"string\") {\n\t\t\t\t\turl = new URL(input, request.url).toString();\n\t\t\t\t}\n\t\t\t\trequest = new Request(url, init);\n\t\t\t}\n\n\t\t\tconst result = handlerIterator.next();\n\t\t\t// Note we can't use `!result.done` because this doesn't narrow to the correct type\n\t\t\tif (result.done === false) {\n\t\t\t\tconst { handler, params, path } = result.value;\n\t\t\t\tconst context = {\n\t\t\t\t\trequest: new Request(request.clone()),\n\t\t\t\t\tfunctionPath: workerContext.functionPath + path,\n\t\t\t\t\tnext: pluginNext,\n\t\t\t\t\tparams,\n\t\t\t\t\tget data() {\n\t\t\t\t\t\treturn data;\n\t\t\t\t\t},\n\t\t\t\t\tset data(value) {\n\t\t\t\t\t\tif (typeof value !== \"object\" || value === null) {\n\t\t\t\t\t\t\tthrow new Error(\"context.data must be an object\");\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// user has overriden context.data, so we need to merge it with the existing data\n\t\t\t\t\t\tdata = value;\n\t\t\t\t\t},\n\t\t\t\t\tpluginArgs,\n\t\t\t\t\tenv,\n\t\t\t\t\twaitUntil: workerContext.waitUntil.bind(workerContext),\n\t\t\t\t\tpassThroughOnException:\n\t\t\t\t\t\tworkerContext.passThroughOnException.bind(workerContext),\n\t\t\t\t};\n\n\t\t\t\tconst response = await handler(context);\n\n\t\t\t\treturn cloneResponse(response);\n\t\t\t} else {\n\t\t\t\treturn next(request);\n\t\t\t}\n\t\t};\n\n\t\treturn pluginNext();\n\t};\n\n\treturn onRequest;\n}\n\n// This makes a Response mutable\nconst cloneResponse = (response: Response) =>\n\t// https://fetch.spec.whatwg.org/#null-body-status\n\tnew Response(\n\t\t[101, 204, 205, 304].includes(response.status) ? null : response.body,\n\t\tresponse\n\t);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAA,iBAAA,OAAA,UAAA;AASA,SAAA,QAAA,KAAA;AACA,UAAA,eAAA,KAAA,GAAA,GAAA;IACA,KAAA;IACA,KAAA;IACA,KAAA;AACA,aAAA;IACA;AACA,aAAA,aAAA,KAAA,KAAA;EACA;AACA;AAQA,SAAA,UAAA,KAAA,WAAA;AACA,SAAA,eAAA,KAAA,GAAA,MAAA,WAAA;AACA;AA0CA,SAAA,SAAA,KAAA;AACA,SAAA,UAAA,KAAA,QAAA;AACA;AASA,SAAA,YAAA,KAAA;AACA,SAAA,QAAA,QAAA,OAAA,QAAA,YAAA,OAAA,QAAA;AACA;AASA,SAAA,cAAA,KAAA;AACA,SAAA,UAAA,KAAA,QAAA;AACA;AASA,SAAA,QAAA,KAAA;AACA,SAAA,OAAA,UAAA,eAAA,aAAA,KAAA,KAAA;AACA;AASA,SAAA,UAAA,KAAA;AACA,SAAA,OAAA,YAAA,eAAA,aAAA,KAAA,OAAA;AACA;AAiBA,SAAA,WAAA,KAAA;AAEA,SAAA,QAAA,OAAA,IAAA,QAAA,OAAA,IAAA,SAAA,UAAA;AACA;AASA,SAAA,iBAAA,KAAA;AACA,SAAA,cAAA,GAAA,KAAA,iBAAA,OAAA,oBAAA,OAAA,qBAAA;AACA;AASA,SAAAA,OAAA,KAAA;AACA,SAAA,OAAA,QAAA,YAAA,QAAA;AACA;AAUA,SAAA,aAAA,KAAA,MAAA;AACA,MAAA;AACA,WAAA,eAAA;EACA,SAAA,IAAA;AACA,WAAA;EACA;AACA;;;ACnHA,SAAA,YAAA,KAAA;AACA,SAAA,OAAA,IAAA,QAAA,OAAA,MAAA;AACA;AAGA,IAAA,aACA,OAAA,cAAA,YAAA,YAAA,UAAA;AAEA,OAAA,UAAA,YAAA,YAAA,MAAA,KACA,OAAA,QAAA,YAAA,YAAA,IAAA,KACA,OAAA,UAAA,YAAA,YAAA,MAAA,KACA,WAAA;AACA,SAAA;AACA,EAAA,KACA,CAAA;AAKA,SAAA,kBAAA;AACA,SAAA;AACA;AAaA,SAAA,mBAAA,MAAA,SAAA,KAAA;AACA,QAAA,MAAA,OAAA;AACA,QAAA,aAAA,IAAA,aAAA,IAAA,cAAA,CAAA;AACA,QAAA,YAAA,WAAA,IAAA,MAAA,WAAA,IAAA,IAAA,QAAA;AACA,SAAA;AACA;;;ACpGA,IAAA,SAAA,gBAAA;AAQA,SAAA,iBAAA,MAAA,UAAA;AASA,MAAA;AACA,QAAA,cAAA;AACA,UAAA,sBAAA;AACA,UAAA,iBAAA;AACA,UAAA,MAAA,CAAA;AACA,QAAA,SAAA;AACA,QAAA,MAAA;AACA,UAAA,YAAA;AACA,UAAA,YAAA,UAAA;AACA,QAAA;AAEA,WAAA,eAAA,WAAA,qBAAA;AACA,gBAAA,qBAAA,aAAA,QAAA;AAKA,UAAA,YAAA,UAAA,SAAA,KAAA,MAAA,IAAA,SAAA,YAAA,QAAA,UAAA,gBAAA;AACA;MACA;AAEA,UAAA,KAAA,OAAA;AAEA,aAAA,QAAA;AACA,oBAAA,YAAA;IACA;AAEA,WAAA,IAAA,QAAA,EAAA,KAAA,SAAA;EACA,SAAA,KAAA;AACA,WAAA;EACA;AACA;AAOA,SAAA,qBAAA,IAAA,UAAA;AACA,QAAA,OAAA;AAOA,QAAA,MAAA,CAAA;AACA,MAAA;AACA,MAAA;AACA,MAAA;AACA,MAAA;AACA,MAAA;AAEA,MAAA,CAAA,QAAA,CAAA,KAAA,SAAA;AACA,WAAA;EACA;AAEA,MAAA,KAAA,KAAA,QAAA,YAAA,CAAA;AAGA,QAAA,eACA,YAAA,SAAA,SACA,SAAA,OAAA,aAAA,KAAA,aAAA,OAAA,CAAA,EAAA,IAAA,aAAA,CAAA,SAAA,KAAA,aAAA,OAAA,CAAA,CAAA,IACA;AAEA,MAAA,gBAAA,aAAA,QAAA;AACA,iBAAA,QAAA,iBAAA;AACA,UAAA,KAAA,IAAA,YAAA,CAAA,MAAA,YAAA,CAAA,KAAA;IACA,CAAA;EACA,OAAA;AACA,QAAA,KAAA,IAAA;AACA,UAAA,KAAA,IAAA,KAAA,IAAA;IACA;AAGA,gBAAA,KAAA;AACA,QAAA,aAAA,SAAA,SAAA,GAAA;AACA,gBAAA,UAAA,MAAA,KAAA;AACA,WAAA,IAAA,GAAA,IAAA,QAAA,QAAA,KAAA;AACA,YAAA,KAAA,IAAA,QAAA,CAAA,GAAA;MACA;IACA;EACA;AACA,QAAA,eAAA,CAAA,QAAA,QAAA,SAAA,KAAA;AACA,OAAA,IAAA,GAAA,IAAA,aAAA,QAAA,KAAA;AACA,UAAA,aAAA,CAAA;AACA,WAAA,KAAA,aAAA,GAAA;AACA,QAAA,MAAA;AACA,UAAA,KAAA,IAAA,QAAA,QAAA;IACA;EACA;AACA,SAAA,IAAA,KAAA,EAAA;AACA;;;AC9GA,IAAA,cAAA,cAAA,MAAA;;EAMA,YAAA,SAAA,WAAA,QAAA;AACA,UAAA,OAAA;AAAA,SAAA,UAAA;AAAA;AAEA,SAAA,OAAA,WAAA,UAAA,YAAA;AAIA,WAAA,eAAA,MAAA,WAAA,SAAA;AACA,SAAA,WAAA;EACA;AACA;;;ACdA,IAAA,YAAA;AAEA,SAAA,gBAAA,UAAA;AACA,SAAA,aAAA,UAAA,aAAA;AACA;AAWA,SAAA,YAAA,KAAA,eAAA,OAAA;AACA,QAAA,EAAA,MAAA,MAAA,MAAA,MAAA,WAAA,UAAA,UAAA,IAAA;AACA,SACA,GAAA,cAAA,YAAA,gBAAA,OAAA,IAAA,SAAA,MACA,OAAA,OAAA,IAAA,SAAA,MAAA,OAAA,GAAA,UAAA,OAAA;AAEA;AAQA,SAAA,cAAA,KAAA;AACA,QAAAC,SAAA,UAAA,KAAA,GAAA;AAEA,MAAA,CAAAA,QAAA;AACA,UAAA,IAAA,YAAA,uBAAA,KAAA;EACA;AAEA,QAAA,CAAA,UAAA,WAAA,OAAA,IAAA,MAAA,OAAA,IAAA,QAAA,IAAAA,OAAA,MAAA,CAAA;AACA,MAAA,OAAA;AACA,MAAA,YAAA;AAEA,QAAA,QAAA,UAAA,MAAA,GAAA;AACA,MAAA,MAAA,SAAA,GAAA;AACA,WAAA,MAAA,MAAA,GAAA,EAAA,EAAA,KAAA,GAAA;AACA,gBAAA,MAAA,IAAA;EACA;AAEA,MAAA,WAAA;AACA,UAAA,eAAA,UAAA,MAAA,MAAA;AACA,QAAA,cAAA;AACA,kBAAA,aAAA,CAAA;IACA;EACA;AAEA,SAAA,kBAAA,EAAA,MAAA,MAAA,MAAA,WAAA,MAAA,UAAA,UAAA,CAAA;AACA;AAEA,SAAA,kBAAA,YAAA;AACA,SAAA;IACA,UAAA,WAAA;IACA,WAAA,WAAA,aAAA;IACA,MAAA,WAAA,QAAA;IACA,MAAA,WAAA;IACA,MAAA,WAAA,QAAA;IACA,MAAA,WAAA,QAAA;IACA,WAAA,WAAA;EACA;AACA;AAEA,SAAA,YAAA,KAAA;AACA,MAAA,EAAA,OAAA,qBAAA,eAAA,mBAAA;AACA;EACA;AAEA,QAAA,EAAA,MAAA,WAAA,SAAA,IAAA;AAEA,QAAA,qBAAA,CAAA,YAAA,aAAA,QAAA,WAAA;AACA,qBAAA,QAAA,eAAA;AACA,QAAA,CAAA,IAAA,SAAA,GAAA;AACA,YAAA,IAAA,YAAA,uBAAA,mBAAA;IACA;EACA,CAAA;AAEA,MAAA,CAAA,UAAA,MAAA,OAAA,GAAA;AACA,UAAA,IAAA,YAAA,yCAAA,WAAA;EACA;AAEA,MAAA,CAAA,gBAAA,QAAA,GAAA;AACA,UAAA,IAAA,YAAA,wCAAA,UAAA;EACA;AAEA,MAAA,QAAA,MAAA,SAAA,MAAA,EAAA,CAAA,GAAA;AACA,UAAA,IAAA,YAAA,oCAAA,MAAA;EACA;AAEA,SAAA;AACA;AAGA,SAAA,QAAA,MAAA;AACA,QAAA,aAAA,OAAA,SAAA,WAAA,cAAA,IAAA,IAAA,kBAAA,IAAA;AACA,cAAA,UAAA;AACA,SAAA;AACA;;;ACtGA,IAAA,SAAA;AAEA,IAAA,iBAAA,CAAA,SAAA,QAAA,QAAA,SAAA,OAAA,UAAA,OAAA;AAkBA,SAAA,eAAA,UAAA;AACA,MAAA,EAAA,aAAA,aAAA;AACA,WAAA,SAAA;EACA;AAEA,QAAA,kBAAA,WAAA;AACA,QAAA,gBAAA,CAAA;AAGA,iBAAA,QAAA,WAAA;AAEA,UAAA,sBACA,gBAAA,KAAA,KAAA,gBAAA,KAAA,EAAA;AACA,QAAA,SAAA,mBAAA,qBAAA;AACA,oBAAA,KAAA,IAAA,gBAAA,KAAA;AACA,sBAAA,KAAA,IAAA;IACA;EACA,CAAA;AAEA,MAAA;AACA,WAAA,SAAA;EACA,UAAA;AAEA,WAAA,KAAA,aAAA,EAAA,QAAA,WAAA;AACA,sBAAA,KAAA,IAAA,cAAA,KAAA;IACA,CAAA;EACA;AACA;AAEA,SAAA,aAAA;AACA,MAAA,UAAA;AACA,QAAAC,UAAA;IACA,QAAA,MAAA;AACA,gBAAA;IACA;IACA,SAAA,MAAA;AACA,gBAAA;IACA;EACA;AAEA,MAAA,OAAA,qBAAA,eAAA,kBAAA;AACA,mBAAA,QAAA,UAAA;AAEA,MAAAA,QAAA,IAAA,IAAA,IAAA,SAAA;AACA,YAAA,SAAA;AACA,yBAAA,MAAA;AACA,uBAAA,QAAA,IAAA,EAAA,GAAA,UAAA,UAAA,GAAA,IAAA;UACA,CAAA;QACA;MACA;IACA,CAAA;EACA,OAAA;AACA,mBAAA,QAAA,UAAA;AACA,MAAAA,QAAA,IAAA,IAAA,MAAA;IACA,CAAA;EACA;AAEA,SAAAA;AACA;AAGA,IAAA;AACA,IAAA,OAAA,qBAAA,eAAA,kBAAA;AACA,WAAA,mBAAA,UAAA,UAAA;AACA,OAAA;AACA,WAAA,WAAA;AACA;;;AClFA,SAAA,SAAA,KAAA,MAAA,GAAA;AACA,MAAA,OAAA,QAAA,YAAA,QAAA,GAAA;AACA,WAAA;EACA;AACA,SAAA,IAAA,UAAA,MAAA,MAAA,GAAA,IAAA,OAAA,GAAA,GAAA;AACA;;;ACkCA,SAAA,yBAAA,KAAA,MAAA,OAAA;AACA,SAAA,eAAA,KAAA,MAAA;;IAEA;IACA,UAAA;IACA,cAAA;EACA,CAAA;AACA;AAgCA,SAAA,UAAA,QAAA;AACA,SAAA,OAAA,KAAA,MAAA,EACA,IAAA,SAAA,GAAA,mBAAA,GAAA,KAAA,mBAAA,OAAA,GAAA,CAAA,GAAA,EACA,KAAA,GAAA;AACA;AAUA,SAAA,qBACA,OAeA;AACA,MAAA,QAAA,KAAA,GAAA;AACA,WAAA;MACA,SAAA,MAAA;MACA,MAAA,MAAA;MACA,OAAA,MAAA;MACA,GAAA,iBAAA,KAAA;IACA;EACA,WAAA,QAAA,KAAA,GAAA;AACA,UAAA,SAMA;MACA,MAAA,MAAA;MACA,QAAA,qBAAA,MAAA,MAAA;MACA,eAAA,qBAAA,MAAA,aAAA;MACA,GAAA,iBAAA,KAAA;IACA;AAEA,QAAA,OAAA,gBAAA,eAAA,aAAA,OAAA,WAAA,GAAA;AACA,aAAA,SAAA,MAAA;IACA;AAEA,WAAA;EACA,OAAA;AACA,WAAA;EACA;AACA;AAGA,SAAA,qBAAA,QAAA;AACA,MAAA;AACA,WAAA,UAAA,MAAA,IAAA,iBAAA,MAAA,IAAA,OAAA,UAAA,SAAA,KAAA,MAAA;EACA,SAAA,KAAA;AACA,WAAA;EACA;AACA;AAGA,SAAA,iBAAA,KAAA;AACA,MAAA,OAAA,QAAA,YAAA,QAAA,MAAA;AACA,UAAA,iBAAA,CAAA;AACA,eAAA,YAAA,KAAA;AACA,UAAA,OAAA,UAAA,eAAA,KAAA,KAAA,QAAA,GAAA;AACA,uBAAA,QAAA,IAAA,IAAA,QAAA;MACA;IACA;AACA,WAAA;EACA,OAAA;AACA,WAAA,CAAA;EACA;AACA;AAOA,SAAA,+BAAA,WAAA,YAAA,IAAA;AACA,QAAA,OAAA,OAAA,KAAA,qBAAA,SAAA,CAAA;AACA,OAAA,KAAA;AAEA,MAAA,CAAA,KAAA,QAAA;AACA,WAAA;EACA;AAEA,MAAA,KAAA,CAAA,EAAA,UAAA,WAAA;AACA,WAAA,SAAA,KAAA,CAAA,GAAA,SAAA;EACA;AAEA,WAAA,eAAA,KAAA,QAAA,eAAA,GAAA,gBAAA;AACA,UAAA,aAAA,KAAA,MAAA,GAAA,YAAA,EAAA,KAAA,IAAA;AACA,QAAA,WAAA,SAAA,WAAA;AACA;IACA;AACA,QAAA,iBAAA,KAAA,QAAA;AACA,aAAA;IACA;AACA,WAAA,SAAA,YAAA,SAAA;EACA;AAEA,SAAA;AACA;AAQA,SAAA,kBAAA,YAAA;AAIA,QAAA,iBAAA,oBAAA,IAAA;AAGA,SAAA,mBAAA,YAAA,cAAA;AACA;AAEA,SAAA,mBAAA,YAAA,gBAAA;AACA,MAAA,cAAA,UAAA,GAAA;AAEA,UAAA,UAAA,eAAA,IAAA,UAAA;AACA,QAAA,YAAA,QAAA;AACA,aAAA;IACA;AAEA,UAAA,cAAA,CAAA;AAEA,mBAAA,IAAA,YAAA,WAAA;AAEA,eAAA,OAAA,OAAA,KAAA,UAAA,GAAA;AACA,UAAA,OAAA,WAAA,GAAA,MAAA,aAAA;AACA,oBAAA,GAAA,IAAA,mBAAA,WAAA,GAAA,GAAA,cAAA;MACA;IACA;AAEA,WAAA;EACA;AAEA,MAAA,MAAA,QAAA,UAAA,GAAA;AAEA,UAAA,UAAA,eAAA,IAAA,UAAA;AACA,QAAA,YAAA,QAAA;AACA,aAAA;IACA;AAEA,UAAA,cAAA,CAAA;AAEA,mBAAA,IAAA,YAAA,WAAA;AAEA,eAAA,QAAA,CAAA,SAAA;AACA,kBAAA,KAAA,mBAAA,MAAA,cAAA,CAAA;IACA,CAAA;AAEA,WAAA;EACA;AAEA,SAAA;AACA;;;ACxPA,SAAA,eAAA,KAAA;AACA,MAAA,gBAAA;AACA,MAAA,QAAA,IAAA,CAAA;AACA,MAAA,IAAA;AACA,SAAA,IAAA,IAAA,QAAA;AACA,UAAA,KAAA,IAAA,CAAA;AACA,UAAA,KAAA,IAAA,IAAA,CAAA;AACA,SAAA;AAEA,SAAA,OAAA,oBAAA,OAAA,mBAAA,SAAA,MAAA;AAEA;IACA;AACA,QAAA,OAAA,YAAA,OAAA,kBAAA;AACA,sBAAA;AACA,cAAA,GAAA,KAAA;IACA,WAAA,OAAA,UAAA,OAAA,gBAAA;AACA,cAAA,GAAA,IAAA,SAAA,MAAA,KAAA,eAAA,GAAA,IAAA,CAAA;AACA,sBAAA;IACA;EACA;AACA,SAAA;AACA;;;AChCA,IAAA,mBAAA;AASA,SAAA,qBAAA,SAAA;AACA,QAAA,gBAAA,QAAA,KAAA,CAAA,GAAA,MAAA,EAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA,IAAA,OAAA,EAAA,CAAA,CAAA;AAEA,SAAA,CAAA,OAAA,YAAA,MAAA;AACA,UAAA,SAAA,CAAA;AAEA,eAAA,QAAA,MAAA,MAAA,IAAA,EAAA,MAAA,SAAA,GAAA;AAGA,YAAA,cAAA,KAAA,QAAA,mBAAA,IAAA;AAEA,iBAAA,UAAA,eAAA;AACA,cAAA,QAAA,OAAA,WAAA;AAEA,YAAA,OAAA;AACA,iBAAA,KAAA,KAAA;AACA;QACA;MACA;IACA;AAEA,WAAA,4BAAA,MAAA;EACA;AACA;AAQA,SAAA,kCAAA,aAAA;AACA,MAAA,MAAA,QAAA,WAAA,GAAA;AACA,WAAA,kBAAA,GAAA,WAAA;EACA;AACA,SAAA;AACA;AAKA,SAAA,4BAAA,OAAA;AACA,MAAA,CAAA,MAAA,QAAA;AACA,WAAA,CAAA;EACA;AAEA,MAAA,aAAA;AAEA,QAAA,qBAAA,WAAA,CAAA,EAAA,YAAA;AACA,QAAA,oBAAA,WAAA,WAAA,SAAA,CAAA,EAAA,YAAA;AAGA,MAAA,mBAAA,QAAA,gBAAA,MAAA,MAAA,mBAAA,QAAA,kBAAA,MAAA,IAAA;AACA,iBAAA,WAAA,MAAA,CAAA;EACA;AAGA,MAAA,kBAAA,QAAA,eAAA,MAAA,IAAA;AACA,iBAAA,WAAA,MAAA,GAAA,EAAA;EACA;AAGA,SAAA,WACA,MAAA,GAAA,gBAAA,EACA,IAAA,YAAA;IACA,GAAA;IACA,UAAA,MAAA,YAAA,WAAA,CAAA,EAAA;IACA,UAAA,MAAA,YAAA;EACA,EAAA,EACA,QAAA;AACA;AAEA,IAAA,sBAAA;AAKA,SAAA,gBAAA,IAAA;AACA,MAAA;AACA,QAAA,CAAA,MAAA,OAAA,OAAA,YAAA;AACA,aAAA;IACA;AACA,WAAA,GAAA,QAAA;EACA,SAAA,GAAA;AAGA,WAAA;EACA;AACA;AAKA,SAAA,KAAAC,YAAA;AACA,QAAA,iBAAA;AACA,QAAA,aAAA;AAGA,SAAA,CAAA,SAAA;AACA,QAAA,KAAA,MAAA,cAAA,GAAA;AACA,aAAA;QACA,UAAA;MACA;IACA;AAEA,UAAA,YAAA,KAAA,MAAA,UAAA;AACA,QAAA,CAAA,WAAA;AACA,aAAA;IACA;AAEA,QAAA;AACA,QAAA;AACA,QAAA;AACA,QAAA;AACA,QAAA;AAEA,QAAA,UAAA,CAAA,GAAA;AACA,qBAAA,UAAA,CAAA;AAEA,UAAA,cAAA,aAAA,YAAA,GAAA;AACA,UAAA,aAAA,cAAA,CAAA,MAAA,KAAA;AACA;MACA;AAEA,UAAA,cAAA,GAAA;AACA,iBAAA,aAAA,OAAA,GAAA,WAAA;AACA,iBAAA,aAAA,OAAA,cAAA,CAAA;AACA,cAAA,YAAA,OAAA,QAAA,SAAA;AACA,YAAA,YAAA,GAAA;AACA,yBAAA,aAAA,OAAA,YAAA,CAAA;AACA,mBAAA,OAAA,OAAA,GAAA,SAAA;QACA;MACA;AACA,iBAAA;IACA;AAEA,QAAA,QAAA;AACA,iBAAA;AACA,mBAAA;IACA;AAEA,QAAA,WAAA,eAAA;AACA,mBAAA;AACA,qBAAA;IACA;AAEA,QAAA,iBAAA,QAAA;AACA,mBAAA,cAAA;AACA,qBAAA,WAAA,GAAA,YAAA,eAAA;IACA;AAEA,UAAA,WAAA,eAAA,CAAA,WAAA,UAAA,OAAA,EAAA,CAAA,GAAA,kBAAA,QAAA,GAAA,YAAA,QAAA,QAAA,GAAA,SAAA,CAAA,CAAA,IAAA,UAAA,CAAA,EAAA,OAAA,CAAA,IAAA,UAAA,CAAA;AACA,UAAA,WAAA,UAAA,CAAA,MAAA;AACA,UAAA,aACA,YAAA,YAAA,CAAA,SAAA,WAAA,GAAA,KAAA,CAAA,SAAA,WAAA,GAAA,KAAA,SAAA,QAAA,KAAA,MAAA;AAKA,UAAA,SAAA,CAAA,cAAA,aAAA,UAAA,CAAA,SAAA,SAAA,eAAA;AAEA,WAAA;MACA;MACA,QAAA,eAAA,CAAAA,YAAA,gBAAA,QAAA,GAAA,QAAA,CAAA,CAAA;MACA,UAAA;MACA,QAAA,SAAA,UAAA,CAAA,GAAA,EAAA,KAAA;MACA,OAAA,SAAA,UAAA,CAAA,GAAA,EAAA,KAAA;MACA;IACA;EACA;AACA;AAQA,SAAA,oBAAAA,YAAA;AACA,SAAA,CAAA,IAAA,KAAAA,UAAA,CAAA;AACA;;;AClLA,SAAA,cAAA;AACA,QAAA,aAAA,OAAA,YAAA;AACA,QAAA,QAAA,aAAA,oBAAA,QAAA,IAAA,CAAA;AACA,WAAA,QAAA,KAAA;AACA,QAAA,YAAA;AACA,UAAA,MAAA,IAAA,GAAA,GAAA;AACA,eAAA;MACA;AACA,YAAA,IAAA,GAAA;AACA,aAAA;IACA;AAEA,aAAA,IAAA,GAAA,IAAA,MAAA,QAAA,KAAA;AACA,YAAA,QAAA,MAAA,CAAA;AACA,UAAA,UAAA,KAAA;AACA,eAAA;MACA;IACA;AACA,UAAA,KAAA,GAAA;AACA,WAAA;EACA;AAEA,WAAA,UAAA,KAAA;AACA,QAAA,YAAA;AACA,YAAA,OAAA,GAAA;IACA,OAAA;AACA,eAAA,IAAA,GAAA,IAAA,MAAA,QAAA,KAAA;AACA,YAAA,MAAA,CAAA,MAAA,KAAA;AACA,gBAAA,OAAA,GAAA,CAAA;AACA;QACA;MACA;IACA;EACA;AACA,SAAA,CAAA,SAAA,SAAA;AACA;;;ACzBA,SAAA,QAAA;AACA,QAAA,MAAA;AACA,QAAA,SAAA,IAAA,UAAA,IAAA;AAEA,MAAA,UAAA,OAAA,YAAA;AACA,WAAA,OAAA,WAAA,EAAA,QAAA,MAAA,EAAA;EACA;AAEA,QAAA,gBACA,UAAA,OAAA,kBAAA,MAAA,OAAA,gBAAA,IAAA,WAAA,CAAA,CAAA,EAAA,CAAA,IAAA,MAAA,KAAA,OAAA,IAAA;AAIA,UAAA,CAAA,GAAA,IAAA,MAAA,MAAA,MAAA,MAAA;IAAA;IAAA;;OAEA,KAAA,cAAA,IAAA,OAAA,IAAA,GAAA,SAAA,EAAA;;EACA;AACA;AAEA,SAAA,kBAAA,OAAA;AACA,SAAA,MAAA,aAAA,MAAA,UAAA,SAAA,MAAA,UAAA,OAAA,CAAA,IAAA;AACA;AA6BA,SAAA,sBAAA,OAAA,OAAA,MAAA;AACA,QAAA,YAAA,MAAA,YAAA,MAAA,aAAA,CAAA;AACA,QAAA,SAAA,UAAA,SAAA,UAAA,UAAA,CAAA;AACA,QAAA,iBAAA,OAAA,CAAA,IAAA,OAAA,CAAA,KAAA,CAAA;AACA,MAAA,CAAA,eAAA,OAAA;AACA,mBAAA,QAAA,SAAA;EACA;AACA,MAAA,CAAA,eAAA,MAAA;AACA,mBAAA,OAAA,QAAA;EACA;AACA;AASA,SAAA,sBAAA,OAAA,cAAA;AACA,QAAA,iBAAA,kBAAA,KAAA;AACA,MAAA,CAAA,gBAAA;AACA;EACA;AAEA,QAAA,mBAAA,EAAA,MAAA,WAAA,SAAA,KAAA;AACA,QAAA,mBAAA,eAAA;AACA,iBAAA,YAAA,EAAA,GAAA,kBAAA,GAAA,kBAAA,GAAA,aAAA;AAEA,MAAA,gBAAA,UAAA,cAAA;AACA,UAAA,aAAA,EAAA,GAAA,oBAAA,iBAAA,MAAA,GAAA,aAAA,KAAA;AACA,mBAAA,UAAA,OAAA;EACA;AACA;AA+EA,SAAA,wBAAA,WAAA;AAEA,MAAA,aAAA,UAAA,qBAAA;AACA,WAAA;EACA;AAEA,MAAA;AAGA,6BAAA,WAAA,uBAAA,IAAA;EACA,SAAA,KAAA;EAEA;AAEA,SAAA;AACA;AAQA,SAAA,SAAA,YAAA;AACA,SAAA,MAAA,QAAA,UAAA,IAAA,aAAA,CAAA,UAAA;AACA;;;AC5LA,SAAA,kBAAA;AACA,SAAA,OAAA,8BAAA,eAAA,CAAA,CAAA;AACA;;;ACZA,SAAA,YAAA;AAGA,SACA,CAAA,gBAAA,KACA,OAAA,UAAA,SAAA,KAAA,OAAA,YAAA,cAAA,UAAA,CAAA,MAAA;AAEA;AAQA,SAAA,eAAA,KAAA,SAAA;AAEA,SAAA,IAAA,QAAA,OAAA;AACA;;;ACIA,SAAA,UAAA,OAAA,QAAA,UAAA,gBAAA,UAAA;AACA,MAAA;AAEA,WAAA,MAAA,IAAA,OAAA,OAAA,aAAA;EACA,SAAA,KAAA;AACA,WAAA,EAAA,OAAA,yBAAA,OAAA;EACA;AACA;AAGA,SAAA,gBAEA,QAEA,QAAA,GAEA,UAAA,MAAA,MACA;AACA,QAAA,aAAA,UAAA,QAAA,KAAA;AAEA,MAAA,SAAA,UAAA,IAAA,SAAA;AACA,WAAA,gBAAA,QAAA,QAAA,GAAA,OAAA;EACA;AAEA,SAAA;AACA;AAWA,SAAA,MACA,KACA,OACA,QAAA,UACA,gBAAA,UACA,OAAA,YAAA,GACA;AACA,QAAA,CAAA,SAAA,SAAA,IAAA;AAGA,MAAA,UAAA,QAAA,CAAA,UAAA,WAAA,QAAA,EAAA,SAAA,OAAA,KAAA,KAAA,CAAAC,OAAA,KAAA,GAAA;AACA,WAAA;EACA;AAEA,QAAA,cAAA,eAAA,KAAA,KAAA;AAIA,MAAA,CAAA,YAAA,WAAA,UAAA,GAAA;AACA,WAAA;EACA;AAOA,MAAA,MAAA,+BAAA,GAAA;AACA,WAAA;EACA;AAGA,MAAA,UAAA,GAAA;AAEA,WAAA,YAAA,QAAA,WAAA,EAAA;EACA;AAGA,MAAA,QAAA,KAAA,GAAA;AACA,WAAA;EACA;AAGA,QAAA,kBAAA;AACA,MAAA,mBAAA,OAAA,gBAAA,WAAA,YAAA;AACA,QAAA;AACA,YAAA,YAAA,gBAAA,OAAA;AAEA,aAAA,MAAA,IAAA,WAAA,QAAA,GAAA,eAAA,IAAA;IACA,SAAA,KAAA;IAEA;EACA;AAKA,QAAA,aAAA,MAAA,QAAA,KAAA,IAAA,CAAA,IAAA,CAAA;AACA,MAAA,WAAA;AAIA,QAAA,YAAA,qBAAA,KAAA;AAEA,aAAA,YAAA,WAAA;AAEA,QAAA,CAAA,OAAA,UAAA,eAAA,KAAA,WAAA,QAAA,GAAA;AACA;IACA;AAEA,QAAA,YAAA,eAAA;AACA,iBAAA,QAAA,IAAA;AACA;IACA;AAGA,UAAA,aAAA,UAAA,QAAA;AACA,eAAA,QAAA,IAAA,MAAA,UAAA,YAAA,QAAA,GAAA,eAAA,IAAA;AAEA;EACA;AAGA,YAAA,KAAA;AAGA,SAAA;AACA;AAcA,SAAA,eACA,KAGA,OACA;AACA,MAAA;AACA,QAAA,QAAA,YAAA,SAAA,OAAA,UAAA,YAAA,MAAA,SAAA;AACA,aAAA;IACA;AAEA,QAAA,QAAA,iBAAA;AACA,aAAA;IACA;AAKA,QAAA,OAAA,WAAA,eAAA,UAAA,QAAA;AACA,aAAA;IACA;AAGA,QAAA,OAAA,WAAA,eAAA,UAAA,QAAA;AACA,aAAA;IACA;AAGA,QAAA,OAAA,aAAA,eAAA,UAAA,UAAA;AACA,aAAA;IACA;AAGA,QAAA,iBAAA,KAAA,GAAA;AACA,aAAA;IACA;AAEA,QAAA,OAAA,UAAA,YAAA,UAAA,OAAA;AACA,aAAA;IACA;AAGA,QAAA,UAAA,QAAA;AACA,aAAA;IACA;AAEA,QAAA,OAAA,UAAA,YAAA;AACA,aAAA,cAAA,gBAAA,KAAA;IACA;AAEA,QAAA,OAAA,UAAA,UAAA;AACA,aAAA,IAAA,OAAA,KAAA;IACA;AAGA,QAAA,OAAA,UAAA,UAAA;AACA,aAAA,YAAA,OAAA,KAAA;IACA;AAMA,WAAA,WAAA,OAAA,eAAA,KAAA,EAAA,YAAA;EACA,SAAA,KAAA;AACA,WAAA,yBAAA;EACA;AACA;AAGA,SAAA,WAAA,OAAA;AAEA,SAAA,CAAA,CAAA,UAAA,KAAA,EAAA,MAAA,OAAA,EAAA;AACA;AAIA,SAAA,SAAA,OAAA;AACA,SAAA,WAAA,KAAA,UAAA,KAAA,CAAA;AACA;;;AC1NA,IAAA,cAAA;AAEA,SAAA,UAAA,UAAA;AACA,QAAA,QAAA,YAAA,KAAA,QAAA;AACA,SAAA,QAAA,MAAA,MAAA,CAAA,IAAA,CAAA;AACA;AA6IA,SAAA,SAAA,MAAA,KAAA;AACA,MAAA,IAAA,UAAA,IAAA,EAAA,CAAA;AACA,MAAA,OAAA,EAAA,OAAA,IAAA,SAAA,EAAA,MAAA,KAAA;AACA,QAAA,EAAA,OAAA,GAAA,EAAA,SAAA,IAAA,MAAA;EACA;AACA,SAAA;AACA;;;ACjLA,IAAA;CAAA,SAAAC,SAAA;AAEA,QAAA,UAAA;AAAA,EAAAA,QAAAA,QAAA,SAAA,IAAA,OAAA,IAAA;AAEA,QAAA,WAAA;AAAA,EAAAA,QAAAA,QAAA,UAAA,IAAA,QAAA,IAAA;AAEA,QAAA,WAAA;AAAA,EAAAA,QAAAA,QAAA,UAAA,IAAA,QAAA,IAAA;AACA,GAAA,WAAA,SAAA,CAAA,EAAA;AAYA,SAAA,oBAAA,OAAA;AACA,SAAA,IAAA,YAAA,CAAAC,aAAA;AACA,IAAAA,SAAA,KAAA;EACA,CAAA;AACA;AAQA,SAAA,oBAAA,QAAA;AACA,SAAA,IAAA,YAAA,CAAA,GAAA,WAAA;AACA,WAAA,MAAA;EACA,CAAA;AACA;AAMA,IAAA,cAAA,MAAA;EACA,SAAA;AAAA,SAAA,SAAA,OAAA;EAAA;EACA,UAAA;AAAA,SAAA,YAAA,CAAA;EAAA;EAGA,YACA,UACA;AAAA;AAAA,gBAAA,UAAA,OAAA,KAAA,IAAA;AAAA,gBAAA,UAAA,QAAA,KAAA,IAAA;AAAA,gBAAA,UAAA,QAAA,KAAA,IAAA;AAAA,gBAAA,UAAA,QAAA,KAAA,IAAA;AAAA,gBAAA,UAAA,QAAA,KAAA,IAAA;AAAA,gBAAA,UAAA,QAAA,KAAA,IAAA;AACA,QAAA;AACA,eAAA,KAAA,UAAA,KAAA,OAAA;IACA,SAAA,GAAA;AACA,WAAA,QAAA,CAAA;IACA;EACA;;EAGA,KACA,aACA,YACA;AACA,WAAA,IAAA,YAAA,CAAAA,UAAA,WAAA;AACA,WAAA,UAAA,KAAA;QACA;QACA,YAAA;AACA,cAAA,CAAA,aAAA;AAGA,YAAAA,SAAA,MAAA;UACA,OAAA;AACA,gBAAA;AACA,cAAAA,SAAA,YAAA,MAAA,CAAA;YACA,SAAA,GAAA;AACA,qBAAA,CAAA;YACA;UACA;QACA;QACA,YAAA;AACA,cAAA,CAAA,YAAA;AACA,mBAAA,MAAA;UACA,OAAA;AACA,gBAAA;AACA,cAAAA,SAAA,WAAA,MAAA,CAAA;YACA,SAAA,GAAA;AACA,qBAAA,CAAA;YACA;UACA;QACA;MACA,CAAA;AACA,WAAA,iBAAA;IACA,CAAA;EACA;;EAGA,MACA,YACA;AACA,WAAA,KAAA,KAAA,SAAA,KAAA,UAAA;EACA;;EAGA,QAAA,WAAA;AACA,WAAA,IAAA,YAAA,CAAAA,UAAA,WAAA;AACA,UAAA;AACA,UAAA;AAEA,aAAA,KAAA;QACA,WAAA;AACA,uBAAA;AACA,gBAAA;AACA,cAAA,WAAA;AACA,sBAAA;UACA;QACA;QACA,YAAA;AACA,uBAAA;AACA,gBAAA;AACA,cAAA,WAAA;AACA,sBAAA;UACA;QACA;MACA,EAAA,KAAA,MAAA;AACA,YAAA,YAAA;AACA,iBAAA,GAAA;AACA;QACA;AAEA,QAAAA,SAAA,GAAA;MACA,CAAA;IACA,CAAA;EACA;;EAGA,UAAA;AAAA,SAAA,WAAA,CAAA,UAAA;AACA,WAAA,WAAA,OAAA,UAAA,KAAA;IACA;EAAA;;EAGA,UAAA;AAAA,SAAA,UAAA,CAAA,WAAA;AACA,WAAA,WAAA,OAAA,UAAA,MAAA;IACA;EAAA;;EAGA,UAAA;AAAA,SAAA,aAAA,CAAA,OAAA,UAAA;AACA,UAAA,KAAA,WAAA,OAAA,SAAA;AACA;MACA;AAEA,UAAA,WAAA,KAAA,GAAA;AACA,aAAA,MAAA,KAAA,KAAA,UAAA,KAAA,OAAA;AACA;MACA;AAEA,WAAA,SAAA;AACA,WAAA,SAAA;AAEA,WAAA,iBAAA;IACA;EAAA;;EAGA,UAAA;AAAA,SAAA,mBAAA,MAAA;AACA,UAAA,KAAA,WAAA,OAAA,SAAA;AACA;MACA;AAEA,YAAA,iBAAA,KAAA,UAAA,MAAA;AACA,WAAA,YAAA,CAAA;AAEA,qBAAA,QAAA,CAAAC,aAAA;AACA,YAAAA,SAAA,CAAA,GAAA;AACA;QACA;AAEA,YAAA,KAAA,WAAA,OAAA,UAAA;AAEA,UAAAA,SAAA,CAAA,EAAA,KAAA,MAAA;QACA;AAEA,YAAA,KAAA,WAAA,OAAA,UAAA;AACA,UAAAA,SAAA,CAAA,EAAA,KAAA,MAAA;QACA;AAEA,QAAAA,SAAA,CAAA,IAAA;MACA,CAAA;IACA;EAAA;AACA;;;ACjLA,SAAA,kBAAA,OAAA;AACA,QAAA,SAAA,CAAA;AAEA,WAAA,UAAA;AACA,WAAA,UAAA,UAAA,OAAA,SAAA;EACA;AAQA,WAAA,OAAA,MAAA;AACA,WAAA,OAAA,OAAA,OAAA,QAAA,IAAA,GAAA,CAAA,EAAA,CAAA;EACA;AAYA,WAAA,IAAA,cAAA;AACA,QAAA,CAAA,QAAA,GAAA;AACA,aAAA,oBAAA,IAAA,YAAA,sDAAA,CAAA;IACA;AAGA,UAAA,OAAA,aAAA;AACA,QAAA,OAAA,QAAA,IAAA,MAAA,IAAA;AACA,aAAA,KAAA,IAAA;IACA;AACA,SAAA,KACA,KAAA,MAAA,OAAA,IAAA,CAAA,EAIA;MAAA;MAAA,MACA,OAAA,IAAA,EAAA,KAAA,MAAA,MAAA;MAEA,CAAA;IACA;AACA,WAAA;EACA;AAWA,WAAA,MAAA,SAAA;AACA,WAAA,IAAA,YAAA,CAAAC,UAAA,WAAA;AACA,UAAA,UAAA,OAAA;AAEA,UAAA,CAAA,SAAA;AACA,eAAAA,SAAA,IAAA;MACA;AAGA,YAAA,qBAAA,WAAA,MAAA;AACA,YAAA,WAAA,UAAA,GAAA;AACA,UAAAA,SAAA,KAAA;QACA;MACA,GAAA,OAAA;AAGA,aAAA,QAAA,UAAA;AACA,aAAA,oBAAA,IAAA,EAAA,KAAA,MAAA;AACA,cAAA,CAAA,EAAA,SAAA;AACA,yBAAA,kBAAA;AACA,YAAAA,SAAA,IAAA;UACA;QACA,GAAA,MAAA;MACA,CAAA;IACA,CAAA;EACA;AAEA,SAAA;IACA,GAAA;IACA;IACA;EACA;AACA;;;ACtGA,IAAAC,UAAA,gBAAA;AAgBA,IAAA,sBAAA;EACA,YAAA,MAAA,KAAA,IAAA,IAAA;AACA;AAuBA,SAAA,wBAAA;AACA,QAAA,EAAA,YAAA,IAAAA;AACA,MAAA,CAAA,eAAA,CAAA,YAAA,KAAA;AACA,WAAA;EACA;AAuBA,QAAA,aAAA,KAAA,IAAA,IAAA,YAAA,IAAA;AAEA,SAAA;IACA,KAAA,MAAA,YAAA,IAAA;IACA;EACA;AACA;AAMA,SAAA,qBAAA;AACA,MAAA;AACA,UAAA,YAAA,eAAA,QAAA,YAAA;AACA,WAAA,UAAA;EACA,SAAA,GAAA;AACA,WAAA;EACA;AACA;AAKA,IAAA,sBAAA,UAAA,IAAA,mBAAA,IAAA,sBAAA;AAEA,IAAA,kBACA,wBAAA,SACA,sBACA;EACA,YAAA,OAAA,oBAAA,aAAA,oBAAA,IAAA,KAAA;AACA;AAKA,IAAA,yBAAA,oBAAA,WAAA,KAAA,mBAAA;AAaA,IAAA,qBAAA,gBAAA,WAAA,KAAA,eAAA;AAaA,IAAA;AAMA,IAAA,gCAAA,MAAA;AAKA,QAAA,EAAA,YAAA,IAAAC;AACA,MAAA,CAAA,eAAA,CAAA,YAAA,KAAA;AACA,wCAAA;AACA,WAAA;EACA;AAEA,QAAA,YAAA,OAAA;AACA,QAAA,iBAAA,YAAA,IAAA;AACA,QAAA,UAAA,KAAA,IAAA;AAGA,QAAA,kBAAA,YAAA,aACA,KAAA,IAAA,YAAA,aAAA,iBAAA,OAAA,IACA;AACA,QAAA,uBAAA,kBAAA;AAQA,QAAA,kBAAA,YAAA,UAAA,YAAA,OAAA;AACA,QAAA,qBAAA,OAAA,oBAAA;AAEA,QAAA,uBAAA,qBAAA,KAAA,IAAA,kBAAA,iBAAA,OAAA,IAAA;AACA,QAAA,4BAAA,uBAAA;AAEA,MAAA,wBAAA,2BAAA;AAEA,QAAA,mBAAA,sBAAA;AACA,0CAAA;AACA,aAAA,YAAA;IACA,OAAA;AACA,0CAAA;AACA,aAAA;IACA;EACA;AAGA,sCAAA;AACA,SAAA;AACA,GAAA;;;ACzKA,SAAA,eAAA,SAAA,QAAA,CAAA,GAAA;AACA,SAAA,CAAA,SAAA,KAAA;AACA;AAOA,SAAA,kBAAA,UAAA,SAAA;AACA,QAAA,CAAA,SAAA,KAAA,IAAA;AACA,SAAA,CAAA,SAAA,CAAA,GAAA,OAAA,OAAA,CAAA;AACA;AAMA,SAAA,oBACA,UACA,UACA;AACA,QAAA,gBAAA,SAAA,CAAA;AACA,gBAAA,QAAA,CAAA,iBAAA;AACA,UAAA,mBAAA,aAAA,CAAA,EAAA;AACA,aAAA,cAAA,gBAAA;EACA,CAAA;AACA;AAEA,SAAA,WAAA,OAAA,aAAA;AACA,QAAA,OAAA,eAAA,IAAA,YAAA;AACA,SAAA,KAAA,OAAA,KAAA;AACA;AAKA,SAAA,kBAAA,UAAA,aAAA;AACA,QAAA,CAAA,YAAA,KAAA,IAAA;AAGA,MAAA,QAAA,KAAA,UAAA,UAAA;AAEA,WAAA,OAAA,MAAA;AACA,QAAA,OAAA,UAAA,UAAA;AACA,cAAA,OAAA,SAAA,WAAA,QAAA,OAAA,CAAA,WAAA,OAAA,WAAA,GAAA,IAAA;IACA,OAAA;AACA,YAAA,KAAA,OAAA,SAAA,WAAA,WAAA,MAAA,WAAA,IAAA,IAAA;IACA;EACA;AAEA,aAAA,QAAA,OAAA;AACA,UAAA,CAAA,aAAA,OAAA,IAAA;AAEA,WAAA;EAAA,KAAA,UAAA,WAAA;CAAA;AAEA,QAAA,OAAA,YAAA,YAAA,mBAAA,YAAA;AACA,aAAA,OAAA;IACA,OAAA;AACA,UAAA;AACA,UAAA;AACA,6BAAA,KAAA,UAAA,OAAA;MACA,SAAA,GAAA;AAIA,6BAAA,KAAA,UAAA,UAAA,OAAA,CAAA;MACA;AACA,aAAA,kBAAA;IACA;EACA;AAEA,SAAA,OAAA,UAAA,WAAA,QAAA,cAAA,KAAA;AACA;AAEA,SAAA,cAAA,SAAA;AACA,QAAA,cAAA,QAAA,OAAA,CAAA,KAAA,QAAA,MAAA,IAAA,QAAA,CAAA;AAEA,QAAA,SAAA,IAAA,WAAA,WAAA;AACA,MAAA,SAAA;AACA,aAAA,UAAA,SAAA;AACA,WAAA,IAAA,QAAA,MAAA;AACA,cAAA,OAAA;EACA;AAEA,SAAA;AACA;AAKA,SAAA,6BACA,YACA,aACA;AACA,QAAA,SAAA,OAAA,WAAA,SAAA,WAAA,WAAA,WAAA,MAAA,WAAA,IAAA,WAAA;AAEA,SAAA;IACA,kBAAA;MACA,MAAA;MACA,QAAA,OAAA;MACA,UAAA,WAAA;MACA,cAAA,WAAA;MACA,iBAAA,WAAA;IACA,CAAA;IACA;EACA;AACA;AAEA,IAAA,iCAAA;EACA,SAAA;EACA,UAAA;EACA,YAAA;EACA,aAAA;EACA,OAAA;EACA,eAAA;EACA,aAAA;AACA;AAKA,SAAA,+BAAA,MAAA;AACA,SAAA,+BAAA,IAAA;AACA;;;ACzIA,IAAA,sBAAA,KAAA;AAQA,SAAA,sBAAA,QAAA,MAAA,KAAA,IAAA,GAAA;AACA,QAAA,cAAA,SAAA,GAAA,UAAA,EAAA;AACA,MAAA,CAAA,MAAA,WAAA,GAAA;AACA,WAAA,cAAA;EACA;AAEA,QAAA,aAAA,KAAA,MAAA,GAAA,QAAA;AACA,MAAA,CAAA,MAAA,UAAA,GAAA;AACA,WAAA,aAAA;EACA;AAEA,SAAA;AACA;AAKA,SAAA,cAAA,QAAA,UAAA;AACA,SAAA,OAAA,QAAA,KAAA,OAAA,OAAA;AACA;AAKA,SAAA,cAAA,QAAA,UAAA,MAAA,KAAA,IAAA,GAAA;AACA,SAAA,cAAA,QAAA,QAAA,IAAA;AACA;AAMA,SAAA,iBACA,QACA,EAAA,YAAA,QAAA,GACA,MAAA,KAAA,IAAA,GACA;AACA,QAAA,oBAAA;IACA,GAAA;EACA;AAIA,QAAA,kBAAA,WAAA,QAAA,sBAAA;AACA,QAAA,mBAAA,WAAA,QAAA,aAAA;AAEA,MAAA,iBAAA;AAaA,eAAA,SAAA,gBAAA,KAAA,EAAA,MAAA,GAAA,GAAA;AACA,YAAA,CAAA,YAAA,UAAA,IAAA,MAAA,MAAA,KAAA,CAAA;AACA,YAAA,cAAA,SAAA,YAAA,EAAA;AACA,YAAA,SAAA,CAAA,MAAA,WAAA,IAAA,cAAA,MAAA;AACA,UAAA,CAAA,YAAA;AACA,0BAAA,MAAA,MAAA;MACA,OAAA;AACA,mBAAA,YAAA,WAAA,MAAA,GAAA,GAAA;AACA,4BAAA,QAAA,IAAA,MAAA;QACA;MACA;IACA;EACA,WAAA,kBAAA;AACA,sBAAA,MAAA,MAAA,sBAAA,kBAAA,GAAA;EACA,WAAA,eAAA,KAAA;AACA,sBAAA,MAAA,MAAA,KAAA;EACA;AAEA,SAAA;AACA;;;AChFA,SAAA,YAAA,SAAA;AAEA,QAAA,eAAA,mBAAA;AAEA,QAAA,UAAA;IACA,KAAA,MAAA;IACA,MAAA;IACA,WAAA;IACA,SAAA;IACA,UAAA;IACA,QAAA;IACA,QAAA;IACA,gBAAA;IACA,QAAA,MAAA,cAAA,OAAA;EACA;AAEA,MAAA,SAAA;AACA,kBAAA,SAAA,OAAA;EACA;AAEA,SAAA;AACA;AAcA,SAAA,cAAA,SAAA,UAAA,CAAA,GAAA;AACA,MAAA,QAAA,MAAA;AACA,QAAA,CAAA,QAAA,aAAA,QAAA,KAAA,YAAA;AACA,cAAA,YAAA,QAAA,KAAA;IACA;AAEA,QAAA,CAAA,QAAA,OAAA,CAAA,QAAA,KAAA;AACA,cAAA,MAAA,QAAA,KAAA,MAAA,QAAA,KAAA,SAAA,QAAA,KAAA;IACA;EACA;AAEA,UAAA,YAAA,QAAA,aAAA,mBAAA;AAEA,MAAA,QAAA,gBAAA;AACA,YAAA,iBAAA,QAAA;EACA;AACA,MAAA,QAAA,KAAA;AAEA,YAAA,MAAA,QAAA,IAAA,WAAA,KAAA,QAAA,MAAA,MAAA;EACA;AACA,MAAA,QAAA,SAAA,QAAA;AACA,YAAA,OAAA,QAAA;EACA;AACA,MAAA,CAAA,QAAA,OAAA,QAAA,KAAA;AACA,YAAA,MAAA,GAAA,QAAA;EACA;AACA,MAAA,OAAA,QAAA,YAAA,UAAA;AACA,YAAA,UAAA,QAAA;EACA;AACA,MAAA,QAAA,gBAAA;AACA,YAAA,WAAA;EACA,WAAA,OAAA,QAAA,aAAA,UAAA;AACA,YAAA,WAAA,QAAA;EACA,OAAA;AACA,UAAA,WAAA,QAAA,YAAA,QAAA;AACA,YAAA,WAAA,YAAA,IAAA,WAAA;EACA;AACA,MAAA,QAAA,SAAA;AACA,YAAA,UAAA,QAAA;EACA;AACA,MAAA,QAAA,aAAA;AACA,YAAA,cAAA,QAAA;EACA;AACA,MAAA,CAAA,QAAA,aAAA,QAAA,WAAA;AACA,YAAA,YAAA,QAAA;EACA;AACA,MAAA,CAAA,QAAA,aAAA,QAAA,WAAA;AACA,YAAA,YAAA,QAAA;EACA;AACA,MAAA,OAAA,QAAA,WAAA,UAAA;AACA,YAAA,SAAA,QAAA;EACA;AACA,MAAA,QAAA,QAAA;AACA,YAAA,SAAA,QAAA;EACA;AACA;AAaA,SAAA,aAAA,SAAA,QAAA;AACA,MAAA,UAAA,CAAA;AACA,MAAA,QAAA;AACA,cAAA,EAAA,OAAA;EACA,WAAA,QAAA,WAAA,MAAA;AACA,cAAA,EAAA,QAAA,SAAA;EACA;AAEA,gBAAA,SAAA,OAAA;AACA;AAWA,SAAA,cAAA,SAAA;AACA,SAAA,kBAAA;IACA,KAAA,GAAA,QAAA;IACA,MAAA,QAAA;;IAEA,SAAA,IAAA,KAAA,QAAA,UAAA,GAAA,EAAA,YAAA;IACA,WAAA,IAAA,KAAA,QAAA,YAAA,GAAA,EAAA,YAAA;IACA,QAAA,QAAA;IACA,QAAA,QAAA;IACA,KAAA,OAAA,QAAA,QAAA,YAAA,OAAA,QAAA,QAAA,WAAA,GAAA,QAAA,QAAA;IACA,UAAA,QAAA;IACA,OAAA;MACA,SAAA,QAAA;MACA,aAAA,QAAA;MACA,YAAA,QAAA;MACA,YAAA,QAAA;IACA;EACA,CAAA;AACA;;;AClHA,IAAA,0BAAA;AAMA,IAAA,QAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;EAuDA,cAAA;AACA,SAAA,sBAAA;AACA,SAAA,kBAAA,CAAA;AACA,SAAA,mBAAA,CAAA;AACA,SAAA,eAAA,CAAA;AACA,SAAA,eAAA,CAAA;AACA,SAAA,QAAA,CAAA;AACA,SAAA,QAAA,CAAA;AACA,SAAA,SAAA,CAAA;AACA,SAAA,YAAA,CAAA;AACA,SAAA,yBAAA,CAAA;EACA;;;;;EAMA,OAAA,MAAA,OAAA;AACA,UAAA,WAAA,IAAA,MAAA;AACA,QAAA,OAAA;AACA,eAAA,eAAA,CAAA,GAAA,MAAA,YAAA;AACA,eAAA,QAAA,EAAA,GAAA,MAAA,MAAA;AACA,eAAA,SAAA,EAAA,GAAA,MAAA,OAAA;AACA,eAAA,YAAA,EAAA,GAAA,MAAA,UAAA;AACA,eAAA,QAAA,MAAA;AACA,eAAA,SAAA,MAAA;AACA,eAAA,QAAA,MAAA;AACA,eAAA,WAAA,MAAA;AACA,eAAA,mBAAA,MAAA;AACA,eAAA,eAAA,MAAA;AACA,eAAA,mBAAA,CAAA,GAAA,MAAA,gBAAA;AACA,eAAA,kBAAA,MAAA;AACA,eAAA,eAAA,CAAA,GAAA,MAAA,YAAA;AACA,eAAA,yBAAA,EAAA,GAAA,MAAA,uBAAA;IACA;AACA,WAAA;EACA;;;;;EAMA,iBAAA,UAAA;AACA,SAAA,gBAAA,KAAA,QAAA;EACA;;;;EAKA,kBAAA,UAAA;AACA,SAAA,iBAAA,KAAA,QAAA;AACA,WAAA;EACA;;;;EAKA,QAAA,MAAA;AACA,SAAA,QAAA,QAAA,CAAA;AACA,QAAA,KAAA,UAAA;AACA,oBAAA,KAAA,UAAA,EAAA,KAAA,CAAA;IACA;AACA,SAAA,sBAAA;AACA,WAAA;EACA;;;;EAKA,UAAA;AACA,WAAA,KAAA;EACA;;;;EAKA,oBAAA;AACA,WAAA,KAAA;EACA;;;;EAKA,kBAAA,gBAAA;AACA,SAAA,kBAAA;AACA,WAAA;EACA;;;;EAKA,QAAA,MAAA;AACA,SAAA,QAAA;MACA,GAAA,KAAA;MACA,GAAA;IACA;AACA,SAAA,sBAAA;AACA,WAAA;EACA;;;;EAKA,OAAA,KAAA,OAAA;AACA,SAAA,QAAA,EAAA,GAAA,KAAA,OAAA,CAAA,GAAA,GAAA,MAAA;AACA,SAAA,sBAAA;AACA,WAAA;EACA;;;;EAKA,UAAA,QAAA;AACA,SAAA,SAAA;MACA,GAAA,KAAA;MACA,GAAA;IACA;AACA,SAAA,sBAAA;AACA,WAAA;EACA;;;;EAKA,SAAA,KAAA,OAAA;AACA,SAAA,SAAA,EAAA,GAAA,KAAA,QAAA,CAAA,GAAA,GAAA,MAAA;AACA,SAAA,sBAAA;AACA,WAAA;EACA;;;;EAKA,eAAA,aAAA;AACA,SAAA,eAAA;AACA,SAAA,sBAAA;AACA,WAAA;EACA;;;;EAKA,SAEA,OACA;AACA,SAAA,SAAA;AACA,SAAA,sBAAA;AACA,WAAA;EACA;;;;EAKA,mBAAA,MAAA;AACA,SAAA,mBAAA;AACA,SAAA,sBAAA;AACA,WAAA;EACA;;;;EAKA,WAAA,KAAA,SAAA;AACA,QAAA,YAAA,MAAA;AAEA,aAAA,KAAA,UAAA,GAAA;IACA,OAAA;AACA,WAAA,UAAA,GAAA,IAAA;IACA;AAEA,SAAA,sBAAA;AACA,WAAA;EACA;;;;EAKA,QAAA,MAAA;AACA,SAAA,QAAA;AACA,SAAA,sBAAA;AACA,WAAA;EACA;;;;EAKA,UAAA;AACA,WAAA,KAAA;EACA;;;;EAKA,iBAAA;AAGA,UAAA,OAAA,KAAA,QAAA;AACA,WAAA,QAAA,KAAA;EACA;;;;EAKA,WAAA,SAAA;AACA,QAAA,CAAA,SAAA;AACA,aAAA,KAAA;IACA,OAAA;AACA,WAAA,WAAA;IACA;AACA,SAAA,sBAAA;AACA,WAAA;EACA;;;;EAKA,aAAA;AACA,WAAA,KAAA;EACA;;;;EAKA,OAAA,gBAAA;AACA,QAAA,CAAA,gBAAA;AACA,aAAA;IACA;AAEA,QAAA,OAAA,mBAAA,YAAA;AACA,YAAA,eAAA,eAAA,IAAA;AACA,aAAA,wBAAA,QAAA,eAAA;IACA;AAEA,QAAA,0BAAA,OAAA;AACA,WAAA,QAAA,EAAA,GAAA,KAAA,OAAA,GAAA,eAAA,MAAA;AACA,WAAA,SAAA,EAAA,GAAA,KAAA,QAAA,GAAA,eAAA,OAAA;AACA,WAAA,YAAA,EAAA,GAAA,KAAA,WAAA,GAAA,eAAA,UAAA;AACA,UAAA,eAAA,SAAA,OAAA,KAAA,eAAA,KAAA,EAAA,QAAA;AACA,aAAA,QAAA,eAAA;MACA;AACA,UAAA,eAAA,QAAA;AACA,aAAA,SAAA,eAAA;MACA;AACA,UAAA,eAAA,cAAA;AACA,aAAA,eAAA,eAAA;MACA;AACA,UAAA,eAAA,iBAAA;AACA,aAAA,kBAAA,eAAA;MACA;IACA,WAAA,cAAA,cAAA,GAAA;AAEA,uBAAA;AACA,WAAA,QAAA,EAAA,GAAA,KAAA,OAAA,GAAA,eAAA,KAAA;AACA,WAAA,SAAA,EAAA,GAAA,KAAA,QAAA,GAAA,eAAA,MAAA;AACA,WAAA,YAAA,EAAA,GAAA,KAAA,WAAA,GAAA,eAAA,SAAA;AACA,UAAA,eAAA,MAAA;AACA,aAAA,QAAA,eAAA;MACA;AACA,UAAA,eAAA,OAAA;AACA,aAAA,SAAA,eAAA;MACA;AACA,UAAA,eAAA,aAAA;AACA,aAAA,eAAA,eAAA;MACA;AACA,UAAA,eAAA,gBAAA;AACA,aAAA,kBAAA,eAAA;MACA;IACA;AAEA,WAAA;EACA;;;;EAKA,QAAA;AACA,SAAA,eAAA,CAAA;AACA,SAAA,QAAA,CAAA;AACA,SAAA,SAAA,CAAA;AACA,SAAA,QAAA,CAAA;AACA,SAAA,YAAA,CAAA;AACA,SAAA,SAAA;AACA,SAAA,mBAAA;AACA,SAAA,eAAA;AACA,SAAA,kBAAA;AACA,SAAA,QAAA;AACA,SAAA,WAAA;AACA,SAAA,sBAAA;AACA,SAAA,eAAA,CAAA;AACA,WAAA;EACA;;;;EAKA,cAAA,YAAA,gBAAA;AACA,UAAA,YAAA,OAAA,mBAAA,WAAA,iBAAA;AAGA,QAAA,aAAA,GAAA;AACA,aAAA;IACA;AAEA,UAAA,mBAAA;MACA,WAAA,uBAAA;MACA,GAAA;IACA;AACA,SAAA,eAAA,CAAA,GAAA,KAAA,cAAA,gBAAA,EAAA,MAAA,CAAA,SAAA;AACA,SAAA,sBAAA;AAEA,WAAA;EACA;;;;EAKA,mBAAA;AACA,SAAA,eAAA,CAAA;AACA,SAAA,sBAAA;AACA,WAAA;EACA;;;;EAKA,cAAA,YAAA;AACA,SAAA,aAAA,KAAA,UAAA;AACA,WAAA;EACA;;;;EAKA,iBAAA;AACA,WAAA,KAAA;EACA;;;;EAKA,mBAAA;AACA,SAAA,eAAA,CAAA;AACA,WAAA;EACA;;;;;;;;EASA,aAAA,OAAA,OAAA,CAAA,GAAA;AACA,QAAA,KAAA,UAAA,OAAA,KAAA,KAAA,MAAA,EAAA,QAAA;AACA,YAAA,QAAA,EAAA,GAAA,KAAA,QAAA,GAAA,MAAA,MAAA;IACA;AACA,QAAA,KAAA,SAAA,OAAA,KAAA,KAAA,KAAA,EAAA,QAAA;AACA,YAAA,OAAA,EAAA,GAAA,KAAA,OAAA,GAAA,MAAA,KAAA;IACA;AACA,QAAA,KAAA,SAAA,OAAA,KAAA,KAAA,KAAA,EAAA,QAAA;AACA,YAAA,OAAA,EAAA,GAAA,KAAA,OAAA,GAAA,MAAA,KAAA;IACA;AACA,QAAA,KAAA,aAAA,OAAA,KAAA,KAAA,SAAA,EAAA,QAAA;AACA,YAAA,WAAA,EAAA,GAAA,KAAA,WAAA,GAAA,MAAA,SAAA;IACA;AACA,QAAA,KAAA,QAAA;AACA,YAAA,QAAA,KAAA;IACA;AACA,QAAA,KAAA,kBAAA;AACA,YAAA,cAAA,KAAA;IACA;AAKA,QAAA,KAAA,OAAA;AACA,YAAA,WAAA,EAAA,OAAA,KAAA,MAAA,gBAAA,GAAA,GAAA,MAAA,SAAA;AACA,YAAA,kBAAA,KAAA,MAAA,eAAA,KAAA,MAAA,YAAA;AACA,UAAA,iBAAA;AACA,cAAA,OAAA,EAAA,aAAA,iBAAA,GAAA,MAAA,KAAA;MACA;IACA;AAEA,SAAA,kBAAA,KAAA;AAEA,UAAA,cAAA,CAAA,GAAA,MAAA,eAAA,CAAA,GAAA,GAAA,KAAA,YAAA;AACA,UAAA,cAAA,MAAA,YAAA,SAAA,IAAA,MAAA,cAAA;AAEA,UAAA,wBAAA,EAAA,GAAA,MAAA,uBAAA,GAAA,KAAA,uBAAA;AAEA,WAAA,KAAA,uBAAA,CAAA,GAAA,yBAAA,GAAA,GAAA,KAAA,gBAAA,GAAA,OAAA,IAAA;EACA;;;;EAKA,yBAAA,SAAA;AACA,SAAA,yBAAA,EAAA,GAAA,KAAA,wBAAA,GAAA,QAAA;AAEA,WAAA;EACA;;;;EAKA,uBACA,YACA,OACA,MACA,QAAA,GACA;AACA,WAAA,IAAA,YAAA,CAAAC,UAAA,WAAA;AACA,YAAA,YAAA,WAAA,KAAA;AACA,UAAA,UAAA,QAAA,OAAA,cAAA,YAAA;AACA,QAAAA,SAAA,KAAA;MACA,OAAA;AACA,cAAA,SAAA,UAAA,EAAA,GAAA,MAAA,GAAA,IAAA;AAEA,SAAA,OAAA,qBAAA,eAAA,qBACA,UAAA,MACA,WAAA,QACA,OAAA,IAAA,oBAAA,UAAA,mBAAA;AAEA,YAAA,WAAA,MAAA,GAAA;AACA,eAAA,OACA,KAAA,WAAA,KAAA,uBAAA,YAAA,OAAA,MAAA,QAAA,CAAA,EAAA,KAAAA,QAAA,CAAA,EACA,KAAA,MAAA,MAAA;QACA,OAAA;AACA,eAAA,KAAA,uBAAA,YAAA,QAAA,MAAA,QAAA,CAAA,EACA,KAAAA,QAAA,EACA,KAAA,MAAA,MAAA;QACA;MACA;IACA,CAAA;EACA;;;;EAKA,wBAAA;AAIA,QAAA,CAAA,KAAA,qBAAA;AACA,WAAA,sBAAA;AACA,WAAA,gBAAA,QAAA,cAAA;AACA,iBAAA,IAAA;MACA,CAAA;AACA,WAAA,sBAAA;IACA;EACA;;;;;EAMA,kBAAA,OAAA;AAEA,UAAA,cAAA,MAAA,cAAA,SAAA,MAAA,WAAA,IAAA,CAAA;AAGA,QAAA,KAAA,cAAA;AACA,YAAA,cAAA,MAAA,YAAA,OAAA,KAAA,YAAA;IACA;AAGA,QAAA,MAAA,eAAA,CAAA,MAAA,YAAA,QAAA;AACA,aAAA,MAAA;IACA;EACA;AACA;AAKA,SAAA,2BAAA;AACA,SAAA,mBAAA,yBAAA,MAAA,CAAA,CAAA;AACA;AAMA,SAAA,wBAAA,UAAA;AACA,2BAAA,EAAA,KAAA,QAAA;AACA;;;ACviBA,IAAA,eAAA;AAUA,IAAA,cAAA;AAMA,IAAA,sBAAA;AAqCA,IAAA,MAAA,MAAA;;EAEA,SAAA;AAAA,SAAA,SAAA,CAAA,CAAA,CAAA;EAAA;;;;;;;;;;EAaA,YAAA,QAAA,QAAA,IAAA,MAAA,GAAA,WAAA,aAAA;AAAA;AAAA,SAAA,WAAA;AAAA,QAAA,UAAA,OAAA,KAAA,IAAA;AACA,SAAA,YAAA,EAAA,QAAA;AACA,QAAA,QAAA;AACA,WAAA,WAAA,MAAA;IACA;EACA;;;;EAKA,YAAA,SAAA;AACA,WAAA,KAAA,WAAA;EACA;;;;EAKA,WAAA,QAAA;AACA,UAAA,MAAA,KAAA,YAAA;AACA,QAAA,SAAA;AACA,QAAA,UAAA,OAAA,mBAAA;AACA,aAAA,kBAAA;IACA;EACA;;;;EAKA,YAAA;AAEA,UAAA,QAAA,MAAA,MAAA,KAAA,SAAA,CAAA;AACA,SAAA,SAAA,EAAA,KAAA;MACA,QAAA,KAAA,UAAA;MACA;IACA,CAAA;AACA,WAAA;EACA;;;;EAKA,WAAA;AACA,QAAA,KAAA,SAAA,EAAA,UAAA;AAAA,aAAA;AACA,WAAA,CAAA,CAAA,KAAA,SAAA,EAAA,IAAA;EACA;;;;EAKA,UAAA,UAAA;AACA,UAAA,QAAA,KAAA,UAAA;AACA,QAAA;AACA,eAAA,KAAA;IACA,UAAA;AACA,WAAA,SAAA;IACA;EACA;;;;EAKA,YAAA;AACA,WAAA,KAAA,YAAA,EAAA;EACA;;EAGA,WAAA;AACA,WAAA,KAAA,YAAA,EAAA;EACA;;EAGA,WAAA;AACA,WAAA,KAAA;EACA;;EAGA,cAAA;AACA,WAAA,KAAA,OAAA,KAAA,OAAA,SAAA,CAAA;EACA;;;;;EAMA,iBAAA,WAAA,MAAA;AACA,UAAA,qBAAA,IAAA,MAAA,2BAAA;AACA,SAAA,eACA,KAAA,YAAA,CAAA,QAAA,UAAA;AACA,aAAA,OAAA;QACA;QACA;UACA,mBAAA;UACA;UACA,GAAA;QACA;QACA;MACA;IACA,CAAA,KAAA;AACA,WAAA,KAAA;EACA;;;;EAKA,eACA,SAEA,OACA,MACA;AACA,UAAA,qBAAA,IAAA,MAAA,OAAA;AACA,SAAA,eACA,KAAA,YAAA,CAAA,QAAA,UAAA;AACA,aAAA,OAAA;QACA;QACA;QACA;UACA,mBAAA;UACA;UACA,GAAA;QACA;QACA;MACA;IACA,CAAA,KAAA;AACA,WAAA,KAAA;EACA;;;;EAKA,aAAA,OAAA,MAAA;AACA,UAAA,WACA,KAAA,YAAA,CAAA,QAAA,UAAA;AACA,aAAA,OAAA,aAAA,OAAA,EAAA,GAAA,KAAA,GAAA,KAAA;IACA,CAAA,KAAA;AAEA,QAAA,MAAA,SAAA,eAAA;AACA,WAAA,eAAA;IACA;AAEA,WAAA;EACA;;;;EAKA,cAAA;AACA,WAAA,KAAA;EACA;;;;EAKA,cAAA,YAAA,MAAA;AACA,UAAA,EAAA,OAAA,OAAA,IAAA,KAAA,YAAA;AAEA,QAAA,CAAA,SAAA,CAAA;AAAA;AAGA,UAAA,EAAA,mBAAA,MAAA,iBAAA,oBAAA,IACA,OAAA,cAAA,OAAA,WAAA,KAAA,CAAA;AAEA,QAAA,kBAAA;AAAA;AAEA,UAAA,YAAA,uBAAA;AACA,UAAA,mBAAA,EAAA,WAAA,GAAA,WAAA;AACA,UAAA,kBAAA,mBACA,eAAA,MAAA,iBAAA,kBAAA,IAAA,CAAA,IACA;AAEA,QAAA,oBAAA;AAAA;AAEA,UAAA,cAAA,iBAAA,cAAA;EACA;;;;EAKA,QAAA,MAAA;AACA,UAAA,QAAA,KAAA,SAAA;AACA,QAAA;AAAA,YAAA,QAAA,IAAA;EACA;;;;EAKA,QAAA,MAAA;AACA,UAAA,QAAA,KAAA,SAAA;AACA,QAAA;AAAA,YAAA,QAAA,IAAA;EACA;;;;EAKA,UAAA,QAAA;AACA,UAAA,QAAA,KAAA,SAAA;AACA,QAAA;AAAA,YAAA,UAAA,MAAA;EACA;;;;EAKA,OAAA,KAAA,OAAA;AACA,UAAA,QAAA,KAAA,SAAA;AACA,QAAA;AAAA,YAAA,OAAA,KAAA,KAAA;EACA;;;;EAKA,SAAA,KAAA,OAAA;AACA,UAAA,QAAA,KAAA,SAAA;AACA,QAAA;AAAA,YAAA,SAAA,KAAA,KAAA;EACA;;;;;EAMA,WAAA,MAAA,SAAA;AACA,UAAA,QAAA,KAAA,SAAA;AACA,QAAA;AAAA,YAAA,WAAA,MAAA,OAAA;EACA;;;;EAKA,eAAA,UAAA;AACA,UAAA,EAAA,OAAA,OAAA,IAAA,KAAA,YAAA;AACA,QAAA,SAAA,QAAA;AACA,eAAA,KAAA;IACA;EACA;;;;EAKA,IAAA,UAAA;AACA,UAAA,SAAA,SAAA,IAAA;AACA,QAAA;AACA,eAAA,IAAA;IACA,UAAA;AACA,eAAA,MAAA;IACA;EACA;;;;EAKA,eAAA,aAAA;AACA,UAAA,SAAA,KAAA,UAAA;AACA,QAAA,CAAA;AAAA,aAAA;AACA,QAAA;AACA,aAAA,OAAA,eAAA,WAAA;IACA,SAAA,KAAA;AACA,OAAA,OAAA,qBAAA,eAAA,qBAAA,OAAA,KAAA,+BAAA,YAAA,yBAAA;AACA,aAAA;IACA;EACA;;;;EAKA,iBAAA,SAAA,uBAAA;AACA,WAAA,KAAA,qBAAA,oBAAA,SAAA,qBAAA;EACA;;;;EAKA,eAAA;AACA,WAAA,KAAA,qBAAA,cAAA;EACA;;;;EAKA,eAAA,aAAA,OAAA;AAEA,QAAA,YAAA;AACA,aAAA,KAAA,WAAA;IACA;AAGA,SAAA,mBAAA;EACA;;;;EAKA,aAAA;AACA,UAAA,QAAA,KAAA,YAAA;AACA,UAAA,QAAA,SAAA,MAAA;AACA,UAAA,UAAA,SAAA,MAAA,WAAA;AACA,QAAA,SAAA;AACA,mBAAA,OAAA;IACA;AACA,SAAA,mBAAA;AAGA,QAAA,OAAA;AACA,YAAA,WAAA;IACA;EACA;;;;EAKA,aAAA,SAAA;AACA,UAAA,EAAA,OAAA,OAAA,IAAA,KAAA,YAAA;AACA,UAAA,EAAA,SAAA,YAAA,IAAA,UAAA,OAAA,WAAA,KAAA,CAAA;AAGA,UAAA,EAAA,UAAA,IAAA,WAAA,aAAA,CAAA;AAEA,UAAA,UAAA,YAAA;MACA;MACA;MACA,GAAA,SAAA,EAAA,MAAA,MAAA,QAAA,EAAA;MACA,GAAA,aAAA,EAAA,UAAA;MACA,GAAA;IACA,CAAA;AAEA,QAAA,OAAA;AAEA,YAAA,iBAAA,MAAA,cAAA,MAAA,WAAA;AACA,UAAA,kBAAA,eAAA,WAAA,MAAA;AACA,sBAAA,gBAAA,EAAA,QAAA,SAAA,CAAA;MACA;AACA,WAAA,WAAA;AAGA,YAAA,WAAA,OAAA;IACA;AAEA,WAAA;EACA;;;;;EAMA,uBAAA;AACA,UAAA,SAAA,KAAA,UAAA;AACA,UAAA,UAAA,UAAA,OAAA,WAAA;AACA,WAAA,QAAA,WAAA,QAAA,cAAA;EACA;;;;EAKA,qBAAA;AACA,UAAA,EAAA,OAAA,OAAA,IAAA,KAAA,YAAA;AACA,QAAA,CAAA;AAAA;AAEA,UAAA,UAAA,MAAA,WAAA;AACA,QAAA,SAAA;AACA,UAAA,UAAA,OAAA,gBAAA;AACA,eAAA,eAAA,OAAA;MACA;IACA;EACA;;;;;;;EAQA,YAAA,UAAA;AACA,UAAA,EAAA,OAAA,OAAA,IAAA,KAAA,YAAA;AACA,WAAA,UAAA,SAAA,QAAA,KAAA;EACA;;;;;;EAOA,qBAAA,WAAA,MAAA;AACA,UAAA,UAAA,eAAA;AACA,UAAA,SAAA,QAAA;AACA,QAAA,UAAA,OAAA,cAAA,OAAA,OAAA,WAAA,MAAA,MAAA,YAAA;AACA,aAAA,OAAA,WAAA,MAAA,EAAA,MAAA,MAAA,IAAA;IACA;AACA,KAAA,OAAA,qBAAA,eAAA,qBAAA,OAAA,KAAA,oBAAA,0CAAA;EACA;AACA;AASA,SAAA,iBAAA;AACA,aAAA,aAAA,WAAA,cAAA;IACA,YAAA,CAAA;IACA,KAAA;EACA;AACA,SAAA;AACA;AAOA,SAAA,SAAA,KAAA;AACA,QAAA,WAAA,eAAA;AACA,QAAA,SAAA,kBAAA,QAAA;AACA,kBAAA,UAAA,GAAA;AACA,SAAA;AACA;AASA,SAAA,gBAAA;AAEA,QAAA,WAAA,eAAA;AAGA,MAAA,CAAA,gBAAA,QAAA,KAAA,kBAAA,QAAA,EAAA,YAAA,WAAA,GAAA;AACA,oBAAA,UAAA,IAAA,IAAA,CAAA;EACA;AAGA,MAAA,UAAA,GAAA;AACA,WAAA,uBAAA,QAAA;EACA;AAEA,SAAA,kBAAA,QAAA;AACA;AAMA,SAAA,uBAAA,UAAA;AACA,MAAA;AACA,UAAA,SAAA,eAAA,EAAA;AACA,UAAA,eAAA,UAAA,OAAA,cAAA,OAAA,WAAA,UAAA,OAAA,WAAA,OAAA;AAGA,QAAA,CAAA,cAAA;AACA,aAAA,kBAAA,QAAA;IACA;AAGA,QAAA,CAAA,gBAAA,YAAA,KAAA,kBAAA,YAAA,EAAA,YAAA,WAAA,GAAA;AACA,YAAA,sBAAA,kBAAA,QAAA,EAAA,YAAA;AACA,sBAAA,cAAA,IAAA,IAAA,oBAAA,QAAA,MAAA,MAAA,oBAAA,KAAA,CAAA,CAAA;IACA;AAGA,WAAA,kBAAA,YAAA;EACA,SAAA,KAAA;AAEA,WAAA,kBAAA,QAAA;EACA;AACA;AAMA,SAAA,gBAAA,SAAA;AACA,SAAA,CAAA,EAAA,WAAA,QAAA,cAAA,QAAA,WAAA;AACA;AAQA,SAAA,kBAAA,SAAA;AACA,SAAA,mBAAA,OAAA,MAAA,IAAA,IAAA,GAAA,OAAA;AACA;AAQA,SAAA,gBAAA,SAAA,KAAA;AACA,MAAA,CAAA;AAAA,WAAA;AACA,QAAA,aAAA,QAAA,aAAA,QAAA,cAAA,CAAA;AACA,aAAA,MAAA;AACA,SAAA;AACA;;;ACplBA,IAAA,qBAAA;AAGA,SAAA,mBAAA,KAAA;AACA,QAAA,WAAA,IAAA,WAAA,GAAA,IAAA,cAAA;AACA,QAAA,OAAA,IAAA,OAAA,IAAA,IAAA,SAAA;AACA,SAAA,GAAA,aAAA,IAAA,OAAA,OAAA,IAAA,OAAA,IAAA,IAAA,SAAA;AACA;AAGA,SAAA,mBAAA,KAAA;AACA,SAAA,GAAA,mBAAA,GAAA,IAAA,IAAA;AACA;AAGA,SAAA,aAAA,KAAA,SAAA;AACA,SAAA,UAAA;;;IAGA,YAAA,IAAA;IACA,gBAAA;IACA,GAAA,WAAA,EAAA,eAAA,GAAA,QAAA,QAAA,QAAA,UAAA;EACA,CAAA;AACA;AAOA,SAAA,sCACA,KAGA,kBAAA,CAAA,GACA;AAKA,QAAA,SAAA,OAAA,oBAAA,WAAA,kBAAA,gBAAA;AACA,QAAA,UACA,OAAA,oBAAA,YAAA,CAAA,gBAAA,YAAA,SAAA,gBAAA,UAAA;AAEA,SAAA,SAAA,SAAA,GAAA,mBAAA,GAAA,KAAA,aAAA,KAAA,OAAA;AACA;;;AChCA,SAAA,gCAAA,UAAA;AACA,MAAA,CAAA,YAAA,CAAA,SAAA,KAAA;AACA;EACA;AACA,QAAA,EAAA,MAAA,QAAA,IAAA,SAAA;AACA,SAAA,EAAA,MAAA,QAAA;AACA;AAMA,SAAA,wBAAA,OAAA,SAAA;AACA,MAAA,CAAA,SAAA;AACA,WAAA;EACA;AACA,QAAA,MAAA,MAAA,OAAA,CAAA;AACA,QAAA,IAAA,OAAA,MAAA,IAAA,QAAA,QAAA;AACA,QAAA,IAAA,UAAA,MAAA,IAAA,WAAA,QAAA;AACA,QAAA,IAAA,eAAA,CAAA,GAAA,MAAA,IAAA,gBAAA,CAAA,GAAA,GAAA,QAAA,gBAAA,CAAA,CAAA;AACA,QAAA,IAAA,WAAA,CAAA,GAAA,MAAA,IAAA,YAAA,CAAA,GAAA,GAAA,QAAA,YAAA,CAAA,CAAA;AACA,SAAA;AACA;AAGA,SAAA,sBACA,SACA,KACA,UACA,QACA;AACA,QAAA,UAAA,gCAAA,QAAA;AACA,QAAA,kBAAA;IACA,UAAA,oBAAA,KAAA,GAAA,YAAA;IACA,GAAA,WAAA,EAAA,KAAA,QAAA;IACA,GAAA,CAAA,CAAA,UAAA,EAAA,KAAA,YAAA,GAAA,EAAA;EACA;AAEA,QAAA,eACA,gBAAA,UAAA,CAAA,EAAA,MAAA,WAAA,GAAA,OAAA,IAAA,CAAA,EAAA,MAAA,UAAA,GAAA,OAAA;AAEA,SAAA,eAAA,iBAAA,CAAA,YAAA,CAAA;AACA;AAKA,SAAA,oBACA,OACA,KACA,UACA,QACA;AACA,QAAA,UAAA,gCAAA,QAAA;AACA,QAAA,YAAA,MAAA,QAAA;AAEA,0BAAA,OAAA,YAAA,SAAA,GAAA;AAEA,QAAA,kBAAA,2BAAA,OAAA,SAAA,QAAA,GAAA;AAMA,SAAA,MAAA;AAEA,QAAA,YAAA,CAAA,EAAA,MAAA,UAAA,GAAA,KAAA;AACA,SAAA,eAAA,iBAAA,CAAA,SAAA,CAAA;AACA;AAEA,SAAA,2BACA,OACA,SACA,QACA,KACA;AACA,QAAA,yBAAA,MAAA,yBAAA,MAAA,sBAAA;AAEA,SAAA;IACA,UAAA,MAAA;IACA,UAAA,oBAAA,KAAA,GAAA,YAAA;IACA,GAAA,WAAA,EAAA,KAAA,QAAA;IACA,GAAA,CAAA,CAAA,UAAA,EAAA,KAAA,YAAA,GAAA,EAAA;IACA,GAAA,MAAA,SAAA,iBACA,0BAAA;MACA,OAAA,kBAAA,EAAA,GAAA,uBAAA,CAAA;IACA;EACA;AACA;;;AC5FA,IAAA,wBAAA,CAAA;AAaA,SAAA,iBAAA,cAAA;AACA,QAAA,qBAAA,CAAA;AAEA,eAAA,QAAA,qBAAA;AACA,UAAA,EAAA,KAAA,IAAA;AAEA,UAAA,mBAAA,mBAAA,IAAA;AAIA,QAAA,oBAAA,CAAA,iBAAA,qBAAA,gBAAA,mBAAA;AACA;IACA;AAEA,uBAAA,IAAA,IAAA;EACA,CAAA;AAEA,SAAA,OAAA,OAAA,kBAAA;AACA;AAGA,SAAA,uBAAA,SAAA;AACA,QAAA,sBAAA,QAAA,uBAAA,CAAA;AACA,QAAA,mBAAA,QAAA;AAGA,sBAAA,QAAA,iBAAA;AACA,gBAAA,oBAAA;EACA,CAAA;AAEA,MAAA;AAEA,MAAA,MAAA,QAAA,gBAAA,GAAA;AACA,mBAAA,CAAA,GAAA,qBAAA,GAAA,gBAAA;EACA,WAAA,OAAA,qBAAA,YAAA;AACA,mBAAA,SAAA,iBAAA,mBAAA,CAAA;EACA,OAAA;AACA,mBAAA;EACA;AAEA,QAAA,oBAAA,iBAAA,YAAA;AAMA,QAAA,aAAA,kBAAA,UAAA,iBAAA,YAAA,SAAA,OAAA;AACA,MAAA,eAAA,IAAA;AACA,UAAA,CAAA,aAAA,IAAA,kBAAA,OAAA,YAAA,CAAA;AACA,sBAAA,KAAA,aAAA;EACA;AAEA,SAAA;AACA;AAQA,SAAA,kBAAA,cAAA;AACA,QAAA,mBAAA,CAAA;AAEA,eAAA,QAAA,iBAAA;AACA,qBAAA,YAAA,IAAA,IAAA;AAEA,QAAA,sBAAA,QAAA,YAAA,IAAA,MAAA,IAAA;AACA,kBAAA,UAAA,yBAAA,aAAA;AACA,4BAAA,KAAA,YAAA,IAAA;AACA,OAAA,OAAA,qBAAA,eAAA,qBAAA,OAAA,IAAA,0BAAA,YAAA,MAAA;IACA;EACA,CAAA;AAEA,SAAA;AACA;;;ACxDA,IAAA,qBAAA;AAiCA,IAAA,aAAA,MAAA;;;;EAUA,SAAA;AAAA,SAAA,gBAAA,CAAA;EAAA;;EAGA,UAAA;AAAA,SAAA,2BAAA;EAAA;;EAGA,UAAA;AAAA,SAAA,iBAAA;EAAA;;EAGA,UAAA;AAAA,SAAA,YAAA,CAAA;EAAA;;;;;;EAOA,YAAA,SAAA;AAAA;AAAA,eAAA,UAAA,OAAA,KAAA,IAAA;AAAA,eAAA,UAAA,QAAA,KAAA,IAAA;AAAA,eAAA,UAAA,QAAA,KAAA,IAAA;AAAA,eAAA,UAAA,QAAA,KAAA,IAAA;AACA,SAAA,WAAA;AACA,QAAA,QAAA,KAAA;AACA,WAAA,OAAA,QAAA,QAAA,GAAA;AACA,YAAA,MAAA,sCAAA,KAAA,MAAA,OAAA;AACA,WAAA,aAAA,QAAA,UAAA;QACA,oBAAA,KAAA,mBAAA,KAAA,IAAA;QACA,GAAA,QAAA;QACA;MACA,CAAA;IACA,OAAA;AACA,OAAA,OAAA,qBAAA,eAAA,qBAAA,OAAA,KAAA,+CAAA;IACA;EACA;;;;;EAMA,iBAAA,WAAA,MAAA,OAAA;AAEA,QAAA,wBAAA,SAAA,GAAA;AACA,OAAA,OAAA,qBAAA,eAAA,qBAAA,OAAA,IAAA,kBAAA;AACA;IACA;AAEA,QAAA;AACA,SAAA;MACA,KAAA,mBAAA,WAAA,IAAA,EACA,KAAA,WAAA,KAAA,cAAA,OAAA,MAAA,KAAA,CAAA,EACA,KAAA,YAAA;AACA,kBAAA;MACA,CAAA;IACA;AAEA,WAAA;EACA;;;;EAKA,eACA,SAEA,OACA,MACA,OACA;AACA,QAAA;AAEA,UAAA,gBAAA,YAAA,OAAA,IACA,KAAA,iBAAA,OAAA,OAAA,GAAA,OAAA,IAAA,IACA,KAAA,mBAAA,SAAA,IAAA;AAEA,SAAA;MACA,cACA,KAAA,WAAA,KAAA,cAAA,OAAA,MAAA,KAAA,CAAA,EACA,KAAA,YAAA;AACA,kBAAA;MACA,CAAA;IACA;AAEA,WAAA;EACA;;;;EAKA,aAAA,OAAA,MAAA,OAAA;AAEA,QAAA,QAAA,KAAA,qBAAA,wBAAA,KAAA,iBAAA,GAAA;AACA,OAAA,OAAA,qBAAA,eAAA,qBAAA,OAAA,IAAA,kBAAA;AACA;IACA;AAEA,QAAA;AAEA,SAAA;MACA,KAAA,cAAA,OAAA,MAAA,KAAA,EAAA,KAAA,YAAA;AACA,kBAAA;MACA,CAAA;IACA;AAEA,WAAA;EACA;;;;EAKA,eAAA,SAAA;AACA,QAAA,CAAA,KAAA,WAAA,GAAA;AACA,OAAA,OAAA,qBAAA,eAAA,qBAAA,OAAA,KAAA,4CAAA;AACA;IACA;AAEA,QAAA,EAAA,OAAA,QAAA,YAAA,WAAA;AACA,OAAA,OAAA,qBAAA,eAAA,qBAAA,OAAA,KAAA,4DAAA;IACA,OAAA;AACA,WAAA,YAAA,OAAA;AAEA,oBAAA,SAAA,EAAA,MAAA,MAAA,CAAA;IACA;EACA;;;;EAKA,SAAA;AACA,WAAA,KAAA;EACA;;;;EAKA,aAAA;AACA,WAAA,KAAA;EACA;;;;EAKA,eAAA;AACA,WAAA,KAAA;EACA;;;;EAKA,MAAA,SAAA;AACA,UAAA,YAAA,KAAA;AACA,QAAA,WAAA;AACA,aAAA,KAAA,wBAAA,OAAA,EAAA,KAAA,oBAAA;AACA,eAAA,UAAA,MAAA,OAAA,EAAA,KAAA,sBAAA,kBAAA,gBAAA;MACA,CAAA;IACA,OAAA;AACA,aAAA,oBAAA,IAAA;IACA;EACA;;;;EAKA,MAAA,SAAA;AACA,WAAA,KAAA,MAAA,OAAA,EAAA,KAAA,YAAA;AACA,WAAA,WAAA,EAAA,UAAA;AACA,aAAA;IACA,CAAA;EACA;;;;EAKA,oBAAA;AACA,QAAA,KAAA,WAAA,KAAA,CAAA,KAAA,0BAAA;AACA,WAAA,gBAAA,kBAAA,KAAA,SAAA,YAAA;AACA,WAAA,2BAAA;IACA;EACA;;;;;;EAOA,mBAAA,eAAA;AACA,WAAA,KAAA,cAAA,aAAA;EACA;;;;EAKA,eAAA,aAAA;AACA,QAAA;AACA,aAAA,KAAA,cAAA,YAAA,EAAA,KAAA;IACA,SAAA,KAAA;AACA,OAAA,OAAA,qBAAA,eAAA,qBAAA,OAAA,KAAA,+BAAA,YAAA,4BAAA;AACA,aAAA;IACA;EACA;;;;EAKA,UAAA,OAAA,OAAA,CAAA,GAAA;AACA,QAAA,KAAA,MAAA;AACA,UAAA,MAAA,oBAAA,OAAA,KAAA,MAAA,KAAA,SAAA,WAAA,KAAA,SAAA,MAAA;AAEA,iBAAA,cAAA,KAAA,eAAA,CAAA,GAAA;AACA,cAAA;UACA;UACA;YACA;YACA,KAAA,SAAA,oBAAA,KAAA,SAAA,iBAAA;UACA;QACA;MACA;AAEA,WAAA,cAAA,GAAA;IACA;EACA;;;;EAKA,YAAA,SAAA;AACA,QAAA,KAAA,MAAA;AACA,YAAA,MAAA,sBAAA,SAAA,KAAA,MAAA,KAAA,SAAA,WAAA,KAAA,SAAA,MAAA;AACA,WAAA,cAAA,GAAA;IACA;EACA;;;;EAKA,mBAAA,QAAA,UAAA,QAAA;AAGA,QAAA,KAAA,SAAA,mBAAA;AAOA,YAAA,MAAA,GAAA,UAAA;AACA,OAAA,OAAA,qBAAA,eAAA,qBAAA,OAAA,IAAA,oBAAA,MAAA;AAGA,WAAA,UAAA,GAAA,IAAA,KAAA,UAAA,GAAA,IAAA,KAAA;IACA;EACA;;EAGA,wBAAA,SAAA,OAAA;AACA,QAAA,UAAA;AACA,QAAA,UAAA;AACA,UAAA,aAAA,MAAA,aAAA,MAAA,UAAA;AAEA,QAAA,YAAA;AACA,gBAAA;AAEA,iBAAA,MAAA,YAAA;AACA,cAAA,YAAA,GAAA;AACA,YAAA,aAAA,UAAA,YAAA,OAAA;AACA,oBAAA;AACA;QACA;MACA;IACA;AAKA,UAAA,qBAAA,QAAA,WAAA;AACA,UAAA,sBAAA,sBAAA,QAAA,WAAA,KAAA,sBAAA;AAEA,QAAA,qBAAA;AACA,oBAAA,SAAA;QACA,GAAA,WAAA,EAAA,QAAA,UAAA;QACA,QAAA,QAAA,UAAA,OAAA,WAAA,OAAA;MACA,CAAA;AACA,WAAA,eAAA,OAAA;IACA;EACA;;;;;;;;;;;EAYA,wBAAA,SAAA;AACA,WAAA,IAAA,YAAA,CAAAC,aAAA;AACA,UAAA,SAAA;AACA,YAAA,OAAA;AAEA,YAAA,WAAA,YAAA,MAAA;AACA,YAAA,KAAA,kBAAA,GAAA;AACA,wBAAA,QAAA;AACA,UAAAA,SAAA,IAAA;QACA,OAAA;AACA,oBAAA;AACA,cAAA,WAAA,UAAA,SAAA;AACA,0BAAA,QAAA;AACA,YAAAA,SAAA,KAAA;UACA;QACA;MACA,GAAA,IAAA;IACA,CAAA;EACA;;EAGA,aAAA;AACA,WAAA,KAAA,WAAA,EAAA,YAAA,SAAA,KAAA,SAAA;EACA;;;;;;;;;;;;;;;EAgBA,cAAA,OAAA,MAAA,OAAA;AACA,UAAA,EAAA,iBAAA,GAAA,sBAAA,IAAA,IAAA,KAAA,WAAA;AACA,UAAA,WAAA;MACA,GAAA;MACA,UAAA,MAAA,YAAA,KAAA,YAAA,MAAA;MACA,WAAA,MAAA,aAAA,uBAAA;IACA;AAEA,SAAA,oBAAA,QAAA;AACA,SAAA,2BAAA,QAAA;AAIA,QAAA,aAAA;AACA,QAAA,KAAA,gBAAA;AACA,mBAAA,MAAA,MAAA,UAAA,EAAA,OAAA,KAAA,cAAA;IACA;AAGA,QAAA,SAAA,oBAAA,QAAA;AASA,QAAA,cAAA,WAAA,gBAAA;AAEA,YAAA,cAAA,CAAA,GAAA,KAAA,eAAA,CAAA,GAAA,GAAA,WAAA,eAAA,CAAA;AAEA,UAAA,YAAA,QAAA;AACA,aAAA,cAAA;MACA;AAGA,eAAA,WAAA,aAAA,UAAA,IAAA;IACA;AAEA,WAAA,OAAA,KAAA,SAAA;AACA,UAAA,OAAA,mBAAA,YAAA,iBAAA,GAAA;AACA,eAAA,KAAA,gBAAA,KAAA,gBAAA,mBAAA;MACA;AACA,aAAA;IACA,CAAA;EACA;;;;;;;;;;;EAYA,gBAAA,OAAA,OAAA,YAAA;AACA,QAAA,CAAA,OAAA;AACA,aAAA;IACA;AAEA,UAAA,aAAA;MACA,GAAA;MACA,GAAA,MAAA,eAAA;QACA,aAAA,MAAA,YAAA,IAAA,QAAA;UACA,GAAA;UACA,GAAA,EAAA,QAAA;YACA,MAAA,UAAA,EAAA,MAAA,OAAA,UAAA;UACA;QACA,EAAA;MACA;MACA,GAAA,MAAA,QAAA;QACA,MAAA,UAAA,MAAA,MAAA,OAAA,UAAA;MACA;MACA,GAAA,MAAA,YAAA;QACA,UAAA,UAAA,MAAA,UAAA,OAAA,UAAA;MACA;MACA,GAAA,MAAA,SAAA;QACA,OAAA,UAAA,MAAA,OAAA,OAAA,UAAA;MACA;IACA;AASA,QAAA,MAAA,YAAA,MAAA,SAAA,SAAA,WAAA,UAAA;AACA,iBAAA,SAAA,QAAA,MAAA,SAAA;AAGA,UAAA,MAAA,SAAA,MAAA,MAAA;AACA,mBAAA,SAAA,MAAA,OAAA,UAAA,MAAA,SAAA,MAAA,MAAA,OAAA,UAAA;MACA;IACA;AAGA,QAAA,MAAA,OAAA;AACA,iBAAA,QAAA,MAAA,MAAA,IAAA,UAAA;AAEA,YAAA,KAAA,MAAA;AACA,eAAA,OAAA,UAAA,KAAA,MAAA,OAAA,UAAA;QACA;AACA,eAAA;MACA,CAAA;IACA;AAEA,WAAA;EACA;;;;;;;EAQA,oBAAA,OAAA;AACA,UAAA,UAAA,KAAA,WAAA;AACA,UAAA,EAAA,aAAA,SAAA,MAAA,iBAAA,IAAA,IAAA;AAEA,QAAA,EAAA,iBAAA,QAAA;AACA,YAAA,cAAA,iBAAA,UAAA,cAAA;IACA;AAEA,QAAA,MAAA,YAAA,UAAA,YAAA,QAAA;AACA,YAAA,UAAA;IACA;AAEA,QAAA,MAAA,SAAA,UAAA,SAAA,QAAA;AACA,YAAA,OAAA;IACA;AAEA,QAAA,MAAA,SAAA;AACA,YAAA,UAAA,SAAA,MAAA,SAAA,cAAA;IACA;AAEA,UAAA,YAAA,MAAA,aAAA,MAAA,UAAA,UAAA,MAAA,UAAA,OAAA,CAAA;AACA,QAAA,aAAA,UAAA,OAAA;AACA,gBAAA,QAAA,SAAA,UAAA,OAAA,cAAA;IACA;AAEA,UAAA,UAAA,MAAA;AACA,QAAA,WAAA,QAAA,KAAA;AACA,cAAA,MAAA,SAAA,QAAA,KAAA,cAAA;IACA;EACA;;;;;EAMA,2BAAA,OAAA;AACA,UAAA,oBAAA,OAAA,KAAA,KAAA,aAAA;AACA,QAAA,kBAAA,SAAA,GAAA;AACA,YAAA,MAAA,MAAA,OAAA,CAAA;AACA,YAAA,IAAA,eAAA,CAAA,GAAA,MAAA,IAAA,gBAAA,CAAA,GAAA,GAAA,iBAAA;IACA;EACA;;;;;;;EAQA,cAAA,OAAA,OAAA,CAAA,GAAA,OAAA;AACA,WAAA,KAAA,cAAA,OAAA,MAAA,KAAA,EAAA;MACA,gBAAA;AACA,eAAA,WAAA;MACA;MACA,YAAA;AACA,YAAA,OAAA,qBAAA,eAAA,kBAAA;AAGA,gBAAA,cAAA;AACA,cAAA,YAAA,aAAA,OAAA;AACA,mBAAA,IAAA,YAAA,OAAA;UACA,OAAA;AACA,mBAAA,KAAA,WAAA;UACA;QACA;AACA,eAAA;MACA;IACA;EACA;;;;;;;;;;;;;;EAeA,cAAA,OAAA,MAAA,OAAA;AACA,UAAA,UAAA,KAAA,WAAA;AACA,UAAA,EAAA,WAAA,IAAA;AAEA,QAAA,CAAA,KAAA,WAAA,GAAA;AACA,aAAA,oBAAA,IAAA,YAAA,4CAAA,KAAA,CAAA;IACA;AAEA,UAAA,gBAAA,MAAA,SAAA;AACA,UAAA,0BAAA,gBAAA,0BAAA;AACA,UAAA,sBAAA,QAAA,uBAAA;AAKA,QAAA,CAAA,iBAAA,OAAA,eAAA,YAAA,KAAA,OAAA,IAAA,YAAA;AACA,WAAA,mBAAA,eAAA,SAAA,KAAA;AACA,aAAA;QACA,IAAA;UACA,oFAAA;UACA;QACA;MACA;IACA;AAEA,WAAA,KAAA,cAAA,OAAA,MAAA,KAAA,EACA,KAAA,cAAA;AACA,UAAA,aAAA,MAAA;AACA,aAAA,mBAAA,mBAAA,MAAA,QAAA,SAAA,KAAA;AACA,cAAA,IAAA,YAAA,4DAAA,KAAA;MACA;AAEA,YAAA,sBAAA,KAAA,QAAA,KAAA,KAAA,eAAA;AACA,UAAA,uBAAA,CAAA,qBAAA;AACA,eAAA;MACA;AAEA,YAAA,mBAAA,oBAAA,UAAA,IAAA;AACA,aAAA,0BAAA,kBAAA,uBAAA;IACA,CAAA,EACA,KAAA,oBAAA;AACA,UAAA,mBAAA,MAAA;AACA,aAAA,mBAAA,eAAA,MAAA,QAAA,SAAA,KAAA;AACA,cAAA,IAAA,YAAA,KAAA,qEAAA,KAAA;MACA;AAEA,YAAA,UAAA,SAAA,MAAA,WAAA;AACA,UAAA,CAAA,iBAAA,SAAA;AACA,aAAA,wBAAA,SAAA,cAAA;MACA;AAKA,YAAA,kBAAA,eAAA;AACA,UAAA,iBAAA,mBAAA,eAAA,gBAAA,MAAA,aAAA;AACA,cAAA,SAAA;AACA,uBAAA,mBAAA;UACA,GAAA;UACA;UACA,SAAA;YACA,GAAA,gBAAA;YACA;cACA;;cAEA,WAAA,eAAA;cACA,cAAA,gBAAA;YACA;UACA;QACA;MACA;AAEA,WAAA,UAAA,gBAAA,IAAA;AACA,aAAA;IACA,CAAA,EACA,KAAA,MAAA,YAAA;AACA,UAAA,kBAAA,aAAA;AACA,cAAA;MACA;AAEA,WAAA,iBAAA,QAAA;QACA,MAAA;UACA,YAAA;QACA;QACA,mBAAA;MACA,CAAA;AACA,YAAA,IAAA;QACA;UAAA;MACA;IACA,CAAA;EACA;;;;EAKA,SAAA,SAAA;AACA,SAAA;AACA,SAAA,QAAA;MACA,WAAA;AACA,aAAA;AACA,eAAA;MACA;MACA,YAAA;AACA,aAAA;AACA,eAAA;MACA;IACA;EACA;;;;EAKA,cAAA,UAAA;AACA,QAAA,KAAA,cAAA,KAAA,MAAA;AACA,WAAA,WAAA,KAAA,QAAA,EAAA,KAAA,MAAA,YAAA;AACA,SAAA,OAAA,qBAAA,eAAA,qBAAA,OAAA,MAAA,8BAAA,MAAA;MACA,CAAA;IACA,OAAA;AACA,OAAA,OAAA,qBAAA,eAAA,qBAAA,OAAA,MAAA,oBAAA;IACA;EACA;;;;EAKA,iBAAA;AACA,UAAA,WAAA,KAAA;AACA,SAAA,YAAA,CAAA;AACA,WAAA,OAAA,KAAA,QAAA,EAAA,IAAA,SAAA;AACA,YAAA,CAAA,QAAA,QAAA,IAAA,IAAA,MAAA,GAAA;AACA,aAAA;QACA;QACA;QACA,UAAA,SAAA,GAAA;MACA;IACA,CAAA;EACA;;;;;AAiBA;AAKA,SAAA,0BACA,kBACA,yBACA;AACA,QAAA,oBAAA,KAAA;AACA,MAAA,WAAA,gBAAA,GAAA;AACA,WAAA,iBAAA;MACA,WAAA;AACA,YAAA,CAAA,cAAA,KAAA,KAAA,UAAA,MAAA;AACA,gBAAA,IAAA,YAAA,iBAAA;QACA;AACA,eAAA;MACA;MACA,OAAA;AACA,cAAA,IAAA,YAAA,KAAA,2CAAA,GAAA;MACA;IACA;EACA,WAAA,CAAA,cAAA,gBAAA,KAAA,qBAAA,MAAA;AACA,UAAA,IAAA,YAAA,iBAAA;EACA;AACA,SAAA;AACA;;;ACrwBA,IAAA,gCAAA;AAQA,SAAA,gBACA,SACA,aACA,SAAA,kBAAA,QAAA,cAAA,6BAAA,GACA;AACA,MAAA,aAAA,CAAA;AAEA,QAAA,QAAA,CAAA,YAAA,OAAA,MAAA,OAAA;AAEA,WAAA,KAAA,UAAA;AACA,UAAA,wBAAA,CAAA;AAGA,wBAAA,UAAA,CAAA,MAAA,SAAA;AACA,YAAA,2BAAA,+BAAA,IAAA;AACA,UAAA,cAAA,YAAA,wBAAA,GAAA;AACA,cAAA,QAAA,wBAAA,MAAA,IAAA;AACA,gBAAA,mBAAA,qBAAA,0BAAA,KAAA;MACA,OAAA;AACA,8BAAA,KAAA,IAAA;MACA;IACA,CAAA;AAGA,QAAA,sBAAA,WAAA,GAAA;AACA,aAAA,oBAAA;IACA;AAGA,UAAA,mBAAA,eAAA,SAAA,CAAA,GAAA,qBAAA;AAGA,UAAA,qBAAA,CAAA,WAAA;AACA,0BAAA,kBAAA,CAAA,MAAA,SAAA;AACA,cAAA,QAAA,wBAAA,MAAA,IAAA;AACA,gBAAA,mBAAA,QAAA,+BAAA,IAAA,GAAA,KAAA;MACA,CAAA;IACA;AAEA,UAAA,cAAA,MACA,YAAA,EAAA,MAAA,kBAAA,kBAAA,QAAA,WAAA,EAAA,CAAA,EAAA;MACA,cAAA;AAEA,YAAA,SAAA,eAAA,WAAA,SAAA,aAAA,OAAA,SAAA,cAAA,MAAA;AACA,WAAA,OAAA,qBAAA,eAAA,qBAAA,OAAA,KAAA,qCAAA,SAAA,2BAAA;QACA;AAEA,qBAAA,iBAAA,YAAA,QAAA;MACA;MACA,WAAA;AACA,SAAA,OAAA,qBAAA,eAAA,qBAAA,OAAA,MAAA,+BAAA,KAAA;AACA,2BAAA,eAAA;MACA;IACA;AAEA,WAAA,OAAA,IAAA,WAAA,EAAA;MACA,YAAA;MACA,WAAA;AACA,YAAA,iBAAA,aAAA;AACA,WAAA,OAAA,qBAAA,eAAA,qBAAA,OAAA,MAAA,+CAAA;AACA,6BAAA,gBAAA;AACA,iBAAA,oBAAA;QACA,OAAA;AACA,gBAAA;QACA;MACA;IACA;EACA;AAEA,SAAA;IACA;IACA;EACA;AACA;AAEA,SAAA,wBAAA,MAAA,MAAA;AACA,MAAA,SAAA,WAAA,SAAA,eAAA;AACA,WAAA;EACA;AAEA,SAAA,MAAA,QAAA,IAAA,IAAA,KAAA,CAAA,IAAA;AACA;;;AChHA,SAAS,SAAS,OAAO;AACrB,SAAO,OAAO,UAAU,YAAY,UAAU;AAClD;AACA,SAAS,YAAY,OAAO;AACxB,SAAQ,SAAS,KAAK,KAClB,aAAa,SACb,OAAO,MAAM,YAAY,aACzB,UAAU,SACV,OAAO,MAAM,SAAS;AAC9B;AACA,SAAS,kBAAkB,OAAO;AAC9B,SAAQ,SAAS,KAAK,KAAK,eAAe,SAAS,YAAY,MAAM,WAAW,CAAC;AACrF;AAIA,SAAS,mBAAmB;AAExB,MAAI,WAAW,kBAAkB,WAAW,eAAe,IAAI;AAC3D,WAAO,WAAW,eAAe;AAAA,EACrC;AACJ;AAQA,SAAS,cAAc,QAAQ,OAAO;AAClC,MAAI,WAAW,QAAW;AACtB,WAAO,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC;AAC1B,WAAO;AAAA,EACX,OACK;AACD,WAAO,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE;AAAA,EAClC;AACJ;AAKA,SAAS,iBAAiB,aAAa,OAAO;AAC1C,SAAO,YAAY,MAAM,SAAS,IAAI,CAAC;AAC3C;AAMA,SAAS,eAAe,IAAI;AACxB,QAAM,UAAU,MAAM,GAAG;AACzB,MAAI,CAAC,SAAS;AACV,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,SAAS,OAAO,QAAQ,MAAM,YAAY,UAAU;AAC5D,WAAO,QAAQ,MAAM;AAAA,EACzB;AACA,SAAO;AACX;AAIA,SAAS,mBAAmB,aAAa,OAAO;AAC5C,QAAM,YAAY;AAAA,IACd,MAAM,MAAM,QAAQ,MAAM,YAAY;AAAA,IACtC,OAAO,eAAe,KAAK;AAAA,EAC/B;AACA,QAAM,SAAS,iBAAiB,aAAa,KAAK;AAClD,MAAI,OAAO,QAAQ;AACf,cAAU,aAAa,EAAE,OAAO;AAAA,EACpC;AACA,MAAI,UAAU,SAAS,UAAa,UAAU,UAAU,IAAI;AACxD,cAAU,QAAQ;AAAA,EACtB;AACA,SAAO;AACX;AAIA,SAAS,sBAAsB,KAAK,aAAa,WAAW,MAAM;AAC9D,MAAI;AACJ,QAAM,oBAAoB,QAAQ,KAAK,QAAQ,kBAAkB,KAAK,IAAI,IACpE,KAAK,KAAK,YACV;AACN,QAAM,YAAY,qBAAqB;AAAA,IACnC,SAAS;AAAA,IACT,MAAM;AAAA,EACV;AACA,MAAI,CAAC,QAAQ,SAAS,GAAG;AACrB,QAAI,cAAc,SAAS,GAAG;AAG1B,YAAM,UAAU,2CAA2C,+BAA+B,SAAS;AACnG,YAAM,SAAS,KAAK,UAAU;AAC9B,YAAM,iBAAiB,UAAU,OAAO,WAAW,EAAE;AACrD,WAAK,eAAe,CAAC,UAAU;AAC3B,cAAM,SAAS,kBAAkB,gBAAgB,WAAW,cAAc,CAAC;AAAA,MAC/E,CAAC;AACD,WAAM,QAAQ,KAAK,sBAAuB,IAAI,MAAM,OAAO;AAC3D,SAAG,UAAU;AAAA,IACjB,OACK;AAGD,WAAM,QAAQ,KAAK,sBAAuB,IAAI,MAAM,SAAS;AAC7D,SAAG,UAAU;AAAA,IACjB;AACA,cAAU,YAAY;AAAA,EAC1B,OACK;AACD,SAAK;AAAA,EACT;AACA,QAAM,QAAQ;AAAA,IACV,WAAW;AAAA,MACP,QAAQ,CAAC,mBAAmB,aAAa,EAAE,CAAC;AAAA,IAChD;AAAA,EACJ;AACA,wBAAsB,OAAO,QAAW,MAAS;AACjD,wBAAsB,OAAO,SAAS;AACtC,SAAO;AAAA,IACH,GAAG;AAAA,IACH,UAAU,QAAQ,KAAK;AAAA,EAC3B;AACJ;AAIA,SAAS,iBAAiB,aAAa,SAAS,QAAQ,QAAQ,MAAM,kBAAkB;AACpF,QAAM,QAAQ;AAAA,IACV,UAAU,QAAQ,KAAK;AAAA,IACvB;AAAA,IACA;AAAA,EACJ;AACA,MAAI,oBAAoB,QAAQ,KAAK,oBAAoB;AACrD,UAAM,SAAS,iBAAiB,aAAa,KAAK,kBAAkB;AACpE,QAAI,OAAO,QAAQ;AACf,YAAM,YAAY;AAAA,QACd,QAAQ;AAAA,UACJ;AAAA,YACI,OAAO;AAAA,YACP,YAAY,EAAE,OAAO;AAAA,UACzB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAM,gBAAgB;AACtB,IAAM,gBAAN,MAAmB;AAAA,EAEf,OAAO,cAAa;AAAA,EACpB;AAAA,EACA,YAAY,UAAU,CAAC,GAAG;AACtB,SAAK,QAAQ,QAAQ,SAAS;AAAA,EAClC;AAAA,EACA,UAAUC,0BAAyBC,gBAAe;AAC9C,UAAM,SAASA,eAAc,EAAE,UAAU;AACzC,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,IAAAD,yBAAwB,CAAC,OAAO,SAAS;AACrC,YAAME,QAAOD,eAAc,EAAE,eAAe,aAAY;AACxD,UAAI,CAACC,OAAM;AACP,eAAO;AAAA,MACX;AACA,aAAO,QAAQ,OAAO,WAAW,EAAE,aAAaA,MAAK,OAAO,OAAO,IAAI;AAAA,IAC3E,CAAC;AAAA,EACL;AACJ;AApBA,IAAM,eAAN;AACI,cADE,cACK,MAAK;AAoBhB,SAAS,QAAQ,QAAQ,OAAO,OAAO,MAAM;AACzC,MAAI,CAAC,MAAM,aACP,CAAC,MAAM,UAAU,UACjB,CAAC,QACD,CAAC,aAAa,KAAK,mBAAmB,KAAK,GAAG;AAC9C,WAAO;AAAA,EACX;AACA,QAAM,eAAe,cAAc,QAAQ,OAAO,KAAK,iBAAiB;AACxE,QAAM,UAAU,SAAS,CAAC,GAAG,cAAc,GAAG,MAAM,UAAU,MAAM;AACpE,SAAO;AACX;AACA,SAAS,cAAc,QAAQ,OAAO,OAAO,QAAQ,CAAC,GAAG;AACrD,MAAI,CAAC,aAAa,MAAM,OAAO,KAAK,KAAK,MAAM,SAAS,KAAK,OAAO;AAChE,WAAO;AAAA,EACX;AACA,QAAM,YAAY,mBAAmB,QAAQ,MAAM,KAAK;AACxD,SAAO,cAAc,QAAQ,OAAO,MAAM,OAAO;AAAA,IAC7C;AAAA,IACA,GAAG;AAAA,EACP,CAAC;AACL;AAEA,IAAM,4BAA4B;AAAA,EAC9B,gBAAgB,CAAC,UAAU,WAAW;AAC1C;AAvMA;AAwMA,IAAM,eAAN,MAAkB;AAAA,EAId,YAAY,UAAU,CAAC,GAAG;AAF1B,gCAAO,aAAY;AACnB;AAEI,uBAAK,UAAW,EAAE,GAAG,2BAA2B,GAAG,QAAQ;AAAA,EAC/D;AAAA,EACA,UAAUF,0BAAyBC,gBAAe;AAC9C,UAAM,SAASA,eAAc,EAAE,UAAU;AACzC,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,IAAAD,yBAAwB,CAAC,UAAU;AAC/B,YAAM,EAAE,sBAAsB,IAAI;AAClC,YAAME,QAAOD,eAAc,EAAE,eAAe,YAAW;AACvD,UAAI,CAACC,SAAQ,CAAC,uBAAuB;AACjC,eAAO;AAAA,MACX;AACA,UAAI,aAAa,yBACb,sBAAsB,mBAAmB,SAAS;AAClD,cAAM,UAAU,eAAe,sBAAsB,SAAS,mBAAK,SAAQ;AAC3E,cAAM,OAAO,YAAY,MAAM,QAAQ,CAAC,GAAG,sBAAsB,SAAS,mBAAK,SAAQ;AAAA,MAC3F;AACA,UAAI,iBAAiB,uBAAuB;AACxC,YAAI,MAAM,SAAS;AACf,gBAAM,QAAQ,OAAO,sBAAsB;AAAA,QAC/C,OACK;AACD,gBAAM,UAAU;AAAA,YACZ,MAAM,sBAAsB;AAAA,UAChC;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AACJ;AApCA,IAAM,cAAN;AAGI;AAFA,cADE,aACK,MAAK;AA4ChB,SAAS,YAAY,MAAM,SAAS,SAAS;AACzC,QAAM,aAAa,QAAQ,QAAQ,IAAI,kBAAkB;AACzD,QAAM,EAAE,WAAW,IAAI;AACvB,QAAM,UAAU,EAAE,GAAG,KAAK;AAC1B,MAAI,EAAE,gBAAgB;AAAA,EAClB,cACA,eAAe,UACf,cAAc,YAAY,UAAU,GAAG;AACvC,YAAQ,aAAa;AAAA,EACzB;AACA,SAAO,OAAO,KAAK,OAAO,EAAE,SAAS,IAAI,UAAU;AACvD;AAQA,SAAS,eAAe,SAAS,SAAS;AAEtC,QAAM,eAAe,QAAQ,QAAQ,IAAI,QAAQ;AACjD,MAAI,UAAU;AACd,MAAI,cAAc;AACd,QAAI;AACA,gBAAU,YAAY,YAAY;AAAA,IACtC,SACO,GAAP;AAAA,IAEA;AAAA,EACJ;AACA,QAAM,UAAU,CAAC;AAEjB,aAAW,CAAC,GAAG,CAAC,KAAK,QAAQ,QAAQ,QAAQ,GAAG;AAC5C,QAAI,MAAM,UAAU;AAChB,cAAQ,CAAC,IAAI;AAAA,IACjB;AAAA,EACJ;AACA,QAAM,eAAe;AAAA,IACjB,QAAQ,QAAQ;AAAA,IAChB;AAAA,IACA;AAAA,EACJ;AACA,MAAI;AACA,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,iBAAa,MAAM,GAAG,IAAI,aAAa,IAAI,WAAW,IAAI;AAC1D,iBAAa,eAAe,IAAI;AAAA,EACpC,SACO,GAAP;AAEI,UAAM,KAAK,QAAQ,IAAI,QAAQ,GAAG;AAClC,QAAI,KAAK,GAAG;AAER,mBAAa,MAAM,QAAQ;AAAA,IAC/B,OACK;AACD,mBAAa,MAAM,QAAQ,IAAI,OAAO,GAAG,EAAE;AAC3C,mBAAa,eAAe,QAAQ,IAAI,OAAO,KAAK,CAAC;AAAA,IACzD;AAAA,EACJ;AAEA,QAAM,EAAE,gBAAgB,gBAAgB,oBAAoB,IAAI;AAChE,MAAI,mBAAmB,UAAa,aAAa,SAAS;AACtD,iBAAa,UAAU,uBAAuB,aAAa,SAAS,cAAc;AAClF,QAAI,OAAO,KAAK,aAAa,OAAO,EAAE,WAAW,GAAG;AAChD,aAAO,aAAa;AAAA,IACxB;AAAA,EACJ,OACK;AACD,WAAO,aAAa;AAAA,EACxB;AACA,MAAI,mBAAmB,UAAa,aAAa,SAAS;AACtD,iBAAa,UAAU,uBAAuB,aAAa,SAAS,cAAc;AAClF,QAAI,OAAO,KAAK,aAAa,OAAO,EAAE,WAAW,GAAG;AAChD,aAAO,aAAa;AAAA,IACxB;AAAA,EACJ,OACK;AACD,WAAO,aAAa;AAAA,EACxB;AACA,MAAI,wBAAwB,QAAW;AACnC,UAAM,SAAS,OAAO,YAAY,IAAI,gBAAgB,aAAa,YAAY,CAAC;AAChF,UAAM,gBAAgB,IAAI,gBAAgB;AAC1C,WAAO,KAAK,uBAAuB,QAAQ,mBAAmB,CAAC,EAAE,QAAQ,CAAC,eAAe;AACrF,oBAAc,IAAI,YAAY,OAAO,UAAU,CAAC;AAAA,IACpD,CAAC;AACD,iBAAa,eAAe,cAAc,SAAS;AAAA,EACvD,OACK;AACD,WAAO,aAAa;AAAA,EACxB;AACA,SAAO;AACX;AAQA,SAAS,cAAc,QAAQ,WAAW;AACtC,MAAI,OAAO,cAAc,WAAW;AAChC,WAAO;AAAA,EACX,WACS,qBAAqB,QAAQ;AAClC,WAAO,UAAU,KAAK,MAAM;AAAA,EAChC,WACS,MAAM,QAAQ,SAAS,GAAG;AAC/B,UAAM,sBAAsB,UAAU,IAAI,CAAC,SAAS,KAAK,YAAY,CAAC;AACtE,WAAO,oBAAoB,SAAS,MAAM;AAAA,EAC9C,OACK;AACD,WAAO;AAAA,EACX;AACJ;AAQA,SAAS,uBAAuB,QAAQ,WAAW;AAC/C,MAAI,YAAY,MAAM;AACtB,MAAI,OAAO,cAAc,WAAW;AAChC,WAAO,YAAY,SAAS,CAAC;AAAA,EACjC,WACS,qBAAqB,QAAQ;AAClC,gBAAY,CAAC,SAAS,UAAU,KAAK,IAAI;AAAA,EAC7C,WACS,MAAM,QAAQ,SAAS,GAAG;AAC/B,UAAM,sBAAsB,UAAU,IAAI,CAAC,SAAS,KAAK,YAAY,CAAC;AACtE,gBAAY,CAAC,SAAS,oBAAoB,SAAS,IAAI;AAAA,EAC3D,OACK;AACD,WAAO,CAAC;AAAA,EACZ;AACA,SAAO,OAAO,KAAK,MAAM,EACpB,IAAI,CAAC,QAAQ,IAAI,YAAY,CAAC,EAC9B,OAAO,CAAC,QAAQ,UAAU,GAAG,CAAC,EAC9B,OAAO,CAAC,SAAS,QAAQ;AAC1B,YAAQ,GAAG,IAAI,OAAO,GAAG;AACzB,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AAOA,SAAS,YAAY,cAAc;AAC/B,MAAI,OAAO,iBAAiB,UAAU;AAClC,WAAO,CAAC;AAAA,EACZ;AACA,MAAI;AACA,WAAO,aACF,MAAM,GAAG,EACT,IAAI,CAAC,SAAS,KAAK,MAAM,GAAG,CAAC,EAC7B,OAAO,CAAC,KAAK,CAAC,WAAW,WAAW,MAAM;AAC3C,UAAI,mBAAmB,UAAU,KAAK,CAAC,CAAC,IAAI,mBAAmB,YAAY,KAAK,CAAC;AACjF,aAAO;AAAA,IACX,GAAG,CAAC,CAAC;AAAA,EACT,QACA;AACI,WAAO,CAAC;AAAA,EACZ;AACJ;AAOA,SAASC,mBAAkB,cAAc,KAAK;AAC1C,QAAM,mBAAmB,CAAC;AAC1B,eAAa,QAAQ,CAAC,gBAAgB;AAClC,qBAAiB,YAAY,IAAI,IAAI;AACrC,gBAAY,UAAU,CAAC,aAAa;AAChC,UAAI,SAAS,GAAG,kBAAkB,QAAQ;AAAA,IAC9C,GAAG,MAAM,GAAG;AAAA,EAChB,CAAC;AACD,SAAO;AACX;AAKA,IAAM,eAAN,cAA2B,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlC,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP,YAAY,SAAS;AACjB,YAAQ,YAAY,QAAQ,aAAa,CAAC;AAC1C,YAAQ,UAAU,MAAM,QAAQ,UAAU,OAAO;AAAA,MAC7C,MAAM;AAAA,MACN,UAAU;AAAA,QACN;AAAA,UACI,MAAM;AAAA,UACN,SAAS;AAAA,QACb;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,IACb;AACA,UAAM,OAAO;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAChB,QAAI,KAAK,WAAW,KAAK,CAAC,KAAK,4BAA4B,KAAK,MAAM;AAClE,WAAK,gBAAgBA,mBAAkB,KAAK,SAAS,cAAc,KAAK,IAAI;AAC5E,WAAK,2BAA2B;AAAA,IACpC;AAAA,EACJ;AAAA,EACA,mBAAmB,WAAW,MAAM;AAChC,WAAO,oBAAoB,sBAAsB,KAAK,MAAM,KAAK,SAAS,aAAa,WAAW,IAAI,CAAC;AAAA,EAC3G;AAAA,EACA,iBAAiB,SAAS,QAAQ,QAAQ,MAAM;AAC5C,WAAO,oBAAoB,iBAAiB,KAAK,SAAS,aAAa,SAAS,OAAO,MAAM,KAAK,SAAS,gBAAgB,CAAC;AAAA,EAChI;AAAA,EACA,cAAc,OAAO,MAAM,OAAO;AAC9B,UAAM,WAAW,MAAM,YAAY;AACnC,QAAI,KAAK,WAAW,EAAE,SAAS;AAE3B,YAAM,wBAAwB,cAAc,MAAM,uBAAuB;AAAA,QACrE;AAAA,QACA,KAAK,WAAW,EAAE;AAAA,MACtB,CAAC;AAAA,IACL;AACA,QAAI,KAAK,WAAW,EAAE,aAAa;AAE/B,YAAM,wBAAwB,cAAc,MAAM,uBAAuB;AAAA,QACrE;AAAA,QACA,KAAK,WAAW,EAAE;AAAA,MACtB,CAAC;AAAA,IACL;AACA,WAAO,MAAM,cAAc,OAAO,MAAM,KAAK;AAAA,EACjD;AAAA,EACA,SAAS;AACL,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,OAAO,KAAK;AACR,SAAK,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,MAAM;AACjB,SAAK,WAAW,EAAE,cAAc;AAAA,EACpC;AACJ;AAOA,SAAS,uBAAuBC,YAAW;AACvC,QAAM,CAAC,MAAM,IAAI,IAAI,oBAAoBA,UAAS;AAClD,QAAM,KAAK,CAAC,SAAS;AACjB,UAAM,SAAS,KAAK,IAAI;AACxB,QAAI,QAAQ;AACR,YAAM,WAAW,OAAO;AAExB,aAAO,WACH,aAAa,UAAa,CAAC,SAAS,WAAW,GAAG,IAC5C,IAAI,aACJ;AAGV,aAAO,SAAS,aAAa;AAAA,IACjC;AACA,WAAO;AAAA,EACX;AACA,SAAO,CAAC,MAAM,EAAE;AACpB;AAOA,SAAS,UAAU,UAAU;AACzB,MAAI,CAAC,UAAU;AACX;AAAA,EACJ;AAEA,SAAO,SAAS,UAAU,KAAK;AACnC;AAEA,IAAM,qBAAqB,kBAAkB,uBAAuB,SAAS,CAAC;AAK9E,SAAS,mBAAmB,SAAS;AACjC,WAAS,YAAY,EAAE,KAAM,GAAG;AAC5B,QAAI;AACA,YAAM,UAAU,MAAM,QAAQ,KAAK;AAAA,QAC/B,QAAQ;AAAA,QACR,SAAS,QAAQ;AAAA,QACjB;AAAA,MACJ,CAAC,EAAE,KAAK,CAAC,aAAa;AAClB,eAAO;AAAA,UACH,YAAY,SAAS;AAAA,UACrB,SAAS;AAAA,YACL,eAAe,SAAS,QAAQ,IAAI,aAAa;AAAA,YACjD,wBAAwB,SAAS,QAAQ,IAAI,sBAAsB;AAAA,UACvE;AAAA,QACJ;AAAA,MACJ,CAAC;AAID,UAAI,QAAQ,SAAS;AACjB,gBAAQ,QAAQ,UAAU,OAAO;AAAA,MACrC;AACA,aAAO;AAAA,IACX,SACO,GAAP;AACI,aAAO,oBAAoB,CAAC;AAAA,IAChC;AAAA,EACJ;AACA,SAAO,gBAAgB,SAAS,WAAW;AAC/C;AAKA,IAAM,SAAN,cAAqB,IAAI;AAAA,EACrB,YAAY,SAAS;AACjB,YAAQ,sBACJ,QAAQ,wBAAwB,QAC1B,CAAC,IACD;AAAA,MACE,GAAI,MAAM,QAAQ,QAAQ,mBAAmB,IACvC,QAAQ,sBACR;AAAA,QACE,IAAI,YAAY,QAAQ,kBAAkB;AAAA,QAC1C,IAAI,aAAa;AAAA,MACrB;AAAA,IACR;AACR,QAAI,QAAQ,YAAY,QAAW;AAC/B,YAAM,kBAAkB,iBAAiB;AACzC,UAAI,oBAAoB,QAAW;AAC/B,gBAAQ,UAAU;AAAA,MACtB;AAAA,IACJ;AACA,UAAM,SAAS,IAAI,aAAa;AAAA,MAC5B,GAAG;AAAA,MACH,WAAW;AAAA,MACX,cAAc,uBAAuB,OAAO;AAAA,MAC5C,aAAa,kCAAkC,QAAQ,eAAe,kBAAkB;AAAA,MACxF,kBAAkB;AAAA,QACd,GAAG,QAAQ;AAAA,QACX,SAAS,QAAQ;AAAA,MACrB;AAAA,IACJ,CAAC;AACD,UAAM,MAAM;AACZ,WAAO,OAAO,IAAI;AAClB,WAAO,kBAAkB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,MAAM;AACjB,SAAK,UAAU,GAAG,eAAe,IAAI;AAAA,EACzC;AACJ;;;AC9mBO,IAAM,YAAuC,OAAO,YAAY;AACrE,UAAQ,KAAK,SAAS,IAAI,OAAO;AAAA,IAC/B;AAAA,IACA,GAAG,QAAQ;AAAA,EACb,CAAC;AAED,MAAI;AACF,WAAO,MAAM,QAAQ,KAAK;AAAA,EAC5B,SAAS,QAAP;AACA,YAAQ,KAAK,OAAO,iBAAiB,MAAM;AAC3C,UAAM;AAAA,EACR;AACF;;;ACnBO,IAAM,SAAS;AAAA,EAClB;AAAA,IACE,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,aAAa,CAAC,SAA0B;AAAA,IACxC,SAAS,CAAC;AAAA,EACZ;AACF;;;ACUF,SAAS,MAAM,KAAW;AACxB,MAAM,SAAqB,CAAA;AAC3B,MAAI,IAAI;AAER,SAAO,IAAI,IAAI,QAAQ;AACrB,QAAM,OAAO,IAAI,CAAC;AAElB,QAAI,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;AAChD,aAAO,KAAK,EAAE,MAAM,YAAY,OAAO,GAAG,OAAO,IAAI,GAAG,EAAC,CAAE;AAC3D;;AAGF,QAAI,SAAS,MAAM;AACjB,aAAO,KAAK,EAAE,MAAM,gBAAgB,OAAO,KAAK,OAAO,IAAI,GAAG,EAAC,CAAE;AACjE;;AAGF,QAAI,SAAS,KAAK;AAChB,aAAO,KAAK,EAAE,MAAM,QAAQ,OAAO,GAAG,OAAO,IAAI,GAAG,EAAC,CAAE;AACvD;;AAGF,QAAI,SAAS,KAAK;AAChB,aAAO,KAAK,EAAE,MAAM,SAAS,OAAO,GAAG,OAAO,IAAI,GAAG,EAAC,CAAE;AACxD;;AAGF,QAAI,SAAS,KAAK;AAChB,UAAI,OAAO;AACX,UAAI,IAAI,IAAI;AAEZ,aAAO,IAAI,IAAI,QAAQ;AACrB,YAAM,OAAO,IAAI,WAAW,CAAC;AAE7B;;UAEG,QAAQ,MAAM,QAAQ;UAEtB,QAAQ,MAAM,QAAQ;UAEtB,QAAQ,MAAM,QAAQ;UAEvB,SAAS;UACT;AACA,kBAAQ,IAAI,GAAG;AACf;;AAGF;;AAGF,UAAI,CAAC;AAAM,cAAM,IAAI,UAAU,6BAAA,OAA6B,CAAC,CAAE;AAE/D,aAAO,KAAK,EAAE,MAAM,QAAQ,OAAO,GAAG,OAAO,KAAI,CAAE;AACnD,UAAI;AACJ;;AAGF,QAAI,SAAS,KAAK;AAChB,UAAI,QAAQ;AACZ,UAAI,UAAU;AACd,UAAI,IAAI,IAAI;AAEZ,UAAI,IAAI,CAAC,MAAM,KAAK;AAClB,cAAM,IAAI,UAAU,oCAAA,OAAoC,CAAC,CAAE;;AAG7D,aAAO,IAAI,IAAI,QAAQ;AACrB,YAAI,IAAI,CAAC,MAAM,MAAM;AACnB,qBAAW,IAAI,GAAG,IAAI,IAAI,GAAG;AAC7B;;AAGF,YAAI,IAAI,CAAC,MAAM,KAAK;AAClB;AACA,cAAI,UAAU,GAAG;AACf;AACA;;mBAEO,IAAI,CAAC,MAAM,KAAK;AACzB;AACA,cAAI,IAAI,IAAI,CAAC,MAAM,KAAK;AACtB,kBAAM,IAAI,UAAU,uCAAA,OAAuC,CAAC,CAAE;;;AAIlE,mBAAW,IAAI,GAAG;;AAGpB,UAAI;AAAO,cAAM,IAAI,UAAU,yBAAA,OAAyB,CAAC,CAAE;AAC3D,UAAI,CAAC;AAAS,cAAM,IAAI,UAAU,sBAAA,OAAsB,CAAC,CAAE;AAE3D,aAAO,KAAK,EAAE,MAAM,WAAW,OAAO,GAAG,OAAO,QAAO,CAAE;AACzD,UAAI;AACJ;;AAGF,WAAO,KAAK,EAAE,MAAM,QAAQ,OAAO,GAAG,OAAO,IAAI,GAAG,EAAC,CAAE;;AAGzD,SAAO,KAAK,EAAE,MAAM,OAAO,OAAO,GAAG,OAAO,GAAE,CAAE;AAEhD,SAAO;AACT;AAgBM,SAAU,MAAM,KAAa,SAA0B;AAA1B,MAAA,YAAA,QAAA;AAAA,cAAA,CAAA;EAA0B;AAC3D,MAAM,SAAS,MAAM,GAAG;AAChB,MAAA,KAAoB,QAAO,UAA3B,WAAQ,OAAA,SAAG,OAAI;AACvB,MAAM,iBAAiB,KAAA,OAAK,aAAa,QAAQ,aAAa,KAAK,GAAC,KAAA;AACpE,MAAM,SAAkB,CAAA;AACxB,MAAI,MAAM;AACV,MAAI,IAAI;AACR,MAAI,OAAO;AAEX,MAAM,aAAa,SAAC,MAAsB;AACxC,QAAI,IAAI,OAAO,UAAU,OAAO,CAAC,EAAE,SAAS;AAAM,aAAO,OAAO,GAAG,EAAE;EACvE;AAEA,MAAM,cAAc,SAAC,MAAsB;AACzC,QAAMC,SAAQ,WAAW,IAAI;AAC7B,QAAIA,WAAU;AAAW,aAAOA;AAC1B,QAAAC,MAA4B,OAAO,CAAC,GAA5B,WAAQA,IAAA,MAAE,QAAKA,IAAA;AAC7B,UAAM,IAAI,UAAU,cAAA,OAAc,UAAQ,MAAA,EAAA,OAAO,OAAK,aAAA,EAAA,OAAc,IAAI,CAAE;EAC5E;AAEA,MAAM,cAAc,WAAA;AAClB,QAAIC,UAAS;AACb,QAAIF;AACJ,WAAQA,SAAQ,WAAW,MAAM,KAAK,WAAW,cAAc,GAAI;AACjE,MAAAE,WAAUF;;AAEZ,WAAOE;EACT;AAEA,SAAO,IAAI,OAAO,QAAQ;AACxB,QAAM,OAAO,WAAW,MAAM;AAC9B,QAAM,OAAO,WAAW,MAAM;AAC9B,QAAM,UAAU,WAAW,SAAS;AAEpC,QAAI,QAAQ,SAAS;AACnB,UAAI,SAAS,QAAQ;AAErB,UAAI,SAAS,QAAQ,MAAM,MAAM,IAAI;AACnC,gBAAQ;AACR,iBAAS;;AAGX,UAAI,MAAM;AACR,eAAO,KAAK,IAAI;AAChB,eAAO;;AAGT,aAAO,KAAK;QACV,MAAM,QAAQ;QACd;QACA,QAAQ;QACR,SAAS,WAAW;QACpB,UAAU,WAAW,UAAU,KAAK;OACrC;AACD;;AAGF,QAAM,QAAQ,QAAQ,WAAW,cAAc;AAC/C,QAAI,OAAO;AACT,cAAQ;AACR;;AAGF,QAAI,MAAM;AACR,aAAO,KAAK,IAAI;AAChB,aAAO;;AAGT,QAAM,OAAO,WAAW,MAAM;AAC9B,QAAI,MAAM;AACR,UAAM,SAAS,YAAW;AAC1B,UAAM,SAAO,WAAW,MAAM,KAAK;AACnC,UAAM,YAAU,WAAW,SAAS,KAAK;AACzC,UAAM,SAAS,YAAW;AAE1B,kBAAY,OAAO;AAEnB,aAAO,KAAK;QACV,MAAM,WAAS,YAAU,QAAQ;QACjC,SAAS,UAAQ,CAAC,YAAU,iBAAiB;QAC7C;QACA;QACA,UAAU,WAAW,UAAU,KAAK;OACrC;AACD;;AAGF,gBAAY,KAAK;;AAGnB,SAAO;AACT;AA+IM,SAAU,MACd,KACA,SAAwE;AAExE,MAAM,OAAc,CAAA;AACpB,MAAM,KAAK,aAAa,KAAK,MAAM,OAAO;AAC1C,SAAO,iBAAoB,IAAI,MAAM,OAAO;AAC9C;AAKM,SAAU,iBACd,IACA,MACA,SAAqC;AAArC,MAAA,YAAA,QAAA;AAAA,cAAA,CAAA;EAAqC;AAE7B,MAAA,KAA8B,QAAO,QAArC,SAAM,OAAA,SAAG,SAAC,GAAS;AAAK,WAAA;EAAA,IAAC;AAEjC,SAAO,SAAU,UAAgB;AAC/B,QAAM,IAAI,GAAG,KAAK,QAAQ;AAC1B,QAAI,CAAC;AAAG,aAAO;AAEP,QAAG,OAAgB,EAAC,CAAA,GAAX,QAAU,EAAC;AAC5B,QAAM,SAAS,uBAAO,OAAO,IAAI;2BAExBC,IAAC;AACR,UAAI,EAAEA,EAAC,MAAM;;AAEb,UAAM,MAAM,KAAKA,KAAI,CAAC;AAEtB,UAAI,IAAI,aAAa,OAAO,IAAI,aAAa,KAAK;AAChD,eAAO,IAAI,IAAI,IAAI,EAAEA,EAAC,EAAE,MAAM,IAAI,SAAS,IAAI,MAAM,EAAE,IAAI,SAAC,OAAK;AAC/D,iBAAO,OAAO,OAAO,GAAG;QAC1B,CAAC;aACI;AACL,eAAO,IAAI,IAAI,IAAI,OAAO,EAAEA,EAAC,GAAG,GAAG;;;AAVvC,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAG;cAAxB,CAAC;;AAcV,WAAO,EAAE,MAAM,OAAO,OAAM;EAC9B;AACF;AAKA,SAAS,aAAa,KAAW;AAC/B,SAAO,IAAI,QAAQ,6BAA6B,MAAM;AACxD;AAKA,SAAS,MAAM,SAAiC;AAC9C,SAAO,WAAW,QAAQ,YAAY,KAAK;AAC7C;AAqBA,SAAS,eAAe,MAAc,MAAY;AAChD,MAAI,CAAC;AAAM,WAAO;AAElB,MAAM,cAAc;AAEpB,MAAI,QAAQ;AACZ,MAAI,aAAa,YAAY,KAAK,KAAK,MAAM;AAC7C,SAAO,YAAY;AACjB,SAAK,KAAK;;MAER,MAAM,WAAW,CAAC,KAAK;MACvB,QAAQ;MACR,QAAQ;MACR,UAAU;MACV,SAAS;KACV;AACD,iBAAa,YAAY,KAAK,KAAK,MAAM;;AAG3C,SAAO;AACT;AAKA,SAAS,cACP,OACA,MACA,SAA8C;AAE9C,MAAM,QAAQ,MAAM,IAAI,SAAC,MAAI;AAAK,WAAA,aAAa,MAAM,MAAM,OAAO,EAAE;EAAlC,CAAwC;AAC1E,SAAO,IAAI,OAAO,MAAA,OAAM,MAAM,KAAK,GAAG,GAAC,GAAA,GAAK,MAAM,OAAO,CAAC;AAC5D;AAKA,SAAS,eACP,MACA,MACA,SAA8C;AAE9C,SAAO,eAAe,MAAM,MAAM,OAAO,GAAG,MAAM,OAAO;AAC3D;AAoCM,SAAU,eACd,QACA,MACA,SAAmC;AAAnC,MAAA,YAAA,QAAA;AAAA,cAAA,CAAA;EAAmC;AAGjC,MAAA,KAME,QAAO,QANT,SAAM,OAAA,SAAG,QAAK,IACd,KAKE,QAAO,OALT,QAAK,OAAA,SAAG,OAAI,IACZ,KAIE,QAAO,KAJT,MAAG,OAAA,SAAG,OAAI,IACV,KAGE,QAAO,QAHT,SAAM,OAAA,SAAG,SAAC,GAAS;AAAK,WAAA;EAAA,IAAC,IACzB,KAEE,QAAO,WAFT,YAAS,OAAA,SAAG,QAAK,IACjB,KACE,QAAO,UADT,WAAQ,OAAA,SAAG,KAAE;AAEf,MAAM,aAAa,IAAA,OAAI,aAAa,QAAQ,GAAC,KAAA;AAC7C,MAAM,cAAc,IAAA,OAAI,aAAa,SAAS,GAAC,GAAA;AAC/C,MAAI,QAAQ,QAAQ,MAAM;AAG1B,WAAoB,KAAA,GAAA,WAAA,QAAA,KAAA,SAAA,QAAA,MAAQ;AAAvB,QAAM,QAAK,SAAA,EAAA;AACd,QAAI,OAAO,UAAU,UAAU;AAC7B,eAAS,aAAa,OAAO,KAAK,CAAC;WAC9B;AACL,UAAM,SAAS,aAAa,OAAO,MAAM,MAAM,CAAC;AAChD,UAAM,SAAS,aAAa,OAAO,MAAM,MAAM,CAAC;AAEhD,UAAI,MAAM,SAAS;AACjB,YAAI;AAAM,eAAK,KAAK,KAAK;AAEzB,YAAI,UAAU,QAAQ;AACpB,cAAI,MAAM,aAAa,OAAO,MAAM,aAAa,KAAK;AACpD,gBAAM,MAAM,MAAM,aAAa,MAAM,MAAM;AAC3C,qBAAS,MAAA,OAAM,QAAM,MAAA,EAAA,OAAO,MAAM,SAAO,MAAA,EAAA,OAAO,MAAM,EAAA,OAAG,QAAM,KAAA,EAAA,OAAM,MAAM,SAAO,MAAA,EAAA,OAAO,QAAM,GAAA,EAAA,OAAI,GAAG;iBACjG;AACL,qBAAS,MAAA,OAAM,QAAM,GAAA,EAAA,OAAI,MAAM,SAAO,GAAA,EAAA,OAAI,QAAM,GAAA,EAAA,OAAI,MAAM,QAAQ;;eAE/D;AACL,cAAI,MAAM,aAAa,OAAO,MAAM,aAAa,KAAK;AACpD,qBAAS,OAAA,OAAO,MAAM,SAAO,GAAA,EAAA,OAAI,MAAM,UAAQ,GAAA;iBAC1C;AACL,qBAAS,IAAA,OAAI,MAAM,SAAO,GAAA,EAAA,OAAI,MAAM,QAAQ;;;aAG3C;AACL,iBAAS,MAAA,OAAM,MAAM,EAAA,OAAG,QAAM,GAAA,EAAA,OAAI,MAAM,QAAQ;;;;AAKtD,MAAI,KAAK;AACP,QAAI,CAAC;AAAQ,eAAS,GAAA,OAAG,aAAW,GAAA;AAEpC,aAAS,CAAC,QAAQ,WAAW,MAAM,MAAA,OAAM,YAAU,GAAA;SAC9C;AACL,QAAM,WAAW,OAAO,OAAO,SAAS,CAAC;AACzC,QAAM,iBACJ,OAAO,aAAa,WAChB,YAAY,QAAQ,SAAS,SAAS,SAAS,CAAC,CAAC,IAAI,KACrD,aAAa;AAEnB,QAAI,CAAC,QAAQ;AACX,eAAS,MAAA,OAAM,aAAW,KAAA,EAAA,OAAM,YAAU,KAAA;;AAG5C,QAAI,CAAC,gBAAgB;AACnB,eAAS,MAAA,OAAM,aAAW,GAAA,EAAA,OAAI,YAAU,GAAA;;;AAI5C,SAAO,IAAI,OAAO,OAAO,MAAM,OAAO,CAAC;AACzC;AAcM,SAAU,aACd,MACA,MACA,SAA8C;AAE9C,MAAI,gBAAgB;AAAQ,WAAO,eAAe,MAAM,IAAI;AAC5D,MAAI,MAAM,QAAQ,IAAI;AAAG,WAAO,cAAc,MAAM,MAAM,OAAO;AACjE,SAAO,eAAe,MAAM,MAAM,OAAO;AAC3C;;;ACzmBA,IAAM,cAAc;AAgEpB,UAAU,eAAe,SAAkB,kBAA0B;AAEpE,aAAW,SAAS,CAAC,GAAG,MAAM,EAAE,QAAQ,GAAG;AAC1C,QAAI,MAAM,UAAU,MAAM,WAAW,QAAQ,QAAQ;AACpD;AAAA,IACD;AAGA,UAAM,eAAe,MAAM,MAAM,UAAU,QAAQ,aAAa,MAAM,GAAG;AAAA,MACxE,KAAK;AAAA,IACN,CAAC;AACD,UAAM,eAAe,MAAM,MAAM,UAAU,QAAQ,aAAa,MAAM,GAAG;AAAA,MACxE,KAAK;AAAA,IACN,CAAC;AACD,UAAM,cAAc,aAAa,gBAAgB;AACjD,UAAM,mBAAmB,aAAa,gBAAgB;AACtD,QAAI,eAAe,kBAAkB;AACpC,iBAAWC,YAAW,MAAM,YAAY,KAAK,GAAG;AAC/C,cAAM;AAAA,UACL,SAAAA;AAAA,UACA,QAAQ,YAAY;AAAA,UACpB,MAAM,iBAAiB;AAAA,QACxB;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAGA,aAAW,SAAS,QAAQ;AAC3B,QAAI,MAAM,UAAU,MAAM,WAAW,QAAQ,QAAQ;AACpD;AAAA,IACD;AAEA,UAAM,eAAe,MAAM,MAAM,UAAU,QAAQ,aAAa,MAAM,GAAG;AAAA,MACxE,KAAK;AAAA,IACN,CAAC;AACD,UAAM,eAAe,MAAM,MAAM,UAAU,QAAQ,aAAa,MAAM,GAAG;AAAA,MACxE,KAAK;AAAA,IACN,CAAC;AACD,UAAM,cAAc,aAAa,gBAAgB;AACjD,UAAM,mBAAmB,aAAa,gBAAgB;AACtD,QAAI,eAAe,oBAAoB,MAAM,QAAQ,QAAQ;AAC5D,iBAAWA,YAAW,MAAM,QAAQ,KAAK,GAAG;AAC3C,cAAM;AAAA,UACL,SAAAA;AAAA,UACA,QAAQ,YAAY;AAAA,UACpB,MAAM,YAAY;AAAA,QACnB;AAAA,MACD;AACA;AAAA,IACD;AAAA,EACD;AACD;AAEe,SAAR,8BAAkB,YAAqB;AAC7C,QAAMC,aAAiC,OAAO,kBAAkB;AAC/D,QAAI,EAAE,QAAQ,IAAI;AAClB,UAAM,EAAE,KAAK,KAAK,IAAI;AACtB,QAAI,EAAE,KAAK,IAAI;AAEf,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAE/B,UAAM,mBAAmB,IACxB,IAAI,SAAS,QAAQ,cAAc,cAAc,EAAE,KAAK,KACtD,QAAQ,SAAS,GAAG;AAEvB,UAAM,kBAAkB,eAAe,SAAS,gBAAgB;AAChE,UAAM,aAAa,OAAO,OAAqB,SAAuB;AACrE,UAAI,UAAU,QAAW;AACxB,YAAIC,OAAM;AACV,YAAI,OAAO,UAAU,UAAU;AAC9B,UAAAA,OAAM,IAAI,IAAI,OAAO,QAAQ,GAAG,EAAE,SAAS;AAAA,QAC5C;AACA,kBAAU,IAAI,QAAQA,MAAK,IAAI;AAAA,MAChC;AAEA,YAAM,SAAS,gBAAgB,KAAK;AAEpC,UAAI,OAAO,SAAS,OAAO;AAC1B,cAAM,EAAE,SAAAF,UAAS,QAAQ,KAAK,IAAI,OAAO;AACzC,cAAM,UAAU;AAAA,UACf,SAAS,IAAI,QAAQ,QAAQ,MAAM,CAAC;AAAA,UACpC,cAAc,cAAc,eAAe;AAAA,UAC3C,MAAM;AAAA,UACN;AAAA,UACA,IAAI,OAAO;AACV,mBAAO;AAAA,UACR;AAAA,UACA,IAAI,KAAK,OAAO;AACf,gBAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAChD,oBAAM,IAAI,MAAM,gCAAgC;AAAA,YACjD;AAEA,mBAAO;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA,WAAW,cAAc,UAAU,KAAK,aAAa;AAAA,UACrD,wBACC,cAAc,uBAAuB,KAAK,aAAa;AAAA,QACzD;AAEA,cAAM,WAAW,MAAMA,SAAQ,OAAO;AAEtC,eAAO,cAAc,QAAQ;AAAA,MAC9B,OAAO;AACN,eAAO,KAAK,OAAO;AAAA,MACpB;AAAA,IACD;AAEA,WAAO,WAAW;AAAA,EACnB;AAEA,SAAOC;AACR;AAGA,IAAM,gBAAgB,CAAC;AAAA;AAAA,EAEtB,IAAI;AAAA,IACH,CAAC,KAAK,KAAK,KAAK,GAAG,EAAE,SAAS,SAAS,MAAM,IAAI,OAAO,SAAS;AAAA,IACjE;AAAA,EACD;AAAA;", "names": ["isNaN", "match", "logger", "getModule", "isNaN", "States", "resolve", "handler", "resolve", "WINDOW", "WINDOW", "resolve", "resolve", "addGlobalEventProcessor", "getCurrentHub", "self", "setupIntegrations", "getModule", "value", "_a", "result", "i", "handler", "onRequest", "url"]}