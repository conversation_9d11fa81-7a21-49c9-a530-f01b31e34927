<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件夹API测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { margin: 5px; padding: 8px 15px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>文件夹管理API测试</h1>
    
    <div class="test-section">
        <h3>1. 创建文件夹</h3>
        <input type="text" id="folderName" placeholder="文件夹名称" value="测试文件夹">
        <button onclick="createFolder()">创建文件夹</button>
        <div id="createResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 获取文件夹列表</h3>
        <button onclick="getFolders()">获取文件夹</button>
        <div id="foldersResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 移动文件到文件夹</h3>
        <input type="text" id="fileId" placeholder="文件ID">
        <input type="text" id="targetFolder" placeholder="目标文件夹路径">
        <button onclick="moveFile()">移动文件</button>
        <div id="moveResult" class="result"></div>
    </div>

    <script>
        async function createFolder() {
            const folderName = document.getElementById('folderName').value;
            const resultDiv = document.getElementById('createResult');
            
            try {
                const response = await fetch(`./api/manage/createFolder?folderName=${encodeURIComponent(folderName)}`, {
                    method: 'GET',
                    credentials: 'include'
                });
                const result = await response.json();
                resultDiv.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<span style="color: red;">错误: ${error.message}</span>`;
            }
        }
        
        async function getFolders() {
            const resultDiv = document.getElementById('foldersResult');
            
            try {
                const response = await fetch('./api/manage/getFolders', {
                    method: 'GET',
                    credentials: 'include'
                });
                const result = await response.json();
                resultDiv.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<span style="color: red;">错误: ${error.message}</span>`;
            }
        }
        
        async function moveFile() {
            const fileId = document.getElementById('fileId').value;
            const targetFolder = document.getElementById('targetFolder').value;
            const resultDiv = document.getElementById('moveResult');
            
            try {
                const response = await fetch(`./api/manage/moveToFolder/${fileId}?targetFolder=${encodeURIComponent(targetFolder)}`, {
                    method: 'GET',
                    credentials: 'include'
                });
                const result = await response.json();
                resultDiv.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<span style="color: red;">错误: ${error.message}</span>`;
            }
        }
    </script>
</body>
</html>
