{"version": 3, "file": "metadata.js", "sources": ["../../../src/integrations/metadata.ts"], "sourcesContent": ["import type { Client, Event, EventHint, EventItem, Integration, IntegrationClass, IntegrationFn } from '@sentry/types';\nimport { forEachEnvelopeItem } from '@sentry/utils';\nimport { convertIntegrationFnToClass, defineIntegration } from '../integration';\n\nimport { addMetadataToStackFrames, stripMetadataFromStackFrames } from '../metadata';\n\nconst INTEGRATION_NAME = 'ModuleMetadata';\n\nconst _moduleMetadataIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    // TODO v8: Remove this\n    setupOnce() {}, // eslint-disable-line @typescript-eslint/no-empty-function\n    setup(client) {\n      if (typeof client.on !== 'function') {\n        return;\n      }\n\n      // We need to strip metadata from stack frames before sending them to Sentry since these are client side only.\n      client.on('beforeEnvelope', envelope => {\n        forEachEnvelopeItem(envelope, (item, type) => {\n          if (type === 'event') {\n            const event = Array.isArray(item) ? (item as EventItem)[1] : undefined;\n\n            if (event) {\n              stripMetadataFromStackFrames(event);\n              item[1] = event;\n            }\n          }\n        });\n      });\n    },\n\n    processEvent(event, _hint, client) {\n      const stackParser = client.getOptions().stackParser;\n      addMetadataToStackFrames(stackParser, event);\n      return event;\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const moduleMetadataIntegration = defineIntegration(_moduleMetadataIntegration);\n\n/**\n * Adds module metadata to stack frames.\n *\n * Metadata can be injected by the Sentry bundler plugins using the `_experiments.moduleMetadata` config option.\n *\n * When this integration is added, the metadata passed to the bundler plugin is added to the stack frames of all events\n * under the `module_metadata` property. This can be used to help in tagging or routing of events from different teams\n * our sources\n *\n * @deprecated Use `moduleMetadataIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const ModuleMetadata = convertIntegrationFnToClass(\n  INTEGRATION_NAME,\n  moduleMetadataIntegration,\n) as IntegrationClass<\n  Integration & {\n    setup: (client: Client) => void;\n    processEvent: (event: Event, hint: EventHint, client: Client) => Event;\n  }\n>;\n"], "names": ["forEachEnvelopeItem", "stripMetadataFromStackFrames", "addMetadataToStackFrames", "defineIntegration", "convertIntegrationFnToClass"], "mappings": ";;;;;;AAMA,MAAM,gBAAA,GAAmB,gBAAgB,CAAA;AACzC;AACA,MAAM,0BAA2B,IAAG,MAAM;AAC1C,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B;AACA,IAAI,SAAS,GAAG,EAAE;AAClB,IAAI,KAAK,CAAC,MAAM,EAAE;AAClB,MAAM,IAAI,OAAO,MAAM,CAAC,EAAG,KAAI,UAAU,EAAE;AAC3C,QAAQ,OAAM;AACd,OAAM;AACN;AACA;AACA,MAAM,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,YAAY;AAC9C,QAAQA,yBAAmB,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK;AACtD,UAAU,IAAI,IAAK,KAAI,OAAO,EAAE;AAChC,YAAY,MAAM,KAAM,GAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAE,GAAE,CAAC,IAAK,GAAc,CAAC,CAAA,GAAI,SAAS,CAAA;AAClF;AACA,YAAY,IAAI,KAAK,EAAE;AACvB,cAAcC,qCAA4B,CAAC,KAAK,CAAC,CAAA;AACjD,cAAc,IAAI,CAAC,CAAC,CAAA,GAAI,KAAK,CAAA;AAC7B,aAAY;AACZ,WAAU;AACV,SAAS,CAAC,CAAA;AACV,OAAO,CAAC,CAAA;AACR,KAAK;AACL;AACA,IAAI,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE;AACvC,MAAM,MAAM,cAAc,MAAM,CAAC,UAAU,EAAE,CAAC,WAAW,CAAA;AACzD,MAAMC,iCAAwB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;AAClD,MAAM,OAAO,KAAK,CAAA;AAClB,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;MACa,yBAA0B,GAAEC,6BAAiB,CAAC,0BAA0B,EAAC;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,cAAe,GAAEC,uCAA2B;AACzD,EAAE,gBAAgB;AAClB,EAAE,yBAAyB;AAC3B,CAAE;;;;;;;"}