{"version": 3, "file": "session.js", "sources": ["../../src/session.ts"], "sourcesContent": ["import type { SerializedSession, Session, SessionContext, SessionStatus } from '@sentry/types';\nimport { dropUndefinedKeys, timestampInSeconds, uuid4 } from '@sentry/utils';\n/**\n * Creates a new `Session` object by setting certain default parameters. If optional @param context\n * is passed, the passed properties are applied to the session object.\n *\n * @param context (optional) additional properties to be applied to the returned session object\n *\n * @returns a new `Session` object\n */\nexport function makeSession(context?: Omit<SessionContext, 'started' | 'status'>): Session {\n  // Both timestamp and started are in seconds since the UNIX epoch.\n  const startingTime = timestampInSeconds();\n\n  const session: Session = {\n    sid: uuid4(),\n    init: true,\n    timestamp: startingTime,\n    started: startingTime,\n    duration: 0,\n    status: 'ok',\n    errors: 0,\n    ignoreDuration: false,\n    toJSON: () => sessionToJSON(session),\n  };\n\n  if (context) {\n    updateSession(session, context);\n  }\n\n  return session;\n}\n\n/**\n * Updates a session object with the properties passed in the context.\n *\n * Note that this function mutates the passed object and returns void.\n * (Had to do this instead of returning a new and updated session because closing and sending a session\n * makes an update to the session after it was passed to the sending logic.\n * @see BaseClient.captureSession )\n *\n * @param session the `Session` to update\n * @param context the `SessionContext` holding the properties that should be updated in @param session\n */\n// eslint-disable-next-line complexity\nexport function updateSession(session: Session, context: SessionContext = {}): void {\n  if (context.user) {\n    if (!session.ipAddress && context.user.ip_address) {\n      session.ipAddress = context.user.ip_address;\n    }\n\n    if (!session.did && !context.did) {\n      session.did = context.user.id || context.user.email || context.user.username;\n    }\n  }\n\n  session.timestamp = context.timestamp || timestampInSeconds();\n\n  if (context.abnormal_mechanism) {\n    session.abnormal_mechanism = context.abnormal_mechanism;\n  }\n\n  if (context.ignoreDuration) {\n    session.ignoreDuration = context.ignoreDuration;\n  }\n  if (context.sid) {\n    // Good enough uuid validation. — Kamil\n    session.sid = context.sid.length === 32 ? context.sid : uuid4();\n  }\n  if (context.init !== undefined) {\n    session.init = context.init;\n  }\n  if (!session.did && context.did) {\n    session.did = `${context.did}`;\n  }\n  if (typeof context.started === 'number') {\n    session.started = context.started;\n  }\n  if (session.ignoreDuration) {\n    session.duration = undefined;\n  } else if (typeof context.duration === 'number') {\n    session.duration = context.duration;\n  } else {\n    const duration = session.timestamp - session.started;\n    session.duration = duration >= 0 ? duration : 0;\n  }\n  if (context.release) {\n    session.release = context.release;\n  }\n  if (context.environment) {\n    session.environment = context.environment;\n  }\n  if (!session.ipAddress && context.ipAddress) {\n    session.ipAddress = context.ipAddress;\n  }\n  if (!session.userAgent && context.userAgent) {\n    session.userAgent = context.userAgent;\n  }\n  if (typeof context.errors === 'number') {\n    session.errors = context.errors;\n  }\n  if (context.status) {\n    session.status = context.status;\n  }\n}\n\n/**\n * Closes a session by setting its status and updating the session object with it.\n * Internally calls `updateSession` to update the passed session object.\n *\n * Note that this function mutates the passed session (@see updateSession for explanation).\n *\n * @param session the `Session` object to be closed\n * @param status the `SessionStatus` with which the session was closed. If you don't pass a status,\n *               this function will keep the previously set status, unless it was `'ok'` in which case\n *               it is changed to `'exited'`.\n */\nexport function closeSession(session: Session, status?: Exclude<SessionStatus, 'ok'>): void {\n  let context = {};\n  if (status) {\n    context = { status };\n  } else if (session.status === 'ok') {\n    context = { status: 'exited' };\n  }\n\n  updateSession(session, context);\n}\n\n/**\n * Serializes a passed session object to a JSON object with a slightly different structure.\n * This is necessary because the Sentry backend requires a slightly different schema of a session\n * than the one the JS SDKs use internally.\n *\n * @param session the session to be converted\n *\n * @returns a JSON object of the passed session\n */\nfunction sessionToJSON(session: Session): SerializedSession {\n  return dropUndefinedKeys({\n    sid: `${session.sid}`,\n    init: session.init,\n    // Make sure that sec is converted to ms for date constructor\n    started: new Date(session.started * 1000).toISOString(),\n    timestamp: new Date(session.timestamp * 1000).toISOString(),\n    status: session.status,\n    errors: session.errors,\n    did: typeof session.did === 'number' || typeof session.did === 'string' ? `${session.did}` : undefined,\n    duration: session.duration,\n    abnormal_mechanism: session.abnormal_mechanism,\n    attrs: {\n      release: session.release,\n      environment: session.environment,\n      ip_address: session.ipAddress,\n      user_agent: session.userAgent,\n    },\n  });\n}\n"], "names": ["timestampInSeconds", "uuid4", "dropUndefinedKeys"], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,CAAC,OAAO,EAAwD;AAC3F;AACA,EAAE,MAAM,YAAA,GAAeA,wBAAkB,EAAE,CAAA;AAC3C;AACA,EAAE,MAAM,OAAO,GAAY;AAC3B,IAAI,GAAG,EAAEC,WAAK,EAAE;AAChB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,SAAS,EAAE,YAAY;AAC3B,IAAI,OAAO,EAAE,YAAY;AACzB,IAAI,QAAQ,EAAE,CAAC;AACf,IAAI,MAAM,EAAE,IAAI;AAChB,IAAI,MAAM,EAAE,CAAC;AACb,IAAI,cAAc,EAAE,KAAK;AACzB,IAAI,MAAM,EAAE,MAAM,aAAa,CAAC,OAAO,CAAC;AACxC,GAAG,CAAA;AACH;AACA,EAAE,IAAI,OAAO,EAAE;AACf,IAAI,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;AACnC,GAAE;AACF;AACA,EAAE,OAAO,OAAO,CAAA;AAChB,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAC,OAAO,EAAW,OAAO,GAAmB,EAAE,EAAQ;AACpF,EAAE,IAAI,OAAO,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAA,IAAa,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE;AACvD,MAAM,OAAO,CAAC,SAAU,GAAE,OAAO,CAAC,IAAI,CAAC,UAAU,CAAA;AACjD,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,OAAO,CAAC,GAAI,IAAG,CAAC,OAAO,CAAC,GAAG,EAAE;AACtC,MAAM,OAAO,CAAC,GAAI,GAAE,OAAO,CAAC,IAAI,CAAC,EAAG,IAAG,OAAO,CAAC,IAAI,CAAC,KAAM,IAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAA;AAClF,KAAI;AACJ,GAAE;AACF;AACA,EAAE,OAAO,CAAC,SAAA,GAAY,OAAO,CAAC,SAAU,IAAGD,wBAAkB,EAAE,CAAA;AAC/D;AACA,EAAE,IAAI,OAAO,CAAC,kBAAkB,EAAE;AAClC,IAAI,OAAO,CAAC,kBAAA,GAAqB,OAAO,CAAC,kBAAkB,CAAA;AAC3D,GAAE;AACF;AACA,EAAE,IAAI,OAAO,CAAC,cAAc,EAAE;AAC9B,IAAI,OAAO,CAAC,cAAA,GAAiB,OAAO,CAAC,cAAc,CAAA;AACnD,GAAE;AACF,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE;AACnB;AACA,IAAI,OAAO,CAAC,GAAA,GAAM,OAAO,CAAC,GAAG,CAAC,MAAA,KAAW,EAAA,GAAK,OAAO,CAAC,MAAMC,WAAK,EAAE,CAAA;AACnE,GAAE;AACF,EAAE,IAAI,OAAO,CAAC,IAAK,KAAI,SAAS,EAAE;AAClC,IAAI,OAAO,CAAC,IAAA,GAAO,OAAO,CAAC,IAAI,CAAA;AAC/B,GAAE;AACF,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,OAAO,CAAC,GAAG,EAAE;AACnC,IAAI,OAAO,CAAC,GAAI,GAAE,CAAC,EAAA,OAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,OAAA,OAAA,CAAA,OAAA,KAAA,QAAA,EAAA;AACA,IAAA,OAAA,CAAA,OAAA,GAAA,OAAA,CAAA,OAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,OAAA,CAAA,cAAA,EAAA;AACA,IAAA,OAAA,CAAA,QAAA,GAAA,SAAA,CAAA;AACA,GAAA,MAAA,IAAA,OAAA,OAAA,CAAA,QAAA,KAAA,QAAA,EAAA;AACA,IAAA,OAAA,CAAA,QAAA,GAAA,OAAA,CAAA,QAAA,CAAA;AACA,GAAA,MAAA;AACA,IAAA,MAAA,QAAA,GAAA,OAAA,CAAA,SAAA,GAAA,OAAA,CAAA,OAAA,CAAA;AACA,IAAA,OAAA,CAAA,QAAA,GAAA,QAAA,IAAA,CAAA,GAAA,QAAA,GAAA,CAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,OAAA,CAAA,OAAA,EAAA;AACA,IAAA,OAAA,CAAA,OAAA,GAAA,OAAA,CAAA,OAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,OAAA,CAAA,WAAA,EAAA;AACA,IAAA,OAAA,CAAA,WAAA,GAAA,OAAA,CAAA,WAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,CAAA,OAAA,CAAA,SAAA,IAAA,OAAA,CAAA,SAAA,EAAA;AACA,IAAA,OAAA,CAAA,SAAA,GAAA,OAAA,CAAA,SAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,CAAA,OAAA,CAAA,SAAA,IAAA,OAAA,CAAA,SAAA,EAAA;AACA,IAAA,OAAA,CAAA,SAAA,GAAA,OAAA,CAAA,SAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,OAAA,OAAA,CAAA,MAAA,KAAA,QAAA,EAAA;AACA,IAAA,OAAA,CAAA,MAAA,GAAA,OAAA,CAAA,MAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA;AACA,IAAA,OAAA,CAAA,MAAA,GAAA,OAAA,CAAA,MAAA,CAAA;AACA,GAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,YAAA,CAAA,OAAA,EAAA,MAAA,EAAA;AACA,EAAA,IAAA,OAAA,GAAA,EAAA,CAAA;AACA,EAAA,IAAA,MAAA,EAAA;AACA,IAAA,OAAA,GAAA,EAAA,MAAA,EAAA,CAAA;AACA,GAAA,MAAA,IAAA,OAAA,CAAA,MAAA,KAAA,IAAA,EAAA;AACA,IAAA,OAAA,GAAA,EAAA,MAAA,EAAA,QAAA,EAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,aAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,aAAA,CAAA,OAAA,EAAA;AACA,EAAA,OAAAC,uBAAA,CAAA;AACA,IAAA,GAAA,EAAA,CAAA,EAAA,OAAA,CAAA,GAAA,CAAA,CAAA;AACA,IAAA,IAAA,EAAA,OAAA,CAAA,IAAA;AACA;AACA,IAAA,OAAA,EAAA,IAAA,IAAA,CAAA,OAAA,CAAA,OAAA,GAAA,IAAA,CAAA,CAAA,WAAA,EAAA;AACA,IAAA,SAAA,EAAA,IAAA,IAAA,CAAA,OAAA,CAAA,SAAA,GAAA,IAAA,CAAA,CAAA,WAAA,EAAA;AACA,IAAA,MAAA,EAAA,OAAA,CAAA,MAAA;AACA,IAAA,MAAA,EAAA,OAAA,CAAA,MAAA;AACA,IAAA,GAAA,EAAA,OAAA,OAAA,CAAA,GAAA,KAAA,QAAA,IAAA,OAAA,OAAA,CAAA,GAAA,KAAA,QAAA,GAAA,CAAA,EAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAA;AACA,IAAA,QAAA,EAAA,OAAA,CAAA,QAAA;AACA,IAAA,kBAAA,EAAA,OAAA,CAAA,kBAAA;AACA,IAAA,KAAA,EAAA;AACA,MAAA,OAAA,EAAA,OAAA,CAAA,OAAA;AACA,MAAA,WAAA,EAAA,OAAA,CAAA,WAAA;AACA,MAAA,UAAA,EAAA,OAAA,CAAA,SAAA;AACA,MAAA,UAAA,EAAA,OAAA,CAAA,SAAA;AACA,KAAA;AACA,GAAA,CAAA,CAAA;AACA;;;;;;"}