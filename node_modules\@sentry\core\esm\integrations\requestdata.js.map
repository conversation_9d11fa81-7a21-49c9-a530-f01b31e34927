{"version": 3, "file": "requestdata.js", "sources": ["../../../src/integrations/requestdata.ts"], "sourcesContent": ["import type {\n  Client,\n  Event,\n  EventHint,\n  Integration,\n  IntegrationClass,\n  IntegrationFn,\n  Transaction,\n} from '@sentry/types';\nimport type { AddRequestDataToEventOptions, TransactionNamingScheme } from '@sentry/utils';\nimport { addRequestDataToEvent, extractPathForTransaction } from '@sentry/utils';\nimport { convertIntegrationFnToClass, defineIntegration } from '../integration';\nimport { spanToJSON } from '../utils/spanUtils';\n\nexport type RequestDataIntegrationOptions = {\n  /**\n   * Controls what data is pulled from the request and added to the event\n   */\n  include?: {\n    cookies?: boolean;\n    data?: boolean;\n    headers?: boolean;\n    ip?: boolean;\n    query_string?: boolean;\n    url?: boolean;\n    user?:\n      | boolean\n      | {\n          id?: boolean;\n          username?: boolean;\n          email?: boolean;\n        };\n  };\n\n  /** Whether to identify transactions by parameterized path, parameterized path with method, or handler name */\n  transactionNamingScheme?: TransactionNamingScheme;\n};\n\nconst DEFAULT_OPTIONS = {\n  include: {\n    cookies: true,\n    data: true,\n    headers: true,\n    ip: false,\n    query_string: true,\n    url: true,\n    user: {\n      id: true,\n      username: true,\n      email: true,\n    },\n  },\n  transactionNamingScheme: 'methodPath',\n};\n\nconst INTEGRATION_NAME = 'RequestData';\n\nconst _requestDataIntegration = ((options: RequestDataIntegrationOptions = {}) => {\n  const _addRequestData = addRequestDataToEvent;\n  const _options: Required<RequestDataIntegrationOptions> = {\n    ...DEFAULT_OPTIONS,\n    ...options,\n    include: {\n      // @ts-expect-error It's mad because `method` isn't a known `include` key. (It's only here and not set by default in\n      // `addRequestDataToEvent` for legacy reasons. TODO (v8): Change that.)\n      method: true,\n      ...DEFAULT_OPTIONS.include,\n      ...options.include,\n      user:\n        options.include && typeof options.include.user === 'boolean'\n          ? options.include.user\n          : {\n              ...DEFAULT_OPTIONS.include.user,\n              // Unclear why TS still thinks `options.include.user` could be a boolean at this point\n              ...((options.include || {}).user as Record<string, boolean>),\n            },\n    },\n  };\n\n  return {\n    name: INTEGRATION_NAME,\n    // TODO v8: Remove this\n    setupOnce() {}, // eslint-disable-line @typescript-eslint/no-empty-function\n    processEvent(event, _hint, client) {\n      // Note: In the long run, most of the logic here should probably move into the request data utility functions. For\n      // the moment it lives here, though, until https://github.com/getsentry/sentry-javascript/issues/5718 is addressed.\n      // (TL;DR: Those functions touch many parts of the repo in many different ways, and need to be clened up. Once\n      // that's happened, it will be easier to add this logic in without worrying about unexpected side effects.)\n      const { transactionNamingScheme } = _options;\n\n      const { sdkProcessingMetadata = {} } = event;\n      const req = sdkProcessingMetadata.request;\n\n      if (!req) {\n        return event;\n      }\n\n      // The Express request handler takes a similar `include` option to that which can be passed to this integration.\n      // If passed there, we store it in `sdkProcessingMetadata`. TODO(v8): Force express and GCP people to use this\n      // integration, so that all of this passing and conversion isn't necessary\n      const addRequestDataOptions =\n        sdkProcessingMetadata.requestDataOptionsFromExpressHandler ||\n        sdkProcessingMetadata.requestDataOptionsFromGCPWrapper ||\n        convertReqDataIntegrationOptsToAddReqDataOpts(_options);\n\n      const processedEvent = _addRequestData(event, req, addRequestDataOptions);\n\n      // Transaction events already have the right `transaction` value\n      if (event.type === 'transaction' || transactionNamingScheme === 'handler') {\n        return processedEvent;\n      }\n\n      // In all other cases, use the request's associated transaction (if any) to overwrite the event's `transaction`\n      // value with a high-quality one\n      const reqWithTransaction = req as { _sentryTransaction?: Transaction };\n      const transaction = reqWithTransaction._sentryTransaction;\n      if (transaction) {\n        const name = spanToJSON(transaction).description || '';\n\n        // TODO (v8): Remove the nextjs check and just base it on `transactionNamingScheme` for all SDKs. (We have to\n        // keep it the way it is for the moment, because changing the names of transactions in Sentry has the potential\n        // to break things like alert rules.)\n        const shouldIncludeMethodInTransactionName =\n          getSDKName(client) === 'sentry.javascript.nextjs'\n            ? name.startsWith('/api')\n            : transactionNamingScheme !== 'path';\n\n        const [transactionValue] = extractPathForTransaction(req, {\n          path: true,\n          method: shouldIncludeMethodInTransactionName,\n          customRoute: name,\n        });\n\n        processedEvent.transaction = transactionValue;\n      }\n\n      return processedEvent;\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const requestDataIntegration = defineIntegration(_requestDataIntegration);\n\n/**\n * Add data about a request to an event. Primarily for use in Node-based SDKs, but included in `@sentry/integrations`\n * so it can be used in cross-platform SDKs like `@sentry/nextjs`.\n * @deprecated Use `requestDataIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const RequestData = convertIntegrationFnToClass(INTEGRATION_NAME, requestDataIntegration) as IntegrationClass<\n  Integration & { processEvent: (event: Event, hint: EventHint, client: Client) => Event }\n> & {\n  new (options?: {\n    /**\n     * Controls what data is pulled from the request and added to the event\n     */\n    include?: {\n      cookies?: boolean;\n      data?: boolean;\n      headers?: boolean;\n      ip?: boolean;\n      query_string?: boolean;\n      url?: boolean;\n      user?:\n        | boolean\n        | {\n            id?: boolean;\n            username?: boolean;\n            email?: boolean;\n          };\n    };\n\n    /** Whether to identify transactions by parameterized path, parameterized path with method, or handler name */\n    transactionNamingScheme?: TransactionNamingScheme;\n  }): Integration;\n};\n\n/** Convert this integration's options to match what `addRequestDataToEvent` expects */\n/** TODO: Can possibly be deleted once https://github.com/getsentry/sentry-javascript/issues/5718 is fixed */\nfunction convertReqDataIntegrationOptsToAddReqDataOpts(\n  integrationOptions: Required<RequestDataIntegrationOptions>,\n): AddRequestDataToEventOptions {\n  const {\n    transactionNamingScheme,\n    include: { ip, user, ...requestOptions },\n  } = integrationOptions;\n\n  const requestIncludeKeys: string[] = [];\n  for (const [key, value] of Object.entries(requestOptions)) {\n    if (value) {\n      requestIncludeKeys.push(key);\n    }\n  }\n\n  let addReqDataUserOpt;\n  if (user === undefined) {\n    addReqDataUserOpt = true;\n  } else if (typeof user === 'boolean') {\n    addReqDataUserOpt = user;\n  } else {\n    const userIncludeKeys: string[] = [];\n    for (const [key, value] of Object.entries(user)) {\n      if (value) {\n        userIncludeKeys.push(key);\n      }\n    }\n    addReqDataUserOpt = userIncludeKeys;\n  }\n\n  return {\n    include: {\n      ip,\n      user: addReqDataUserOpt,\n      request: requestIncludeKeys.length !== 0 ? requestIncludeKeys : undefined,\n      transaction: transactionNamingScheme,\n    },\n  };\n}\n\nfunction getSDKName(client: Client): string | undefined {\n  try {\n    // For a long chain like this, it's fewer bytes to combine a try-catch with assuming everything is there than to\n    // write out a long chain of `a && a.b && a.b.c && ...`\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    return client.getOptions()._metadata!.sdk!.name;\n  } catch (err) {\n    // In theory we should never get here\n    return undefined;\n  }\n}\n"], "names": [], "mappings": ";;;;AAsCA,MAAM,kBAAkB;AACxB,EAAE,OAAO,EAAE;AACX,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,EAAE,EAAE,KAAK;AACb,IAAI,YAAY,EAAE,IAAI;AACtB,IAAI,GAAG,EAAE,IAAI;AACb,IAAI,IAAI,EAAE;AACV,MAAM,EAAE,EAAE,IAAI;AACd,MAAM,QAAQ,EAAE,IAAI;AACpB,MAAM,KAAK,EAAE,IAAI;AACjB,KAAK;AACL,GAAG;AACH,EAAE,uBAAuB,EAAE,YAAY;AACvC,CAAC,CAAA;AACD;AACA,MAAM,gBAAA,GAAmB,aAAa,CAAA;AACtC;AACA,MAAM,uBAAA,IAA2B,CAAC,OAAO,GAAkC,EAAE,KAAK;AAClF,EAAE,MAAM,eAAgB,GAAE,qBAAqB,CAAA;AAC/C,EAAE,MAAM,QAAQ,GAA4C;AAC5D,IAAI,GAAG,eAAe;AACtB,IAAI,GAAG,OAAO;AACd,IAAI,OAAO,EAAE;AACb;AACA;AACA,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,GAAG,eAAe,CAAC,OAAO;AAChC,MAAM,GAAG,OAAO,CAAC,OAAO;AACxB,MAAM,IAAI;AACV,QAAQ,OAAO,CAAC,OAAA,IAAW,OAAO,OAAO,CAAC,OAAO,CAAC,IAAA,KAAS,SAAA;AAC3D,YAAY,OAAO,CAAC,OAAO,CAAC,IAAA;AAC5B,YAAY;AACZ,cAAc,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI;AAC7C;AACA,cAAc,IAAI,CAAC,OAAO,CAAC,OAAA,IAAW,EAAE,EAAE,IAAA,EAAgC;AAC1E,aAAa;AACb,KAAK;AACL,GAAG,CAAA;AACH;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B;AACA,IAAI,SAAS,GAAG,EAAE;AAClB,IAAI,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE;AACvC;AACA;AACA;AACA;AACA,MAAM,MAAM,EAAE,uBAAwB,EAAA,GAAI,QAAQ,CAAA;AAClD;AACA,MAAM,MAAM,EAAE,qBAAsB,GAAE,EAAG,EAAA,GAAI,KAAK,CAAA;AAClD,MAAM,MAAM,GAAA,GAAM,qBAAqB,CAAC,OAAO,CAAA;AAC/C;AACA,MAAM,IAAI,CAAC,GAAG,EAAE;AAChB,QAAQ,OAAO,KAAK,CAAA;AACpB,OAAM;AACN;AACA;AACA;AACA;AACA,MAAM,MAAM,qBAAsB;AAClC,QAAQ,qBAAqB,CAAC,oCAAqC;AACnE,QAAQ,qBAAqB,CAAC,gCAAiC;AAC/D,QAAQ,6CAA6C,CAAC,QAAQ,CAAC,CAAA;AAC/D;AACA,MAAM,MAAM,cAAe,GAAE,eAAe,CAAC,KAAK,EAAE,GAAG,EAAE,qBAAqB,CAAC,CAAA;AAC/E;AACA;AACA,MAAM,IAAI,KAAK,CAAC,IAAA,KAAS,aAAA,IAAiB,uBAAA,KAA4B,SAAS,EAAE;AACjF,QAAQ,OAAO,cAAc,CAAA;AAC7B,OAAM;AACN;AACA;AACA;AACA,MAAM,MAAM,kBAAmB,GAAE,GAAI,EAAA;AACrC,MAAM,MAAM,WAAA,GAAc,kBAAkB,CAAC,kBAAkB,CAAA;AAC/D,MAAM,IAAI,WAAW,EAAE;AACvB,QAAQ,MAAM,IAAK,GAAE,UAAU,CAAC,WAAW,CAAC,CAAC,WAAY,IAAG,EAAE,CAAA;AAC9D;AACA;AACA;AACA;AACA,QAAQ,MAAM,oCAAqC;AACnD,UAAU,UAAU,CAAC,MAAM,CAAA,KAAM,0BAAA;AACjC,cAAc,IAAI,CAAC,UAAU,CAAC,MAAM,CAAA;AACpC,cAAc,uBAAwB,KAAI,MAAM,CAAA;AAChD;AACA,QAAQ,MAAM,CAAC,gBAAgB,CAAA,GAAI,yBAAyB,CAAC,GAAG,EAAE;AAClE,UAAU,IAAI,EAAE,IAAI;AACpB,UAAU,MAAM,EAAE,oCAAoC;AACtD,UAAU,WAAW,EAAE,IAAI;AAC3B,SAAS,CAAC,CAAA;AACV;AACA,QAAQ,cAAc,CAAC,WAAY,GAAE,gBAAgB,CAAA;AACrD,OAAM;AACN;AACA,MAAM,OAAO,cAAc,CAAA;AAC3B,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;MACa,sBAAuB,GAAE,iBAAiB,CAAC,uBAAuB,EAAC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,cAAc,2BAA2B,CAAC,gBAAgB,EAAE,sBAAsB,CAAE;;CA0BjG;AACA;AACA;AACA;AACA,SAAS,6CAA6C;AACtD,EAAE,kBAAkB;AACpB,EAAgC;AAChC,EAAE,MAAM;AACR,IAAI,uBAAuB;AAC3B,IAAI,OAAO,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,cAAA,EAAgB;AAC5C,GAAE,GAAI,kBAAkB,CAAA;AACxB;AACA,EAAE,MAAM,kBAAkB,GAAa,EAAE,CAAA;AACzC,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAE,IAAG,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;AAC7D,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AAClC,KAAI;AACJ,GAAE;AACF;AACA,EAAE,IAAI,iBAAiB,CAAA;AACvB,EAAE,IAAI,IAAK,KAAI,SAAS,EAAE;AAC1B,IAAI,iBAAA,GAAoB,IAAI,CAAA;AAC5B,GAAE,MAAO,IAAI,OAAO,IAAK,KAAI,SAAS,EAAE;AACxC,IAAI,iBAAA,GAAoB,IAAI,CAAA;AAC5B,SAAS;AACT,IAAI,MAAM,eAAe,GAAa,EAAE,CAAA;AACxC,IAAI,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAE,IAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACrD,MAAM,IAAI,KAAK,EAAE;AACjB,QAAQ,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AACjC,OAAM;AACN,KAAI;AACJ,IAAI,iBAAA,GAAoB,eAAe,CAAA;AACvC,GAAE;AACF;AACA,EAAE,OAAO;AACT,IAAI,OAAO,EAAE;AACb,MAAM,EAAE;AACR,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,OAAO,EAAE,kBAAkB,CAAC,MAAA,KAAW,CAAE,GAAE,kBAAmB,GAAE,SAAS;AAC/E,MAAM,WAAW,EAAE,uBAAuB;AAC1C,KAAK;AACL,GAAG,CAAA;AACH,CAAA;AACA;AACA,SAAS,UAAU,CAAC,MAAM,EAA8B;AACxD,EAAE,IAAI;AACN;AACA;AACA;AACA,IAAI,OAAO,MAAM,CAAC,UAAU,EAAE,CAAC,SAAS,CAAE,GAAG,CAAE,IAAI,CAAA;AACnD,GAAI,CAAA,OAAO,GAAG,EAAE;AAChB;AACA,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;;;;"}