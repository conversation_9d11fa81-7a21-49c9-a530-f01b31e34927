{"version": 3, "file": "metric-summary.js", "sources": ["../../../src/metrics/metric-summary.ts"], "sourcesContent": ["import type { MeasurementUnit, Span } from '@sentry/types';\nimport type { MetricSummary } from '@sentry/types';\nimport type { Primitive } from '@sentry/types';\nimport { dropUndefinedKeys } from '@sentry/utils';\nimport { getActiveSpan } from '../tracing';\nimport type { MetricType } from './types';\n\n/**\n * key: bucketKey\n * value: [exportKey, MetricSummary]\n */\ntype MetricSummaryStorage = Map<string, [string, MetricSummary]>;\n\nlet SPAN_METRIC_SUMMARY: WeakMap<Span, MetricSummaryStorage> | undefined;\n\nfunction getMetricStorageForSpan(span: Span): MetricSummaryStorage | undefined {\n  return SPAN_METRIC_SUMMARY ? SPAN_METRIC_SUMMARY.get(span) : undefined;\n}\n\n/**\n * Fetches the metric summary if it exists for the passed span\n */\nexport function getMetricSummaryJsonForSpan(span: Span): Record<string, Array<MetricSummary>> | undefined {\n  const storage = getMetricStorageForSpan(span);\n\n  if (!storage) {\n    return undefined;\n  }\n  const output: Record<string, Array<MetricSummary>> = {};\n\n  for (const [, [exportKey, summary]] of storage) {\n    if (!output[exportKey]) {\n      output[exportKey] = [];\n    }\n\n    output[exportKey].push(dropUndefinedKeys(summary));\n  }\n\n  return output;\n}\n\n/**\n * Updates the metric summary on the currently active span\n */\nexport function updateMetricSummaryOnActiveSpan(\n  metricType: MetricType,\n  sanitizedName: string,\n  value: number,\n  unit: MeasurementUnit,\n  tags: Record<string, Primitive>,\n  bucketKey: string,\n): void {\n  const span = getActiveSpan();\n  if (span) {\n    const storage = getMetricStorageForSpan(span) || new Map<string, [string, MetricSummary]>();\n\n    const exportKey = `${metricType}:${sanitizedName}@${unit}`;\n    const bucketItem = storage.get(bucketKey);\n\n    if (bucketItem) {\n      const [, summary] = bucketItem;\n      storage.set(bucketKey, [\n        exportKey,\n        {\n          min: Math.min(summary.min, value),\n          max: Math.max(summary.max, value),\n          count: (summary.count += 1),\n          sum: (summary.sum += value),\n          tags: summary.tags,\n        },\n      ]);\n    } else {\n      storage.set(bucketKey, [\n        exportKey,\n        {\n          min: value,\n          max: value,\n          count: 1,\n          sum: value,\n          tags,\n        },\n      ]);\n    }\n\n    if (!SPAN_METRIC_SUMMARY) {\n      SPAN_METRIC_SUMMARY = new WeakMap();\n    }\n\n    SPAN_METRIC_SUMMARY.set(span, storage);\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAOA;AACA;AACA;AACA;;AAGA,IAAI,mBAAmB,CAAA;AACvB;AACA,SAAS,uBAAuB,CAAC,IAAI,EAA0C;AAC/E,EAAE,OAAO,mBAAoB,GAAE,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAE,GAAE,SAAS,CAAA;AACxE,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,2BAA2B,CAAC,IAAI,EAA0D;AAC1G,EAAE,MAAM,OAAQ,GAAE,uBAAuB,CAAC,IAAI,CAAC,CAAA;AAC/C;AACA,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF,EAAE,MAAM,MAAM,GAAyC,EAAE,CAAA;AACzD;AACA,EAAE,KAAK,MAAM,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAE,IAAG,OAAO,EAAE;AAClD,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;AAC5B,MAAM,MAAM,CAAC,SAAS,CAAE,GAAE,EAAE,CAAA;AAC5B,KAAI;AACJ;AACA,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAA;AACtD,GAAE;AACF;AACA,EAAE,OAAO,MAAM,CAAA;AACf,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,+BAA+B;AAC/C,EAAE,UAAU;AACZ,EAAE,aAAa;AACf,EAAE,KAAK;AACP,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,SAAS;AACX,EAAQ;AACR,EAAE,MAAM,IAAA,GAAO,aAAa,EAAE,CAAA;AAC9B,EAAE,IAAI,IAAI,EAAE;AACZ,IAAI,MAAM,OAAQ,GAAE,uBAAuB,CAAC,IAAI,CAAA,IAAK,IAAI,GAAG,EAAmC,CAAA;AAC/F;AACA,IAAI,MAAM,SAAA,GAAY,CAAC,EAAA,UAAA,CAAA,CAAA,EAAA,aAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AACA,IAAA,MAAA,UAAA,GAAA,OAAA,CAAA,GAAA,CAAA,SAAA,CAAA,CAAA;AACA;AACA,IAAA,IAAA,UAAA,EAAA;AACA,MAAA,MAAA,GAAA,OAAA,CAAA,GAAA,UAAA,CAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA;AACA,QAAA,SAAA;AACA,QAAA;AACA,UAAA,GAAA,EAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,EAAA,KAAA,CAAA;AACA,UAAA,GAAA,EAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,EAAA,KAAA,CAAA;AACA,UAAA,KAAA,GAAA,OAAA,CAAA,KAAA,IAAA,CAAA,CAAA;AACA,UAAA,GAAA,GAAA,OAAA,CAAA,GAAA,IAAA,KAAA,CAAA;AACA,UAAA,IAAA,EAAA,OAAA,CAAA,IAAA;AACA,SAAA;AACA,OAAA,CAAA,CAAA;AACA,KAAA,MAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA;AACA,QAAA,SAAA;AACA,QAAA;AACA,UAAA,GAAA,EAAA,KAAA;AACA,UAAA,GAAA,EAAA,KAAA;AACA,UAAA,KAAA,EAAA,CAAA;AACA,UAAA,GAAA,EAAA,KAAA;AACA,UAAA,IAAA;AACA,SAAA;AACA,OAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,CAAA,mBAAA,EAAA;AACA,MAAA,mBAAA,GAAA,IAAA,OAAA,EAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,mBAAA,CAAA,GAAA,CAAA,IAAA,EAAA,OAAA,CAAA,CAAA;AACA,GAAA;AACA;;;;"}