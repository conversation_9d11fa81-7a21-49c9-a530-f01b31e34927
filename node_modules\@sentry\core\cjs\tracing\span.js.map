{"version": 3, "file": "span.js", "sources": ["../../../src/tracing/span.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport type {\n  Instrumenter,\n  Measurements,\n  Primitive,\n  Span as SpanInterface,\n  SpanAttributeValue,\n  SpanAttributes,\n  SpanContext,\n  SpanContextData,\n  SpanJSON,\n  SpanOrigin,\n  SpanTimeInput,\n  TraceContext,\n  Transaction,\n} from '@sentry/types';\nimport { dropUndefinedKeys, logger, timestampInSeconds, uuid4 } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../debug-build';\nimport { getMetricSummaryJsonForSpan } from '../metrics/metric-summary';\nimport {\n  SEMANTIC_ATTRIBUTE_PROFILE_ID,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n} from '../semanticAttributes';\nimport { getRootSpan } from '../utils/getRootSpan';\nimport {\n  TRACE_FLAG_NONE,\n  TRACE_FLAG_SAMPLED,\n  spanTimeInputToSeconds,\n  spanToJSON,\n  spanToTraceContext,\n  spanToTraceHeader,\n} from '../utils/spanUtils';\nimport type { SpanStatusType } from './spanstatus';\nimport { setHttpStatus } from './spanstatus';\n\n/**\n * Keeps track of finished spans for a given transaction\n * @internal\n * @hideconstructor\n * @hidden\n */\nexport class SpanRecorder {\n  public spans: Span[];\n\n  private readonly _maxlen: number;\n\n  public constructor(maxlen: number = 1000) {\n    this._maxlen = maxlen;\n    this.spans = [];\n  }\n\n  /**\n   * This is just so that we don't run out of memory while recording a lot\n   * of spans. At some point we just stop and flush out the start of the\n   * trace tree (i.e.the first n spans with the smallest\n   * start_timestamp).\n   */\n  public add(span: Span): void {\n    if (this.spans.length > this._maxlen) {\n      // eslint-disable-next-line deprecation/deprecation\n      span.spanRecorder = undefined;\n    } else {\n      this.spans.push(span);\n    }\n  }\n}\n\n/**\n * Span contains all data about a span\n */\nexport class Span implements SpanInterface {\n  /**\n   * Tags for the span.\n   * @deprecated Use `spanToJSON(span).atttributes` instead.\n   */\n  public tags: { [key: string]: Primitive };\n\n  /**\n   * Data for the span.\n   * @deprecated Use `spanToJSON(span).atttributes` instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  public data: { [key: string]: any };\n\n  /**\n   * List of spans that were finalized\n   *\n   * @deprecated This property will no longer be public. Span recording will be handled internally.\n   */\n  public spanRecorder?: SpanRecorder;\n\n  /**\n   * @inheritDoc\n   * @deprecated Use top level `Sentry.getRootSpan()` instead\n   */\n  public transaction?: Transaction;\n\n  /**\n   * The instrumenter that created this span.\n   *\n   * TODO (v8): This can probably be replaced by an `instanceOf` check of the span class.\n   *            the instrumenter can only be sentry or otel so we can check the span instance\n   *            to verify which one it is and remove this field entirely.\n   *\n   * @deprecated This field will be removed.\n   */\n  public instrumenter: Instrumenter;\n\n  protected _traceId: string;\n  protected _spanId: string;\n  protected _parentSpanId?: string | undefined;\n  protected _sampled: boolean | undefined;\n  protected _name?: string | undefined;\n  protected _attributes: SpanAttributes;\n  /** Epoch timestamp in seconds when the span started. */\n  protected _startTime: number;\n  /** Epoch timestamp in seconds when the span ended. */\n  protected _endTime?: number | undefined;\n  /** Internal keeper of the status */\n  protected _status?: SpanStatusType | string | undefined;\n  protected _exclusiveTime?: number;\n\n  protected _measurements: Measurements;\n\n  private _logMessage?: string;\n\n  /**\n   * You should never call the constructor manually, always use `Sentry.startTransaction()`\n   * or call `startChild()` on an existing span.\n   * @internal\n   * @hideconstructor\n   * @hidden\n   */\n  public constructor(spanContext: SpanContext = {}) {\n    this._traceId = spanContext.traceId || uuid4();\n    this._spanId = spanContext.spanId || uuid4().substring(16);\n    this._startTime = spanContext.startTimestamp || timestampInSeconds();\n    // eslint-disable-next-line deprecation/deprecation\n    this.tags = spanContext.tags ? { ...spanContext.tags } : {};\n    // eslint-disable-next-line deprecation/deprecation\n    this.data = spanContext.data ? { ...spanContext.data } : {};\n    // eslint-disable-next-line deprecation/deprecation\n    this.instrumenter = spanContext.instrumenter || 'sentry';\n\n    this._attributes = {};\n    this.setAttributes({\n      [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: spanContext.origin || 'manual',\n      [SEMANTIC_ATTRIBUTE_SENTRY_OP]: spanContext.op,\n      ...spanContext.attributes,\n    });\n\n    // eslint-disable-next-line deprecation/deprecation\n    this._name = spanContext.name || spanContext.description;\n\n    if (spanContext.parentSpanId) {\n      this._parentSpanId = spanContext.parentSpanId;\n    }\n    // We want to include booleans as well here\n    if ('sampled' in spanContext) {\n      this._sampled = spanContext.sampled;\n    }\n    if (spanContext.status) {\n      this._status = spanContext.status;\n    }\n    if (spanContext.endTimestamp) {\n      this._endTime = spanContext.endTimestamp;\n    }\n    if (spanContext.exclusiveTime !== undefined) {\n      this._exclusiveTime = spanContext.exclusiveTime;\n    }\n    this._measurements = spanContext.measurements ? { ...spanContext.measurements } : {};\n  }\n\n  // This rule conflicts with another eslint rule :(\n  /* eslint-disable @typescript-eslint/member-ordering */\n\n  /**\n   * An alias for `description` of the Span.\n   * @deprecated Use `spanToJSON(span).description` instead.\n   */\n  public get name(): string {\n    return this._name || '';\n  }\n\n  /**\n   * Update the name of the span.\n   * @deprecated Use `spanToJSON(span).description` instead.\n   */\n  public set name(name: string) {\n    this.updateName(name);\n  }\n\n  /**\n   * Get the description of the Span.\n   * @deprecated Use `spanToJSON(span).description` instead.\n   */\n  public get description(): string | undefined {\n    return this._name;\n  }\n\n  /**\n   * Get the description of the Span.\n   * @deprecated Use `spanToJSON(span).description` instead.\n   */\n  public set description(description: string | undefined) {\n    this._name = description;\n  }\n\n  /**\n   * The ID of the trace.\n   * @deprecated Use `spanContext().traceId` instead.\n   */\n  public get traceId(): string {\n    return this._traceId;\n  }\n\n  /**\n   * The ID of the trace.\n   * @deprecated You cannot update the traceId of a span after span creation.\n   */\n  public set traceId(traceId: string) {\n    this._traceId = traceId;\n  }\n\n  /**\n   * The ID of the span.\n   * @deprecated Use `spanContext().spanId` instead.\n   */\n  public get spanId(): string {\n    return this._spanId;\n  }\n\n  /**\n   * The ID of the span.\n   * @deprecated You cannot update the spanId of a span after span creation.\n   */\n  public set spanId(spanId: string) {\n    this._spanId = spanId;\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use `startSpan` functions instead.\n   */\n  public set parentSpanId(string) {\n    this._parentSpanId = string;\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use `spanToJSON(span).parent_span_id` instead.\n   */\n  public get parentSpanId(): string | undefined {\n    return this._parentSpanId;\n  }\n\n  /**\n   * Was this span chosen to be sent as part of the sample?\n   * @deprecated Use `isRecording()` instead.\n   */\n  public get sampled(): boolean | undefined {\n    return this._sampled;\n  }\n\n  /**\n   * Was this span chosen to be sent as part of the sample?\n   * @deprecated You cannot update the sampling decision of a span after span creation.\n   */\n  public set sampled(sampled: boolean | undefined) {\n    this._sampled = sampled;\n  }\n\n  /**\n   * Attributes for the span.\n   * @deprecated Use `spanToJSON(span).atttributes` instead.\n   */\n  public get attributes(): SpanAttributes {\n    return this._attributes;\n  }\n\n  /**\n   * Attributes for the span.\n   * @deprecated Use `setAttributes()` instead.\n   */\n  public set attributes(attributes: SpanAttributes) {\n    this._attributes = attributes;\n  }\n\n  /**\n   * Timestamp in seconds (epoch time) indicating when the span started.\n   * @deprecated Use `spanToJSON()` instead.\n   */\n  public get startTimestamp(): number {\n    return this._startTime;\n  }\n\n  /**\n   * Timestamp in seconds (epoch time) indicating when the span started.\n   * @deprecated In v8, you will not be able to update the span start time after creation.\n   */\n  public set startTimestamp(startTime: number) {\n    this._startTime = startTime;\n  }\n\n  /**\n   * Timestamp in seconds when the span ended.\n   * @deprecated Use `spanToJSON()` instead.\n   */\n  public get endTimestamp(): number | undefined {\n    return this._endTime;\n  }\n\n  /**\n   * Timestamp in seconds when the span ended.\n   * @deprecated Set the end time via `span.end()` instead.\n   */\n  public set endTimestamp(endTime: number | undefined) {\n    this._endTime = endTime;\n  }\n\n  /**\n   * The status of the span.\n   *\n   * @deprecated Use `spanToJSON().status` instead to get the status.\n   */\n  public get status(): SpanStatusType | string | undefined {\n    return this._status;\n  }\n\n  /**\n   * The status of the span.\n   *\n   * @deprecated Use `.setStatus()` instead to set or update the status.\n   */\n  public set status(status: SpanStatusType | string | undefined) {\n    this._status = status;\n  }\n\n  /**\n   * Operation of the span\n   *\n   * @deprecated Use `spanToJSON().op` to read the op instead.\n   */\n  public get op(): string | undefined {\n    return this._attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP] as string | undefined;\n  }\n\n  /**\n   * Operation of the span\n   *\n   * @deprecated Use `startSpan()` functions to set or `span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'op')\n   *             to update the span instead.\n   */\n  public set op(op: string | undefined) {\n    this.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, op);\n  }\n\n  /**\n   * The origin of the span, giving context about what created the span.\n   *\n   * @deprecated Use `spanToJSON().origin` to read the origin instead.\n   */\n  public get origin(): SpanOrigin | undefined {\n    return this._attributes[SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN] as SpanOrigin | undefined;\n  }\n\n  /**\n   * The origin of the span, giving context about what created the span.\n   *\n   * @deprecated Use `startSpan()` functions to set the origin instead.\n   */\n  public set origin(origin: SpanOrigin | undefined) {\n    this.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, origin);\n  }\n\n  /* eslint-enable @typescript-eslint/member-ordering */\n\n  /** @inheritdoc */\n  public spanContext(): SpanContextData {\n    const { _spanId: spanId, _traceId: traceId, _sampled: sampled } = this;\n    return {\n      spanId,\n      traceId,\n      traceFlags: sampled ? TRACE_FLAG_SAMPLED : TRACE_FLAG_NONE,\n    };\n  }\n\n  /**\n   * Creates a new `Span` while setting the current `Span.id` as `parentSpanId`.\n   * Also the `sampled` decision will be inherited.\n   *\n   * @deprecated Use `startSpan()`, `startSpanManual()` or `startInactiveSpan()` instead.\n   */\n  public startChild(\n    spanContext?: Pick<SpanContext, Exclude<keyof SpanContext, 'sampled' | 'traceId' | 'parentSpanId'>>,\n  ): SpanInterface {\n    const childSpan = new Span({\n      ...spanContext,\n      parentSpanId: this._spanId,\n      sampled: this._sampled,\n      traceId: this._traceId,\n    });\n\n    // eslint-disable-next-line deprecation/deprecation\n    childSpan.spanRecorder = this.spanRecorder;\n    // eslint-disable-next-line deprecation/deprecation\n    if (childSpan.spanRecorder) {\n      // eslint-disable-next-line deprecation/deprecation\n      childSpan.spanRecorder.add(childSpan);\n    }\n\n    const rootSpan = getRootSpan(this);\n    // TODO: still set span.transaction here until we have a more permanent solution\n    // Probably similarly to the weakmap we hold in node-experimental\n    // eslint-disable-next-line deprecation/deprecation\n    childSpan.transaction = rootSpan as Transaction;\n\n    if (DEBUG_BUILD && rootSpan) {\n      const opStr = (spanContext && spanContext.op) || '< unknown op >';\n      const nameStr = spanToJSON(childSpan).description || '< unknown name >';\n      const idStr = rootSpan.spanContext().spanId;\n\n      const logMessage = `[Tracing] Starting '${opStr}' span on transaction '${nameStr}' (${idStr}).`;\n      logger.log(logMessage);\n      this._logMessage = logMessage;\n    }\n\n    return childSpan;\n  }\n\n  /**\n   * Sets the tag attribute on the current span.\n   *\n   * Can also be used to unset a tag, by passing `undefined`.\n   *\n   * @param key Tag key\n   * @param value Tag value\n   * @deprecated Use `setAttribute()` instead.\n   */\n  public setTag(key: string, value: Primitive): this {\n    // eslint-disable-next-line deprecation/deprecation\n    this.tags = { ...this.tags, [key]: value };\n    return this;\n  }\n\n  /**\n   * Sets the data attribute on the current span\n   * @param key Data key\n   * @param value Data value\n   * @deprecated Use `setAttribute()` instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  public setData(key: string, value: any): this {\n    // eslint-disable-next-line deprecation/deprecation\n    this.data = { ...this.data, [key]: value };\n    return this;\n  }\n\n  /** @inheritdoc */\n  public setAttribute(key: string, value: SpanAttributeValue | undefined): void {\n    if (value === undefined) {\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this._attributes[key];\n    } else {\n      this._attributes[key] = value;\n    }\n  }\n\n  /** @inheritdoc */\n  public setAttributes(attributes: SpanAttributes): void {\n    Object.keys(attributes).forEach(key => this.setAttribute(key, attributes[key]));\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setStatus(value: SpanStatusType): this {\n    this._status = value;\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   * @deprecated Use top-level `setHttpStatus()` instead.\n   */\n  public setHttpStatus(httpStatus: number): this {\n    setHttpStatus(this, httpStatus);\n    return this;\n  }\n\n  /**\n   * @inheritdoc\n   *\n   * @deprecated Use `.updateName()` instead.\n   */\n  public setName(name: string): void {\n    this.updateName(name);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public updateName(name: string): this {\n    this._name = name;\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use `spanToJSON(span).status === 'ok'` instead.\n   */\n  public isSuccess(): boolean {\n    return this._status === 'ok';\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use `.end()` instead.\n   */\n  public finish(endTimestamp?: number): void {\n    return this.end(endTimestamp);\n  }\n\n  /** @inheritdoc */\n  public end(endTimestamp?: SpanTimeInput): void {\n    // If already ended, skip\n    if (this._endTime) {\n      return;\n    }\n    const rootSpan = getRootSpan(this);\n    if (\n      DEBUG_BUILD &&\n      // Don't call this for transactions\n      rootSpan &&\n      rootSpan.spanContext().spanId !== this._spanId\n    ) {\n      const logMessage = this._logMessage;\n      if (logMessage) {\n        logger.log((logMessage as string).replace('Starting', 'Finishing'));\n      }\n    }\n\n    this._endTime = spanTimeInputToSeconds(endTimestamp);\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use `spanToTraceHeader()` instead.\n   */\n  public toTraceparent(): string {\n    return spanToTraceHeader(this);\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use `spanToJSON()` or access the fields directly instead.\n   */\n  public toContext(): SpanContext {\n    return dropUndefinedKeys({\n      data: this._getData(),\n      description: this._name,\n      endTimestamp: this._endTime,\n      // eslint-disable-next-line deprecation/deprecation\n      op: this.op,\n      parentSpanId: this._parentSpanId,\n      sampled: this._sampled,\n      spanId: this._spanId,\n      startTimestamp: this._startTime,\n      status: this._status,\n      // eslint-disable-next-line deprecation/deprecation\n      tags: this.tags,\n      traceId: this._traceId,\n    });\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Update the fields directly instead.\n   */\n  public updateWithContext(spanContext: SpanContext): this {\n    // eslint-disable-next-line deprecation/deprecation\n    this.data = spanContext.data || {};\n    // eslint-disable-next-line deprecation/deprecation\n    this._name = spanContext.name || spanContext.description;\n    this._endTime = spanContext.endTimestamp;\n    // eslint-disable-next-line deprecation/deprecation\n    this.op = spanContext.op;\n    this._parentSpanId = spanContext.parentSpanId;\n    this._sampled = spanContext.sampled;\n    this._spanId = spanContext.spanId || this._spanId;\n    this._startTime = spanContext.startTimestamp || this._startTime;\n    this._status = spanContext.status;\n    // eslint-disable-next-line deprecation/deprecation\n    this.tags = spanContext.tags || {};\n    this._traceId = spanContext.traceId || this._traceId;\n\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use `spanToTraceContext()` util function instead.\n   */\n  public getTraceContext(): TraceContext {\n    return spanToTraceContext(this);\n  }\n\n  /**\n   * Get JSON representation of this span.\n   *\n   * @hidden\n   * @internal This method is purely for internal purposes and should not be used outside\n   * of SDK code. If you need to get a JSON representation of a span,\n   * use `spanToJSON(span)` instead.\n   */\n  public getSpanJSON(): SpanJSON {\n    return dropUndefinedKeys({\n      data: this._getData(),\n      description: this._name,\n      op: this._attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP] as string | undefined,\n      parent_span_id: this._parentSpanId,\n      span_id: this._spanId,\n      start_timestamp: this._startTime,\n      status: this._status,\n      // eslint-disable-next-line deprecation/deprecation\n      tags: Object.keys(this.tags).length > 0 ? this.tags : undefined,\n      timestamp: this._endTime,\n      trace_id: this._traceId,\n      origin: this._attributes[SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN] as SpanOrigin | undefined,\n      _metrics_summary: getMetricSummaryJsonForSpan(this),\n      profile_id: this._attributes[SEMANTIC_ATTRIBUTE_PROFILE_ID] as string | undefined,\n      exclusive_time: this._exclusiveTime,\n      measurements: Object.keys(this._measurements).length > 0 ? this._measurements : undefined,\n    });\n  }\n\n  /** @inheritdoc */\n  public isRecording(): boolean {\n    return !this._endTime && !!this._sampled;\n  }\n\n  /**\n   * Convert the object to JSON.\n   * @deprecated Use `spanToJSON(span)` instead.\n   */\n  public toJSON(): SpanJSON {\n    return this.getSpanJSON();\n  }\n\n  /**\n   * Get the merged data for this span.\n   * For now, this combines `data` and `attributes` together,\n   * until eventually we can ingest `attributes` directly.\n   */\n  private _getData():\n    | {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        [key: string]: any;\n      }\n    | undefined {\n    // eslint-disable-next-line deprecation/deprecation\n    const { data, _attributes: attributes } = this;\n\n    const hasData = Object.keys(data).length > 0;\n    const hasAttributes = Object.keys(attributes).length > 0;\n\n    if (!hasData && !hasAttributes) {\n      return undefined;\n    }\n\n    if (hasData && hasAttributes) {\n      return {\n        ...data,\n        ...attributes,\n      };\n    }\n\n    return hasData ? data : attributes;\n  }\n}\n"], "names": ["uuid4", "timestampInSeconds", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "TRACE_FLAG_SAMPLED", "TRACE_FLAG_NONE", "getRootSpan", "DEBUG_BUILD", "spanToJSON", "logger", "setHttpStatus", "spanTimeInputToSeconds", "spanToTraceHeader", "dropUndefinedKeys", "spanToTraceContext", "getMetricSummaryJsonForSpan", "SEMANTIC_ATTRIBUTE_PROFILE_ID"], "mappings": ";;;;;;;;;;AAqCA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,YAAa,CAAA;;AAK1B,GAAS,WAAW,CAAC,MAAM,GAAW,IAAI,EAAE;AAC5C,IAAI,IAAI,CAAC,OAAQ,GAAE,MAAM,CAAA;AACzB,IAAI,IAAI,CAAC,KAAM,GAAE,EAAE,CAAA;AACnB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,GAAG,CAAC,IAAI,EAAc;AAC/B,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAA,GAAS,IAAI,CAAC,OAAO,EAAE;AAC1C;AACA,MAAM,IAAI,CAAC,YAAa,GAAE,SAAS,CAAA;AACnC,WAAW;AACX,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AAC3B,KAAI;AACJ,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA;AACO,MAAM,MAA8B;AAC3C;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AASA;;AAEA;;AAEA;;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,WAAW,CAAC,WAAW,GAAgB,EAAE,EAAE;AACpD,IAAI,IAAI,CAAC,QAAA,GAAW,WAAW,CAAC,OAAQ,IAAGA,WAAK,EAAE,CAAA;AAClD,IAAI,IAAI,CAAC,OAAQ,GAAE,WAAW,CAAC,MAAA,IAAUA,WAAK,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;AAC9D,IAAI,IAAI,CAAC,UAAA,GAAa,WAAW,CAAC,cAAe,IAAGC,wBAAkB,EAAE,CAAA;AACxE;AACA,IAAI,IAAI,CAAC,IAAA,GAAO,WAAW,CAAC,IAAK,GAAE,EAAE,GAAG,WAAW,CAAC,IAAA,EAAO,GAAE,EAAE,CAAA;AAC/D;AACA,IAAI,IAAI,CAAC,IAAA,GAAO,WAAW,CAAC,IAAK,GAAE,EAAE,GAAG,WAAW,CAAC,IAAA,EAAO,GAAE,EAAE,CAAA;AAC/D;AACA,IAAI,IAAI,CAAC,YAAa,GAAE,WAAW,CAAC,YAAA,IAAgB,QAAQ,CAAA;AAC5D;AACA,IAAI,IAAI,CAAC,WAAY,GAAE,EAAE,CAAA;AACzB,IAAI,IAAI,CAAC,aAAa,CAAC;AACvB,MAAM,CAACC,mDAAgC,GAAG,WAAW,CAAC,MAAA,IAAU,QAAQ;AACxE,MAAM,CAACC,+CAA4B,GAAG,WAAW,CAAC,EAAE;AACpD,MAAM,GAAG,WAAW,CAAC,UAAU;AAC/B,KAAK,CAAC,CAAA;AACN;AACA;AACA,IAAI,IAAI,CAAC,KAAA,GAAQ,WAAW,CAAC,IAAK,IAAG,WAAW,CAAC,WAAW,CAAA;AAC5D;AACA,IAAI,IAAI,WAAW,CAAC,YAAY,EAAE;AAClC,MAAM,IAAI,CAAC,aAAA,GAAgB,WAAW,CAAC,YAAY,CAAA;AACnD,KAAI;AACJ;AACA,IAAI,IAAI,SAAU,IAAG,WAAW,EAAE;AAClC,MAAM,IAAI,CAAC,QAAA,GAAW,WAAW,CAAC,OAAO,CAAA;AACzC,KAAI;AACJ,IAAI,IAAI,WAAW,CAAC,MAAM,EAAE;AAC5B,MAAM,IAAI,CAAC,OAAA,GAAU,WAAW,CAAC,MAAM,CAAA;AACvC,KAAI;AACJ,IAAI,IAAI,WAAW,CAAC,YAAY,EAAE;AAClC,MAAM,IAAI,CAAC,QAAA,GAAW,WAAW,CAAC,YAAY,CAAA;AAC9C,KAAI;AACJ,IAAI,IAAI,WAAW,CAAC,aAAc,KAAI,SAAS,EAAE;AACjD,MAAM,IAAI,CAAC,cAAA,GAAiB,WAAW,CAAC,aAAa,CAAA;AACrD,KAAI;AACJ,IAAI,IAAI,CAAC,aAAA,GAAgB,WAAW,CAAC,YAAa,GAAE,EAAE,GAAG,WAAW,CAAC,YAAA,EAAe,GAAE,EAAE,CAAA;AACxF,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,IAAI,GAAW;AAC5B,IAAI,OAAO,IAAI,CAAC,KAAA,IAAS,EAAE,CAAA;AAC3B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,IAAI,CAAC,IAAI,EAAU;AAChC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;AACzB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,WAAW,GAAuB;AAC/C,IAAI,OAAO,IAAI,CAAC,KAAK,CAAA;AACrB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,WAAW,CAAC,WAAW,EAAsB;AAC1D,IAAI,IAAI,CAAC,KAAM,GAAE,WAAW,CAAA;AAC5B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,OAAO,GAAW;AAC/B,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAA;AACxB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,OAAO,CAAC,OAAO,EAAU;AACtC,IAAI,IAAI,CAAC,QAAS,GAAE,OAAO,CAAA;AAC3B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,MAAM,GAAW;AAC9B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAA;AACvB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,MAAM,CAAC,MAAM,EAAU;AACpC,IAAI,IAAI,CAAC,OAAQ,GAAE,MAAM,CAAA;AACzB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,YAAY,CAAC,MAAM,EAAE;AAClC,IAAI,IAAI,CAAC,aAAc,GAAE,MAAM,CAAA;AAC/B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,YAAY,GAAuB;AAChD,IAAI,OAAO,IAAI,CAAC,aAAa,CAAA;AAC7B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,OAAO,GAAwB;AAC5C,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAA;AACxB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,OAAO,CAAC,OAAO,EAAuB;AACnD,IAAI,IAAI,CAAC,QAAS,GAAE,OAAO,CAAA;AAC3B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,UAAU,GAAmB;AAC1C,IAAI,OAAO,IAAI,CAAC,WAAW,CAAA;AAC3B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,UAAU,CAAC,UAAU,EAAkB;AACpD,IAAI,IAAI,CAAC,WAAY,GAAE,UAAU,CAAA;AACjC,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,cAAc,GAAW;AACtC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAA;AAC1B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,cAAc,CAAC,SAAS,EAAU;AAC/C,IAAI,IAAI,CAAC,UAAW,GAAE,SAAS,CAAA;AAC/B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,YAAY,GAAuB;AAChD,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAA;AACxB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,YAAY,CAAC,OAAO,EAAsB;AACvD,IAAI,IAAI,CAAC,QAAS,GAAE,OAAO,CAAA;AAC3B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,MAAM,GAAwC;AAC3D,IAAI,OAAO,IAAI,CAAC,OAAO,CAAA;AACvB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,MAAM,CAAC,MAAM,EAAuC;AACjE,IAAI,IAAI,CAAC,OAAQ,GAAE,MAAM,CAAA;AACzB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,EAAE,GAAuB;AACtC,IAAI,OAAO,IAAI,CAAC,WAAW,CAACA,+CAA4B,CAAE,EAAA;AAC1D,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,EAAE,CAAC,EAAE,EAAsB;AACxC,IAAI,IAAI,CAAC,YAAY,CAACA,+CAA4B,EAAE,EAAE,CAAC,CAAA;AACvD,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,MAAM,GAA2B;AAC9C,IAAI,OAAO,IAAI,CAAC,WAAW,CAACD,mDAAgC,CAAE,EAAA;AAC9D,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,MAAM,CAAC,MAAM,EAA0B;AACpD,IAAI,IAAI,CAAC,YAAY,CAACA,mDAAgC,EAAE,MAAM,CAAC,CAAA;AAC/D,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,WAAW,GAAoB;AACxC,IAAI,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAQ,EAAA,GAAI,IAAI,CAAA;AAC1E,IAAI,OAAO;AACX,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,UAAU,EAAE,OAAA,GAAUE,4BAAA,GAAqBC,yBAAe;AAChE,KAAK,CAAA;AACL,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,UAAU;AACnB,IAAI,WAAW;AACf,IAAmB;AACnB,IAAI,MAAM,SAAA,GAAY,IAAI,IAAI,CAAC;AAC/B,MAAM,GAAG,WAAW;AACpB,MAAM,YAAY,EAAE,IAAI,CAAC,OAAO;AAChC,MAAM,OAAO,EAAE,IAAI,CAAC,QAAQ;AAC5B,MAAM,OAAO,EAAE,IAAI,CAAC,QAAQ;AAC5B,KAAK,CAAC,CAAA;AACN;AACA;AACA,IAAI,SAAS,CAAC,YAAA,GAAe,IAAI,CAAC,YAAY,CAAA;AAC9C;AACA,IAAI,IAAI,SAAS,CAAC,YAAY,EAAE;AAChC;AACA,MAAM,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;AAC3C,KAAI;AACJ;AACA,IAAI,MAAM,QAAS,GAAEC,uBAAW,CAAC,IAAI,CAAC,CAAA;AACtC;AACA;AACA;AACA,IAAI,SAAS,CAAC,WAAY,GAAE,QAAS,EAAA;AACrC;AACA,IAAI,IAAIC,sBAAY,IAAG,QAAQ,EAAE;AACjC,MAAM,MAAM,KAAM,GAAE,CAAC,WAAA,IAAe,WAAW,CAAC,EAAE,KAAK,gBAAgB,CAAA;AACvE,MAAM,MAAM,OAAQ,GAAEC,oBAAU,CAAC,SAAS,CAAC,CAAC,WAAY,IAAG,kBAAkB,CAAA;AAC7E,MAAM,MAAM,QAAQ,QAAQ,CAAC,WAAW,EAAE,CAAC,MAAM,CAAA;AACjD;AACA,MAAM,MAAM,UAAW,GAAE,CAAC,oBAAoB,EAAE,KAAK,CAAC,uBAAuB,EAAE,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,CAAA;AACrG,MAAMC,YAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;AAC5B,MAAM,IAAI,CAAC,WAAY,GAAE,UAAU,CAAA;AACnC,KAAI;AACJ;AACA,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,MAAM,CAAC,GAAG,EAAU,KAAK,EAAmB;AACrD;AACA,IAAI,IAAI,CAAC,IAAK,GAAE,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,OAAO,CAAA;AAC9C,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,OAAO,CAAC,GAAG,EAAU,KAAK,EAAa;AAChD;AACA,IAAI,IAAI,CAAC,IAAK,GAAE,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,OAAO,CAAA;AAC9C,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA,GAAS,YAAY,CAAC,GAAG,EAAU,KAAK,EAAwC;AAChF,IAAI,IAAI,KAAM,KAAI,SAAS,EAAE;AAC7B;AACA,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;AAClC,WAAW;AACX,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAA,GAAI,KAAK,CAAA;AACnC,KAAI;AACJ,GAAE;AACF;AACA;AACA,GAAS,aAAa,CAAC,UAAU,EAAwB;AACzD,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,GAAI,IAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;AACnF,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,SAAS,CAAC,KAAK,EAAwB;AAChD,IAAI,IAAI,CAAC,OAAQ,GAAE,KAAK,CAAA;AACxB,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,aAAa,CAAC,UAAU,EAAgB;AACjD,IAAIC,wBAAa,CAAC,IAAI,EAAE,UAAU,CAAC,CAAA;AACnC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,OAAO,CAAC,IAAI,EAAgB;AACrC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;AACzB,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,UAAU,CAAC,IAAI,EAAgB;AACxC,IAAI,IAAI,CAAC,KAAM,GAAE,IAAI,CAAA;AACrB,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,SAAS,GAAY;AAC9B,IAAI,OAAO,IAAI,CAAC,OAAA,KAAY,IAAI,CAAA;AAChC,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,MAAM,CAAC,YAAY,EAAiB;AAC7C,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;AACjC,GAAE;AACF;AACA;AACA,GAAS,GAAG,CAAC,YAAY,EAAwB;AACjD;AACA,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvB,MAAM,OAAM;AACZ,KAAI;AACJ,IAAI,MAAM,QAAS,GAAEJ,uBAAW,CAAC,IAAI,CAAC,CAAA;AACtC,IAAI;AACJ,MAAMC,sBAAY;AAClB;AACA,MAAM,QAAS;AACf,MAAM,QAAQ,CAAC,WAAW,EAAE,CAAC,MAAA,KAAW,IAAI,CAAC,OAAA;AAC7C,MAAM;AACN,MAAM,MAAM,UAAA,GAAa,IAAI,CAAC,WAAW,CAAA;AACzC,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQE,YAAM,CAAC,GAAG,CAAC,CAAC,UAAW,GAAW,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAAA;AAC3E,OAAM;AACN,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,QAAA,GAAWE,gCAAsB,CAAC,YAAY,CAAC,CAAA;AACxD,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,aAAa,GAAW;AACjC,IAAI,OAAOC,2BAAiB,CAAC,IAAI,CAAC,CAAA;AAClC,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,SAAS,GAAgB;AAClC,IAAI,OAAOC,uBAAiB,CAAC;AAC7B,MAAM,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE;AAC3B,MAAM,WAAW,EAAE,IAAI,CAAC,KAAK;AAC7B,MAAM,YAAY,EAAE,IAAI,CAAC,QAAQ;AACjC;AACA,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE;AACjB,MAAM,YAAY,EAAE,IAAI,CAAC,aAAa;AACtC,MAAM,OAAO,EAAE,IAAI,CAAC,QAAQ;AAC5B,MAAM,MAAM,EAAE,IAAI,CAAC,OAAO;AAC1B,MAAM,cAAc,EAAE,IAAI,CAAC,UAAU;AACrC,MAAM,MAAM,EAAE,IAAI,CAAC,OAAO;AAC1B;AACA,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI;AACrB,MAAM,OAAO,EAAE,IAAI,CAAC,QAAQ;AAC5B,KAAK,CAAC,CAAA;AACN,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,iBAAiB,CAAC,WAAW,EAAqB;AAC3D;AACA,IAAI,IAAI,CAAC,IAAK,GAAE,WAAW,CAAC,IAAA,IAAQ,EAAE,CAAA;AACtC;AACA,IAAI,IAAI,CAAC,KAAA,GAAQ,WAAW,CAAC,IAAK,IAAG,WAAW,CAAC,WAAW,CAAA;AAC5D,IAAI,IAAI,CAAC,QAAA,GAAW,WAAW,CAAC,YAAY,CAAA;AAC5C;AACA,IAAI,IAAI,CAAC,EAAA,GAAK,WAAW,CAAC,EAAE,CAAA;AAC5B,IAAI,IAAI,CAAC,aAAA,GAAgB,WAAW,CAAC,YAAY,CAAA;AACjD,IAAI,IAAI,CAAC,QAAA,GAAW,WAAW,CAAC,OAAO,CAAA;AACvC,IAAI,IAAI,CAAC,OAAA,GAAU,WAAW,CAAC,MAAO,IAAG,IAAI,CAAC,OAAO,CAAA;AACrD,IAAI,IAAI,CAAC,UAAA,GAAa,WAAW,CAAC,cAAe,IAAG,IAAI,CAAC,UAAU,CAAA;AACnE,IAAI,IAAI,CAAC,OAAA,GAAU,WAAW,CAAC,MAAM,CAAA;AACrC;AACA,IAAI,IAAI,CAAC,IAAK,GAAE,WAAW,CAAC,IAAA,IAAQ,EAAE,CAAA;AACtC,IAAI,IAAI,CAAC,QAAA,GAAW,WAAW,CAAC,OAAQ,IAAG,IAAI,CAAC,QAAQ,CAAA;AACxD;AACA,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,eAAe,GAAiB;AACzC,IAAI,OAAOC,4BAAkB,CAAC,IAAI,CAAC,CAAA;AACnC,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,WAAW,GAAa;AACjC,IAAI,OAAOD,uBAAiB,CAAC;AAC7B,MAAM,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE;AAC3B,MAAM,WAAW,EAAE,IAAI,CAAC,KAAK;AAC7B,MAAM,EAAE,EAAE,IAAI,CAAC,WAAW,CAACV,+CAA4B,CAAE;AACzD,MAAM,cAAc,EAAE,IAAI,CAAC,aAAa;AACxC,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,eAAe,EAAE,IAAI,CAAC,UAAU;AACtC,MAAM,MAAM,EAAE,IAAI,CAAC,OAAO;AAC1B;AACA,MAAM,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAA,GAAS,CAAE,GAAE,IAAI,CAAC,IAAA,GAAO,SAAS;AACrE,MAAM,SAAS,EAAE,IAAI,CAAC,QAAQ;AAC9B,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ;AAC7B,MAAM,MAAM,EAAE,IAAI,CAAC,WAAW,CAACD,mDAAgC,CAAE;AACjE,MAAM,gBAAgB,EAAEa,yCAA2B,CAAC,IAAI,CAAC;AACzD,MAAM,UAAU,EAAE,IAAI,CAAC,WAAW,CAACC,gDAA6B,CAAE;AAClE,MAAM,cAAc,EAAE,IAAI,CAAC,cAAc;AACzC,MAAM,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAA,GAAS,CAAE,GAAE,IAAI,CAAC,aAAA,GAAgB,SAAS;AAC/F,KAAK,CAAC,CAAA;AACN,GAAE;AACF;AACA;AACA,GAAS,WAAW,GAAY;AAChC,IAAI,OAAO,CAAC,IAAI,CAAC,QAAA,IAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAA;AAC5C,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,MAAM,GAAa;AAC5B,IAAI,OAAO,IAAI,CAAC,WAAW,EAAE,CAAA;AAC7B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAU,QAAQ;;AAKd,CAAY;AAChB;AACA,IAAI,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,UAAA,EAAa,GAAE,IAAI,CAAA;AAClD;AACA,IAAI,MAAM,OAAA,GAAU,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAO,GAAE,CAAC,CAAA;AAChD,IAAI,MAAM,aAAA,GAAgB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAO,GAAE,CAAC,CAAA;AAC5D;AACA,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;AACpC,MAAM,OAAO,SAAS,CAAA;AACtB,KAAI;AACJ;AACA,IAAI,IAAI,OAAQ,IAAG,aAAa,EAAE;AAClC,MAAM,OAAO;AACb,QAAQ,GAAG,IAAI;AACf,QAAQ,GAAG,UAAU;AACrB,OAAO,CAAA;AACP,KAAI;AACJ;AACA,IAAI,OAAO,OAAA,GAAU,IAAA,GAAO,UAAU,CAAA;AACtC,GAAE;AACF;;;;;"}