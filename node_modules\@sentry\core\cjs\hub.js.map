{"version": 3, "file": "hub.js", "sources": ["../../src/hub.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport type {\n  Breadcrumb,\n  BreadcrumbHint,\n  Client,\n  CustomSamplingContext,\n  Event,\n  EventHint,\n  Extra,\n  Extras,\n  Hub as HubInterface,\n  Integration,\n  IntegrationClass,\n  Primitive,\n  Session,\n  SessionContext,\n  Severity,\n  SeverityLevel,\n  Transaction,\n  TransactionContext,\n  User,\n} from '@sentry/types';\nimport {\n  GLOBAL_OBJ,\n  consoleSandbox,\n  dateTimestampInSeconds,\n  getGlobalSingleton,\n  isThenable,\n  logger,\n  uuid4,\n} from '@sentry/utils';\n\nimport { DEFAULT_ENVIRONMENT } from './constants';\nimport { DEBUG_BUILD } from './debug-build';\nimport { Scope } from './scope';\nimport { closeSession, makeSession, updateSession } from './session';\nimport { SDK_VERSION } from './version';\n\n/**\n * API compatibility version of this hub.\n *\n * WARNING: This number should only be increased when the global interface\n * changes and new methods are introduced.\n *\n * @hidden\n */\nexport const API_VERSION = parseFloat(SDK_VERSION);\n\n/**\n * Default maximum number of breadcrumbs added to an event. Can be overwritten\n * with {@link Options.maxBreadcrumbs}.\n */\nconst DEFAULT_BREADCRUMBS = 100;\n\nexport interface RunWithAsyncContextOptions {\n  /** Whether to reuse an existing async context if one exists. Defaults to false. */\n  reuseExisting?: boolean;\n}\n\n/**\n * @private Private API with no semver guarantees!\n *\n * Strategy used to track async context.\n */\nexport interface AsyncContextStrategy {\n  /**\n   * Gets the current async context. Returns undefined if there is no current async context.\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  getCurrentHub: () => Hub | undefined;\n  /**\n   * Runs the supplied callback in its own async context.\n   */\n  runWithAsyncContext<T>(callback: () => T, options: RunWithAsyncContextOptions): T;\n}\n\n/**\n * A layer in the process stack.\n * @hidden\n */\nexport interface Layer {\n  client?: Client;\n  scope: Scope;\n}\n\n/**\n * An object that contains a hub and maintains a scope stack.\n * @hidden\n */\nexport interface Carrier {\n  __SENTRY__?: {\n    // eslint-disable-next-line deprecation/deprecation\n    hub?: Hub;\n    acs?: AsyncContextStrategy;\n    /**\n     * Extra Hub properties injected by various SDKs\n     */\n    integrations?: Integration[];\n    extensions?: {\n      /** Extension methods for the hub, which are bound to the current Hub instance */\n      // eslint-disable-next-line @typescript-eslint/ban-types\n      [key: string]: Function;\n    };\n  };\n}\n\n/**\n * @deprecated The `Hub` class will be removed in version 8 of the SDK in favour of `Scope` and `Client` objects.\n *\n * If you previously used the `Hub` class directly, replace it with `Scope` and `Client` objects. More information:\n * - [Multiple Sentry Instances](https://docs.sentry.io/platforms/javascript/best-practices/multiple-sentry-instances/)\n * - [Browser Extensions](https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/)\n *\n * Some of our APIs are typed with the Hub class instead of the interface (e.g. `getCurrentHub`). Most of them are deprecated\n * themselves and will also be removed in version 8. More information:\n * - [Migration Guide](https://github.com/getsentry/sentry-javascript/blob/develop/MIGRATION.md#deprecate-hub)\n */\n// eslint-disable-next-line deprecation/deprecation\nexport class Hub implements HubInterface {\n  /** Is a {@link Layer}[] containing the client and scope */\n  private readonly _stack: Layer[];\n\n  /** Contains the last event id of a captured event.  */\n  private _lastEventId?: string;\n\n  private _isolationScope: Scope;\n\n  /**\n   * Creates a new instance of the hub, will push one {@link Layer} into the\n   * internal stack on creation.\n   *\n   * @param client bound to the hub.\n   * @param scope bound to the hub.\n   * @param version number, higher number means higher priority.\n   *\n   * @deprecated Instantiation of Hub objects is deprecated and the constructor will be removed in version 8 of the SDK.\n   *\n   * If you are currently using the Hub for multi-client use like so:\n   *\n   * ```\n   * // OLD\n   * const hub = new Hub();\n   * hub.bindClient(client);\n   * makeMain(hub)\n   * ```\n   *\n   * instead initialize the client as follows:\n   *\n   * ```\n   * // NEW\n   * Sentry.withIsolationScope(() => {\n   *    Sentry.setCurrentClient(client);\n   *    client.init();\n   * });\n   * ```\n   *\n   * If you are using the Hub to capture events like so:\n   *\n   * ```\n   * // OLD\n   * const client = new Client();\n   * const hub = new Hub(client);\n   * hub.captureException()\n   * ```\n   *\n   * instead capture isolated events as follows:\n   *\n   * ```\n   * // NEW\n   * const client = new Client();\n   * const scope = new Scope();\n   * scope.setClient(client);\n   * scope.captureException();\n   * ```\n   */\n  public constructor(\n    client?: Client,\n    scope?: Scope,\n    isolationScope?: Scope,\n    private readonly _version: number = API_VERSION,\n  ) {\n    let assignedScope;\n    if (!scope) {\n      assignedScope = new Scope();\n      assignedScope.setClient(client);\n    } else {\n      assignedScope = scope;\n    }\n\n    let assignedIsolationScope;\n    if (!isolationScope) {\n      assignedIsolationScope = new Scope();\n      assignedIsolationScope.setClient(client);\n    } else {\n      assignedIsolationScope = isolationScope;\n    }\n\n    this._stack = [{ scope: assignedScope }];\n\n    if (client) {\n      // eslint-disable-next-line deprecation/deprecation\n      this.bindClient(client);\n    }\n\n    this._isolationScope = assignedIsolationScope;\n  }\n\n  /**\n   * Checks if this hub's version is older than the given version.\n   *\n   * @param version A version number to compare to.\n   * @return True if the given version is newer; otherwise false.\n   *\n   * @deprecated This will be removed in v8.\n   */\n  public isOlderThan(version: number): boolean {\n    return this._version < version;\n  }\n\n  /**\n   * This binds the given client to the current scope.\n   * @param client An SDK client (client) instance.\n   *\n   * @deprecated Use `initAndBind()` directly, or `setCurrentClient()` and/or `client.init()` instead.\n   */\n  public bindClient(client?: Client): void {\n    // eslint-disable-next-line deprecation/deprecation\n    const top = this.getStackTop();\n    top.client = client;\n    top.scope.setClient(client);\n    // eslint-disable-next-line deprecation/deprecation\n    if (client && client.setupIntegrations) {\n      // eslint-disable-next-line deprecation/deprecation\n      client.setupIntegrations();\n    }\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use `withScope` instead.\n   */\n  public pushScope(): Scope {\n    // We want to clone the content of prev scope\n    // eslint-disable-next-line deprecation/deprecation\n    const scope = this.getScope().clone();\n    // eslint-disable-next-line deprecation/deprecation\n    this.getStack().push({\n      // eslint-disable-next-line deprecation/deprecation\n      client: this.getClient(),\n      scope,\n    });\n    return scope;\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use `withScope` instead.\n   */\n  public popScope(): boolean {\n    // eslint-disable-next-line deprecation/deprecation\n    if (this.getStack().length <= 1) return false;\n    // eslint-disable-next-line deprecation/deprecation\n    return !!this.getStack().pop();\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use `Sentry.withScope()` instead.\n   */\n  public withScope<T>(callback: (scope: Scope) => T): T {\n    // eslint-disable-next-line deprecation/deprecation\n    const scope = this.pushScope();\n\n    let maybePromiseResult: T;\n    try {\n      maybePromiseResult = callback(scope);\n    } catch (e) {\n      // eslint-disable-next-line deprecation/deprecation\n      this.popScope();\n      throw e;\n    }\n\n    if (isThenable(maybePromiseResult)) {\n      // @ts-expect-error - isThenable returns the wrong type\n      return maybePromiseResult.then(\n        res => {\n          // eslint-disable-next-line deprecation/deprecation\n          this.popScope();\n          return res;\n        },\n        e => {\n          // eslint-disable-next-line deprecation/deprecation\n          this.popScope();\n          throw e;\n        },\n      );\n    }\n\n    // eslint-disable-next-line deprecation/deprecation\n    this.popScope();\n    return maybePromiseResult;\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use `Sentry.getClient()` instead.\n   */\n  public getClient<C extends Client>(): C | undefined {\n    // eslint-disable-next-line deprecation/deprecation\n    return this.getStackTop().client as C;\n  }\n\n  /**\n   * Returns the scope of the top stack.\n   *\n   * @deprecated Use `Sentry.getCurrentScope()` instead.\n   */\n  public getScope(): Scope {\n    // eslint-disable-next-line deprecation/deprecation\n    return this.getStackTop().scope;\n  }\n\n  /**\n   * @deprecated Use `Sentry.getIsolationScope()` instead.\n   */\n  public getIsolationScope(): Scope {\n    return this._isolationScope;\n  }\n\n  /**\n   * Returns the scope stack for domains or the process.\n   * @deprecated This will be removed in v8.\n   */\n  public getStack(): Layer[] {\n    return this._stack;\n  }\n\n  /**\n   * Returns the topmost scope layer in the order domain > local > process.\n   * @deprecated This will be removed in v8.\n   */\n  public getStackTop(): Layer {\n    return this._stack[this._stack.length - 1];\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use `Sentry.captureException()` instead.\n   */\n  public captureException(exception: unknown, hint?: EventHint): string {\n    const eventId = (this._lastEventId = hint && hint.event_id ? hint.event_id : uuid4());\n    const syntheticException = new Error('Sentry syntheticException');\n    // eslint-disable-next-line deprecation/deprecation\n    this.getScope().captureException(exception, {\n      originalException: exception,\n      syntheticException,\n      ...hint,\n      event_id: eventId,\n    });\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use  `Sentry.captureMessage()` instead.\n   */\n  public captureMessage(\n    message: string,\n    // eslint-disable-next-line deprecation/deprecation\n    level?: Severity | SeverityLevel,\n    hint?: EventHint,\n  ): string {\n    const eventId = (this._lastEventId = hint && hint.event_id ? hint.event_id : uuid4());\n    const syntheticException = new Error(message);\n    // eslint-disable-next-line deprecation/deprecation\n    this.getScope().captureMessage(message, level, {\n      originalException: message,\n      syntheticException,\n      ...hint,\n      event_id: eventId,\n    });\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use `Sentry.captureEvent()` instead.\n   */\n  public captureEvent(event: Event, hint?: EventHint): string {\n    const eventId = hint && hint.event_id ? hint.event_id : uuid4();\n    if (!event.type) {\n      this._lastEventId = eventId;\n    }\n    // eslint-disable-next-line deprecation/deprecation\n    this.getScope().captureEvent(event, { ...hint, event_id: eventId });\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated This will be removed in v8.\n   */\n  public lastEventId(): string | undefined {\n    return this._lastEventId;\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use `Sentry.addBreadcrumb()` instead.\n   */\n  public addBreadcrumb(breadcrumb: Breadcrumb, hint?: BreadcrumbHint): void {\n    // eslint-disable-next-line deprecation/deprecation\n    const { scope, client } = this.getStackTop();\n\n    if (!client) return;\n\n    const { beforeBreadcrumb = null, maxBreadcrumbs = DEFAULT_BREADCRUMBS } =\n      (client.getOptions && client.getOptions()) || {};\n\n    if (maxBreadcrumbs <= 0) return;\n\n    const timestamp = dateTimestampInSeconds();\n    const mergedBreadcrumb = { timestamp, ...breadcrumb };\n    const finalBreadcrumb = beforeBreadcrumb\n      ? (consoleSandbox(() => beforeBreadcrumb(mergedBreadcrumb, hint)) as Breadcrumb | null)\n      : mergedBreadcrumb;\n\n    if (finalBreadcrumb === null) return;\n\n    if (client.emit) {\n      client.emit('beforeAddBreadcrumb', finalBreadcrumb, hint);\n    }\n\n    // TODO(v8): I know this comment doesn't make much sense because the hub will be deprecated but I still wanted to\n    // write it down. In theory, we would have to add the breadcrumbs to the isolation scope here, however, that would\n    // duplicate all of the breadcrumbs. There was the possibility of adding breadcrumbs to both, the isolation scope\n    // and the normal scope, and deduplicating it down the line in the event processing pipeline. However, that would\n    // have been very fragile, because the breadcrumb objects would have needed to keep their identity all throughout\n    // the event processing pipeline.\n    // In the new implementation, the top level `Sentry.addBreadcrumb()` should ONLY write to the isolation scope.\n\n    scope.addBreadcrumb(finalBreadcrumb, maxBreadcrumbs);\n  }\n\n  /**\n   * @inheritDoc\n   * @deprecated Use `Sentry.setUser()` instead.\n   */\n  public setUser(user: User | null): void {\n    // TODO(v8): The top level `Sentry.setUser()` function should write ONLY to the isolation scope.\n    // eslint-disable-next-line deprecation/deprecation\n    this.getScope().setUser(user);\n    // eslint-disable-next-line deprecation/deprecation\n    this.getIsolationScope().setUser(user);\n  }\n\n  /**\n   * @inheritDoc\n   * @deprecated Use `Sentry.setTags()` instead.\n   */\n  public setTags(tags: { [key: string]: Primitive }): void {\n    // TODO(v8): The top level `Sentry.setTags()` function should write ONLY to the isolation scope.\n    // eslint-disable-next-line deprecation/deprecation\n    this.getScope().setTags(tags);\n    // eslint-disable-next-line deprecation/deprecation\n    this.getIsolationScope().setTags(tags);\n  }\n\n  /**\n   * @inheritDoc\n   * @deprecated Use `Sentry.setExtras()` instead.\n   */\n  public setExtras(extras: Extras): void {\n    // TODO(v8): The top level `Sentry.setExtras()` function should write ONLY to the isolation scope.\n    // eslint-disable-next-line deprecation/deprecation\n    this.getScope().setExtras(extras);\n    // eslint-disable-next-line deprecation/deprecation\n    this.getIsolationScope().setExtras(extras);\n  }\n\n  /**\n   * @inheritDoc\n   * @deprecated Use `Sentry.setTag()` instead.\n   */\n  public setTag(key: string, value: Primitive): void {\n    // TODO(v8): The top level `Sentry.setTag()` function should write ONLY to the isolation scope.\n    // eslint-disable-next-line deprecation/deprecation\n    this.getScope().setTag(key, value);\n    // eslint-disable-next-line deprecation/deprecation\n    this.getIsolationScope().setTag(key, value);\n  }\n\n  /**\n   * @inheritDoc\n   * @deprecated Use `Sentry.setExtra()` instead.\n   */\n  public setExtra(key: string, extra: Extra): void {\n    // TODO(v8): The top level `Sentry.setExtra()` function should write ONLY to the isolation scope.\n    // eslint-disable-next-line deprecation/deprecation\n    this.getScope().setExtra(key, extra);\n    // eslint-disable-next-line deprecation/deprecation\n    this.getIsolationScope().setExtra(key, extra);\n  }\n\n  /**\n   * @inheritDoc\n   * @deprecated Use `Sentry.setContext()` instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  public setContext(name: string, context: { [key: string]: any } | null): void {\n    // TODO(v8): The top level `Sentry.setContext()` function should write ONLY to the isolation scope.\n    // eslint-disable-next-line deprecation/deprecation\n    this.getScope().setContext(name, context);\n    // eslint-disable-next-line deprecation/deprecation\n    this.getIsolationScope().setContext(name, context);\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use `getScope()` directly.\n   */\n  public configureScope(callback: (scope: Scope) => void): void {\n    // eslint-disable-next-line deprecation/deprecation\n    const { scope, client } = this.getStackTop();\n    if (client) {\n      callback(scope);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  public run(callback: (hub: Hub) => void): void {\n    // eslint-disable-next-line deprecation/deprecation\n    const oldHub = makeMain(this);\n    try {\n      callback(this);\n    } finally {\n      // eslint-disable-next-line deprecation/deprecation\n      makeMain(oldHub);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   * @deprecated Use `Sentry.getClient().getIntegrationByName()` instead.\n   */\n  public getIntegration<T extends Integration>(integration: IntegrationClass<T>): T | null {\n    // eslint-disable-next-line deprecation/deprecation\n    const client = this.getClient();\n    if (!client) return null;\n    try {\n      // eslint-disable-next-line deprecation/deprecation\n      return client.getIntegration(integration);\n    } catch (_oO) {\n      DEBUG_BUILD && logger.warn(`Cannot retrieve integration ${integration.id} from the current Hub`);\n      return null;\n    }\n  }\n\n  /**\n   * Starts a new `Transaction` and returns it. This is the entry point to manual tracing instrumentation.\n   *\n   * A tree structure can be built by adding child spans to the transaction, and child spans to other spans. To start a\n   * new child span within the transaction or any span, call the respective `.startChild()` method.\n   *\n   * Every child span must be finished before the transaction is finished, otherwise the unfinished spans are discarded.\n   *\n   * The transaction must be finished with a call to its `.end()` method, at which point the transaction with all its\n   * finished child spans will be sent to Sentry.\n   *\n   * @param context Properties of the new `Transaction`.\n   * @param customSamplingContext Information given to the transaction sampling function (along with context-dependent\n   * default values). See {@link Options.tracesSampler}.\n   *\n   * @returns The transaction which was just started\n   *\n   * @deprecated Use `startSpan()`, `startSpanManual()` or `startInactiveSpan()` instead.\n   */\n  public startTransaction(context: TransactionContext, customSamplingContext?: CustomSamplingContext): Transaction {\n    const result = this._callExtensionMethod<Transaction>('startTransaction', context, customSamplingContext);\n\n    if (DEBUG_BUILD && !result) {\n      // eslint-disable-next-line deprecation/deprecation\n      const client = this.getClient();\n      if (!client) {\n        logger.warn(\n          \"Tracing extension 'startTransaction' is missing. You should 'init' the SDK before calling 'startTransaction'\",\n        );\n      } else {\n        logger.warn(`Tracing extension 'startTransaction' has not been added. Call 'addTracingExtensions' before calling 'init':\nSentry.addTracingExtensions();\nSentry.init({...});\n`);\n      }\n    }\n\n    return result;\n  }\n\n  /**\n   * @inheritDoc\n   * @deprecated Use `spanToTraceHeader()` instead.\n   */\n  public traceHeaders(): { [key: string]: string } {\n    return this._callExtensionMethod<{ [key: string]: string }>('traceHeaders');\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use top level `captureSession` instead.\n   */\n  public captureSession(endSession: boolean = false): void {\n    // both send the update and pull the session from the scope\n    if (endSession) {\n      // eslint-disable-next-line deprecation/deprecation\n      return this.endSession();\n    }\n\n    // only send the update\n    this._sendSessionUpdate();\n  }\n\n  /**\n   * @inheritDoc\n   * @deprecated Use top level `endSession` instead.\n   */\n  public endSession(): void {\n    // eslint-disable-next-line deprecation/deprecation\n    const layer = this.getStackTop();\n    const scope = layer.scope;\n    const session = scope.getSession();\n    if (session) {\n      closeSession(session);\n    }\n    this._sendSessionUpdate();\n\n    // the session is over; take it off of the scope\n    scope.setSession();\n  }\n\n  /**\n   * @inheritDoc\n   * @deprecated Use top level `startSession` instead.\n   */\n  public startSession(context?: SessionContext): Session {\n    // eslint-disable-next-line deprecation/deprecation\n    const { scope, client } = this.getStackTop();\n    const { release, environment = DEFAULT_ENVIRONMENT } = (client && client.getOptions()) || {};\n\n    // Will fetch userAgent if called from browser sdk\n    const { userAgent } = GLOBAL_OBJ.navigator || {};\n\n    const session = makeSession({\n      release,\n      environment,\n      user: scope.getUser(),\n      ...(userAgent && { userAgent }),\n      ...context,\n    });\n\n    // End existing session if there's one\n    const currentSession = scope.getSession && scope.getSession();\n    if (currentSession && currentSession.status === 'ok') {\n      updateSession(currentSession, { status: 'exited' });\n    }\n    // eslint-disable-next-line deprecation/deprecation\n    this.endSession();\n\n    // Afterwards we set the new session on the scope\n    scope.setSession(session);\n\n    return session;\n  }\n\n  /**\n   * Returns if default PII should be sent to Sentry and propagated in ourgoing requests\n   * when Tracing is used.\n   *\n   * @deprecated Use top-level `getClient().getOptions().sendDefaultPii` instead. This function\n   * only unnecessarily increased API surface but only wrapped accessing the option.\n   */\n  public shouldSendDefaultPii(): boolean {\n    // eslint-disable-next-line deprecation/deprecation\n    const client = this.getClient();\n    const options = client && client.getOptions();\n    return Boolean(options && options.sendDefaultPii);\n  }\n\n  /**\n   * Sends the current Session on the scope\n   */\n  private _sendSessionUpdate(): void {\n    // eslint-disable-next-line deprecation/deprecation\n    const { scope, client } = this.getStackTop();\n\n    const session = scope.getSession();\n    if (session && client && client.captureSession) {\n      client.captureSession(session);\n    }\n  }\n\n  /**\n   * Calls global extension method and binding current instance to the function call\n   */\n  // @ts-expect-error Function lacks ending return statement and return type does not include 'undefined'. ts(2366)\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _callExtensionMethod<T>(method: string, ...args: any[]): T {\n    const carrier = getMainCarrier();\n    const sentry = carrier.__SENTRY__;\n    if (sentry && sentry.extensions && typeof sentry.extensions[method] === 'function') {\n      return sentry.extensions[method].apply(this, args);\n    }\n    DEBUG_BUILD && logger.warn(`Extension method ${method} couldn't be found, doing nothing.`);\n  }\n}\n\n/**\n * Returns the global shim registry.\n *\n * FIXME: This function is problematic, because despite always returning a valid Carrier,\n * it has an optional `__SENTRY__` property, which then in turn requires us to always perform an unnecessary check\n * at the call-site. We always access the carrier through this function, so we can guarantee that `__SENTRY__` is there.\n **/\nexport function getMainCarrier(): Carrier {\n  GLOBAL_OBJ.__SENTRY__ = GLOBAL_OBJ.__SENTRY__ || {\n    extensions: {},\n    hub: undefined,\n  };\n  return GLOBAL_OBJ;\n}\n\n/**\n * Replaces the current main hub with the passed one on the global object\n *\n * @returns The old replaced hub\n *\n * @deprecated Use `setCurrentClient()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport function makeMain(hub: Hub): Hub {\n  const registry = getMainCarrier();\n  const oldHub = getHubFromCarrier(registry);\n  setHubOnCarrier(registry, hub);\n  return oldHub;\n}\n\n/**\n * Returns the default hub instance.\n *\n * If a hub is already registered in the global carrier but this module\n * contains a more recent version, it replaces the registered version.\n * Otherwise, the currently registered hub will be returned.\n *\n * @deprecated Use the respective replacement method directly instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport function getCurrentHub(): Hub {\n  // Get main carrier (global for every environment)\n  const registry = getMainCarrier();\n\n  if (registry.__SENTRY__ && registry.__SENTRY__.acs) {\n    const hub = registry.__SENTRY__.acs.getCurrentHub();\n\n    if (hub) {\n      return hub;\n    }\n  }\n\n  // Return hub that lives on a global object\n  return getGlobalHub(registry);\n}\n\n/**\n * Get the currently active isolation scope.\n * The isolation scope is active for the current exection context,\n * meaning that it will remain stable for the same Hub.\n */\nexport function getIsolationScope(): Scope {\n  // eslint-disable-next-line deprecation/deprecation\n  return getCurrentHub().getIsolationScope();\n}\n\n// eslint-disable-next-line deprecation/deprecation\nfunction getGlobalHub(registry: Carrier = getMainCarrier()): Hub {\n  // If there's no hub, or its an old API, assign a new one\n\n  if (\n    !hasHubOnCarrier(registry) ||\n    // eslint-disable-next-line deprecation/deprecation\n    getHubFromCarrier(registry).isOlderThan(API_VERSION)\n  ) {\n    // eslint-disable-next-line deprecation/deprecation\n    setHubOnCarrier(registry, new Hub());\n  }\n\n  // Return hub that lives on a global object\n  return getHubFromCarrier(registry);\n}\n\n/**\n * @private Private API with no semver guarantees!\n *\n * If the carrier does not contain a hub, a new hub is created with the global hub client and scope.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport function ensureHubOnCarrier(carrier: Carrier, parent: Hub = getGlobalHub()): void {\n  // If there's no hub on current domain, or it's an old API, assign a new one\n  if (\n    !hasHubOnCarrier(carrier) ||\n    // eslint-disable-next-line deprecation/deprecation\n    getHubFromCarrier(carrier).isOlderThan(API_VERSION)\n  ) {\n    // eslint-disable-next-line deprecation/deprecation\n    const client = parent.getClient();\n    // eslint-disable-next-line deprecation/deprecation\n    const scope = parent.getScope();\n    // eslint-disable-next-line deprecation/deprecation\n    const isolationScope = parent.getIsolationScope();\n    // eslint-disable-next-line deprecation/deprecation\n    setHubOnCarrier(carrier, new Hub(client, scope.clone(), isolationScope.clone()));\n  }\n}\n\n/**\n * @private Private API with no semver guarantees!\n *\n * Sets the global async context strategy\n */\nexport function setAsyncContextStrategy(strategy: AsyncContextStrategy | undefined): void {\n  // Get main carrier (global for every environment)\n  const registry = getMainCarrier();\n  registry.__SENTRY__ = registry.__SENTRY__ || {};\n  registry.__SENTRY__.acs = strategy;\n}\n\n/**\n * Runs the supplied callback in its own async context. Async Context strategies are defined per SDK.\n *\n * @param callback The callback to run in its own async context\n * @param options Options to pass to the async context strategy\n * @returns The result of the callback\n */\nexport function runWithAsyncContext<T>(callback: () => T, options: RunWithAsyncContextOptions = {}): T {\n  const registry = getMainCarrier();\n\n  if (registry.__SENTRY__ && registry.__SENTRY__.acs) {\n    return registry.__SENTRY__.acs.runWithAsyncContext(callback, options);\n  }\n\n  // if there was no strategy, fallback to just calling the callback\n  return callback();\n}\n\n/**\n * This will tell whether a carrier has a hub on it or not\n * @param carrier object\n */\nfunction hasHubOnCarrier(carrier: Carrier): boolean {\n  return !!(carrier && carrier.__SENTRY__ && carrier.__SENTRY__.hub);\n}\n\n/**\n * This will create a new {@link Hub} and add to the passed object on\n * __SENTRY__.hub.\n * @param carrier object\n * @hidden\n */\n// eslint-disable-next-line deprecation/deprecation\nexport function getHubFromCarrier(carrier: Carrier): Hub {\n  // eslint-disable-next-line deprecation/deprecation\n  return getGlobalSingleton<Hub>('hub', () => new Hub(), carrier);\n}\n\n/**\n * This will set passed {@link Hub} on the passed object's __SENTRY__.hub attribute\n * @param carrier object\n * @param hub Hub\n * @returns A boolean indicating success or failure\n */\n// eslint-disable-next-line deprecation/deprecation\nexport function setHubOnCarrier(carrier: Carrier, hub: Hub): boolean {\n  if (!carrier) return false;\n  const __SENTRY__ = (carrier.__SENTRY__ = carrier.__SENTRY__ || {});\n  __SENTRY__.hub = hub;\n  return true;\n}\n"], "names": ["SDK_VERSION", "scope", "<PERSON><PERSON>", "isThenable", "uuid4", "dateTimestampInSeconds", "consoleSandbox", "DEBUG_BUILD", "logger", "session", "closeSession", "DEFAULT_ENVIRONMENT", "GLOBAL_OBJ", "makeSession", "updateSession", "getGlobalSingleton"], "mappings": ";;;;;;;;;AAsCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACa,WAAY,GAAE,UAAU,CAACA,mBAAW,EAAC;AAClD;AACA;AACA;AACA;AACA;AACA,MAAM,mBAAA,GAAsB,GAAG,CAAA;;AAsD/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,KAA4B;AACzC;;AAGA;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,WAAW;AACpB,IAAI,MAAM;AACV,IAAIC,OAAK;AACT,IAAI,cAAc;AAClB,MAAqB,QAAQ,GAAW,WAAW;AACnD,IAAI,CAAA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA;AACJ,IAAI,IAAI,aAAa,CAAA;AACrB,IAAI,IAAI,CAACA,OAAK,EAAE;AAChB,MAAM,aAAc,GAAE,IAAIC,WAAK,EAAE,CAAA;AACjC,MAAM,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;AACrC,WAAW;AACX,MAAM,aAAA,GAAgBD,OAAK,CAAA;AAC3B,KAAI;AACJ;AACA,IAAI,IAAI,sBAAsB,CAAA;AAC9B,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,sBAAuB,GAAE,IAAIC,WAAK,EAAE,CAAA;AAC1C,MAAM,sBAAsB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;AAC9C,WAAW;AACX,MAAM,sBAAA,GAAyB,cAAc,CAAA;AAC7C,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,MAAA,GAAS,CAAC,EAAE,KAAK,EAAE,aAAc,EAAC,CAAC,CAAA;AAC5C;AACA,IAAI,IAAI,MAAM,EAAE;AAChB;AACA,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;AAC7B,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,eAAgB,GAAE,sBAAsB,CAAA;AACjD,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,WAAW,CAAC,OAAO,EAAmB;AAC/C,IAAI,OAAO,IAAI,CAAC,QAAA,GAAW,OAAO,CAAA;AAClC,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,UAAU,CAAC,MAAM,EAAiB;AAC3C;AACA,IAAI,MAAM,GAAI,GAAE,IAAI,CAAC,WAAW,EAAE,CAAA;AAClC,IAAI,GAAG,CAAC,MAAO,GAAE,MAAM,CAAA;AACvB,IAAI,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;AAC/B;AACA,IAAI,IAAI,MAAA,IAAU,MAAM,CAAC,iBAAiB,EAAE;AAC5C;AACA,MAAM,MAAM,CAAC,iBAAiB,EAAE,CAAA;AAChC,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,SAAS,GAAU;AAC5B;AACA;AACA,IAAI,MAAM,KAAM,GAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,CAAA;AACzC;AACA,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC;AACzB;AACA,MAAM,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE;AAC9B,MAAM,KAAK;AACX,KAAK,CAAC,CAAA;AACN,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,QAAQ,GAAY;AAC7B;AACA,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAO,IAAG,CAAC,EAAE,OAAO,KAAK,CAAA;AACjD;AACA,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAA;AAClC,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,SAAS,CAAI,QAAQ,EAA0B;AACxD;AACA,IAAI,MAAM,KAAM,GAAE,IAAI,CAAC,SAAS,EAAE,CAAA;AAClC;AACA,IAAI,IAAI,kBAAkB,CAAA;AAC1B,IAAI,IAAI;AACR,MAAM,kBAAmB,GAAE,QAAQ,CAAC,KAAK,CAAC,CAAA;AAC1C,KAAM,CAAA,OAAO,CAAC,EAAE;AAChB;AACA,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAA;AACrB,MAAM,MAAM,CAAC,CAAA;AACb,KAAI;AACJ;AACA,IAAI,IAAIC,gBAAU,CAAC,kBAAkB,CAAC,EAAE;AACxC;AACA,MAAM,OAAO,kBAAkB,CAAC,IAAI;AACpC,QAAQ,OAAO;AACf;AACA,UAAU,IAAI,CAAC,QAAQ,EAAE,CAAA;AACzB,UAAU,OAAO,GAAG,CAAA;AACpB,SAAS;AACT,QAAQ,KAAK;AACb;AACA,UAAU,IAAI,CAAC,QAAQ,EAAE,CAAA;AACzB,UAAU,MAAM,CAAC,CAAA;AACjB,SAAS;AACT,OAAO,CAAA;AACP,KAAI;AACJ;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAA;AACnB,IAAI,OAAO,kBAAkB,CAAA;AAC7B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,SAAS,GAAoC;AACtD;AACA,IAAI,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,MAAO,EAAA;AACrC,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,QAAQ,GAAU;AAC3B;AACA,IAAI,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAA;AACnC,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,iBAAiB,GAAU;AACpC,IAAI,OAAO,IAAI,CAAC,eAAe,CAAA;AAC/B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,QAAQ,GAAY;AAC7B,IAAI,OAAO,IAAI,CAAC,MAAM,CAAA;AACtB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,WAAW,GAAU;AAC9B,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAA,GAAS,CAAC,CAAC,CAAA;AAC9C,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,gBAAgB,CAAC,SAAS,EAAW,IAAI,EAAsB;AACxE,IAAI,MAAM,WAAW,IAAI,CAAC,YAAA,GAAe,IAAK,IAAG,IAAI,CAAC,QAAA,GAAW,IAAI,CAAC,WAAWC,WAAK,EAAE,CAAC,CAAA;AACzF,IAAI,MAAM,kBAAmB,GAAE,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;AACrE;AACA,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE;AAChD,MAAM,iBAAiB,EAAE,SAAS;AAClC,MAAM,kBAAkB;AACxB,MAAM,GAAG,IAAI;AACb,MAAM,QAAQ,EAAE,OAAO;AACvB,KAAK,CAAC,CAAA;AACN;AACA,IAAI,OAAO,OAAO,CAAA;AAClB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,cAAc;AACvB,IAAI,OAAO;AACX;AACA,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAY;AACZ,IAAI,MAAM,WAAW,IAAI,CAAC,YAAA,GAAe,IAAK,IAAG,IAAI,CAAC,QAAA,GAAW,IAAI,CAAC,WAAWA,WAAK,EAAE,CAAC,CAAA;AACzF,IAAI,MAAM,kBAAmB,GAAE,IAAI,KAAK,CAAC,OAAO,CAAC,CAAA;AACjD;AACA,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE;AACnD,MAAM,iBAAiB,EAAE,OAAO;AAChC,MAAM,kBAAkB;AACxB,MAAM,GAAG,IAAI;AACb,MAAM,QAAQ,EAAE,OAAO;AACvB,KAAK,CAAC,CAAA;AACN;AACA,IAAI,OAAO,OAAO,CAAA;AAClB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,YAAY,CAAC,KAAK,EAAS,IAAI,EAAsB;AAC9D,IAAI,MAAM,OAAA,GAAU,IAAA,IAAQ,IAAI,CAAC,QAAS,GAAE,IAAI,CAAC,QAAA,GAAWA,WAAK,EAAE,CAAA;AACnE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;AACrB,MAAM,IAAI,CAAC,YAAa,GAAE,OAAO,CAAA;AACjC,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,GAAG,IAAI,EAAE,QAAQ,EAAE,OAAA,EAAS,CAAC,CAAA;AACvE,IAAI,OAAO,OAAO,CAAA;AAClB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,WAAW,GAAuB;AAC3C,IAAI,OAAO,IAAI,CAAC,YAAY,CAAA;AAC5B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,aAAa,CAAC,UAAU,EAAc,IAAI,EAAyB;AAC5E;AACA,IAAI,MAAM,EAAE,KAAK,EAAE,MAAA,EAAS,GAAE,IAAI,CAAC,WAAW,EAAE,CAAA;AAChD;AACA,IAAI,IAAI,CAAC,MAAM,EAAE,OAAM;AACvB;AACA,IAAI,MAAM,EAAE,gBAAiB,GAAE,IAAI,EAAE,cAAA,GAAiB,mBAAA,EAAsB;AAC5E,MAAM,CAAC,MAAM,CAAC,UAAA,IAAc,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,CAAA;AACtD;AACA,IAAI,IAAI,cAAA,IAAkB,CAAC,EAAE,OAAM;AACnC;AACA,IAAI,MAAM,SAAA,GAAYC,4BAAsB,EAAE,CAAA;AAC9C,IAAI,MAAM,mBAAmB,EAAE,SAAS,EAAE,GAAG,YAAY,CAAA;AACzD,IAAI,MAAM,kBAAkB,gBAAA;AAC5B,SAASC,oBAAc,CAAC,MAAM,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAE;AACxE,QAAQ,gBAAgB,CAAA;AACxB;AACA,IAAI,IAAI,eAAA,KAAoB,IAAI,EAAE,OAAM;AACxC;AACA,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE;AACrB,MAAM,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,eAAe,EAAE,IAAI,CAAC,CAAA;AAC/D,KAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAK,CAAC,aAAa,CAAC,eAAe,EAAE,cAAc,CAAC,CAAA;AACxD,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,OAAO,CAAC,IAAI,EAAqB;AAC1C;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;AACjC;AACA,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;AAC1C,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,OAAO,CAAC,IAAI,EAAsC;AAC3D;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;AACjC;AACA,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;AAC1C,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,SAAS,CAAC,MAAM,EAAgB;AACzC;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;AACrC;AACA,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;AAC9C,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,MAAM,CAAC,GAAG,EAAU,KAAK,EAAmB;AACrD;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;AACtC;AACA,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;AAC/C,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,QAAQ,CAAC,GAAG,EAAU,KAAK,EAAe;AACnD;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;AACxC;AACA,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;AACjD,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,UAAU,CAAC,IAAI,EAAU,OAAO,EAAuC;AAChF;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;AAC7C;AACA,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;AACtD,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,cAAc,CAAC,QAAQ,EAAgC;AAChE;AACA,IAAI,MAAM,EAAE,KAAK,EAAE,MAAA,EAAS,GAAE,IAAI,CAAC,WAAW,EAAE,CAAA;AAChD,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,QAAQ,CAAC,KAAK,CAAC,CAAA;AACrB,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,GAAG,CAAC,QAAQ,EAA4B;AACjD;AACA,IAAI,MAAM,MAAO,GAAE,QAAQ,CAAC,IAAI,CAAC,CAAA;AACjC,IAAI,IAAI;AACR,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA;AACpB,cAAc;AACd;AACA,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA;AACtB,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,cAAc,CAAwB,WAAW,EAAiC;AAC3F;AACA,IAAI,MAAM,MAAO,GAAE,IAAI,CAAC,SAAS,EAAE,CAAA;AACnC,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,IAAI,CAAA;AAC5B,IAAI,IAAI;AACR;AACA,MAAM,OAAO,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;AAC/C,KAAM,CAAA,OAAO,GAAG,EAAE;AAClB,MAAMC,sBAAY,IAAGC,YAAM,CAAC,IAAI,CAAC,CAAC,4BAA4B,EAAE,WAAW,CAAC,EAAE,CAAC,qBAAqB,CAAC,CAAC,CAAA;AACtG,MAAM,OAAO,IAAI,CAAA;AACjB,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,gBAAgB,CAAC,OAAO,EAAsB,qBAAqB,EAAuC;AACnH,IAAI,MAAM,MAAA,GAAS,IAAI,CAAC,oBAAoB,CAAc,kBAAkB,EAAE,OAAO,EAAE,qBAAqB,CAAC,CAAA;AAC7G;AACA,IAAI,IAAID,sBAAA,IAAe,CAAC,MAAM,EAAE;AAChC;AACA,MAAM,MAAM,MAAO,GAAE,IAAI,CAAC,SAAS,EAAE,CAAA;AACrC,MAAM,IAAI,CAAC,MAAM,EAAE;AACnB,QAAQC,YAAM,CAAC,IAAI;AACnB,UAAU,8GAA8G;AACxH,SAAS,CAAA;AACT,aAAa;AACb,QAAQA,YAAM,CAAC,IAAI,CAAC,CAAC;AACrB;AACA;AACA,CAAC,CAAC,CAAA;AACF,OAAM;AACN,KAAI;AACJ;AACA,IAAI,OAAO,MAAM,CAAA;AACjB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,YAAY,GAA8B;AACnD,IAAI,OAAO,IAAI,CAAC,oBAAoB,CAA4B,cAAc,CAAC,CAAA;AAC/E,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,cAAc,CAAC,UAAU,GAAY,KAAK,EAAQ;AAC3D;AACA,IAAI,IAAI,UAAU,EAAE;AACpB;AACA,MAAM,OAAO,IAAI,CAAC,UAAU,EAAE,CAAA;AAC9B,KAAI;AACJ;AACA;AACA,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAA;AAC7B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,UAAU,GAAS;AAC5B;AACA,IAAI,MAAM,KAAM,GAAE,IAAI,CAAC,WAAW,EAAE,CAAA;AACpC,IAAI,MAAM,KAAA,GAAQ,KAAK,CAAC,KAAK,CAAA;AAC7B,IAAI,MAAMC,SAAQ,GAAE,KAAK,CAAC,UAAU,EAAE,CAAA;AACtC,IAAI,IAAIA,SAAO,EAAE;AACjB,MAAMC,oBAAY,CAACD,SAAO,CAAC,CAAA;AAC3B,KAAI;AACJ,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAA;AAC7B;AACA;AACA,IAAI,KAAK,CAAC,UAAU,EAAE,CAAA;AACtB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,YAAY,CAAC,OAAO,EAA4B;AACzD;AACA,IAAI,MAAM,EAAE,KAAK,EAAE,MAAA,EAAS,GAAE,IAAI,CAAC,WAAW,EAAE,CAAA;AAChD,IAAI,MAAM,EAAE,OAAO,EAAE,cAAcE,6BAAA,KAAwB,CAAC,UAAU,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,CAAA;AAChG;AACA;AACA,IAAI,MAAM,EAAE,SAAA,EAAY,GAAEC,gBAAU,CAAC,SAAA,IAAa,EAAE,CAAA;AACpD;AACA,IAAI,MAAMH,SAAA,GAAUI,mBAAW,CAAC;AAChC,MAAM,OAAO;AACb,MAAM,WAAW;AACjB,MAAM,IAAI,EAAE,KAAK,CAAC,OAAO,EAAE;AAC3B,MAAM,IAAI,SAAA,IAAa,EAAE,SAAA,EAAW,CAAC;AACrC,MAAM,GAAG,OAAO;AAChB,KAAK,CAAC,CAAA;AACN;AACA;AACA,IAAI,MAAM,cAAe,GAAE,KAAK,CAAC,UAAW,IAAG,KAAK,CAAC,UAAU,EAAE,CAAA;AACjE,IAAI,IAAI,cAAe,IAAG,cAAc,CAAC,MAAA,KAAW,IAAI,EAAE;AAC1D,MAAMC,qBAAa,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,QAAS,EAAC,CAAC,CAAA;AACzD,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,UAAU,EAAE,CAAA;AACrB;AACA;AACA,IAAI,KAAK,CAAC,UAAU,CAACL,SAAO,CAAC,CAAA;AAC7B;AACA,IAAI,OAAOA,SAAO,CAAA;AAClB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,oBAAoB,GAAY;AACzC;AACA,IAAI,MAAM,MAAO,GAAE,IAAI,CAAC,SAAS,EAAE,CAAA;AACnC,IAAI,MAAM,UAAU,MAAA,IAAU,MAAM,CAAC,UAAU,EAAE,CAAA;AACjD,IAAI,OAAO,OAAO,CAAC,OAAA,IAAW,OAAO,CAAC,cAAc,CAAC,CAAA;AACrD,GAAE;AACF;AACA;AACA;AACA;AACA,GAAU,kBAAkB,GAAS;AACrC;AACA,IAAI,MAAM,EAAE,KAAK,EAAE,MAAA,EAAS,GAAE,IAAI,CAAC,WAAW,EAAE,CAAA;AAChD;AACA,IAAI,MAAM,OAAQ,GAAE,KAAK,CAAC,UAAU,EAAE,CAAA;AACtC,IAAI,IAAI,OAAQ,IAAG,UAAU,MAAM,CAAC,cAAc,EAAE;AACpD,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;AACpC,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAU,oBAAoB,CAAI,MAAM,EAAU,GAAG,IAAI,EAAY;AACrE,IAAI,MAAM,OAAA,GAAU,cAAc,EAAE,CAAA;AACpC,IAAI,MAAM,MAAA,GAAS,OAAO,CAAC,UAAU,CAAA;AACrC,IAAI,IAAI,MAAA,IAAU,MAAM,CAAC,UAAW,IAAG,OAAO,MAAM,CAAC,UAAU,CAAC,MAAM,CAAE,KAAI,UAAU,EAAE;AACxF,MAAM,OAAO,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AACxD,KAAI;AACJ,IAAIF,sBAAY,IAAGC,YAAM,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,MAAM,CAAC,kCAAkC,CAAC,CAAC,CAAA;AAC9F,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,GAAY;AAC1C,EAAEI,gBAAU,CAAC,UAAA,GAAaA,gBAAU,CAAC,cAAc;AACnD,IAAI,UAAU,EAAE,EAAE;AAClB,IAAI,GAAG,EAAE,SAAS;AAClB,GAAG,CAAA;AACH,EAAE,OAAOA,gBAAU,CAAA;AACnB,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,GAAG,EAAY;AACxC,EAAE,MAAM,QAAA,GAAW,cAAc,EAAE,CAAA;AACnC,EAAE,MAAM,MAAO,GAAE,iBAAiB,CAAC,QAAQ,CAAC,CAAA;AAC5C,EAAE,eAAe,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;AAChC,EAAE,OAAO,MAAM,CAAA;AACf,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,GAAQ;AACrC;AACA,EAAE,MAAM,QAAA,GAAW,cAAc,EAAE,CAAA;AACnC;AACA,EAAE,IAAI,QAAQ,CAAC,UAAA,IAAc,QAAQ,CAAC,UAAU,CAAC,GAAG,EAAE;AACtD,IAAI,MAAM,GAAI,GAAE,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,CAAA;AACvD;AACA,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,OAAO,GAAG,CAAA;AAChB,KAAI;AACJ,GAAE;AACF;AACA;AACA,EAAE,OAAO,YAAY,CAAC,QAAQ,CAAC,CAAA;AAC/B,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,GAAU;AAC3C;AACA,EAAE,OAAO,aAAa,EAAE,CAAC,iBAAiB,EAAE,CAAA;AAC5C,CAAA;AACA;AACA;AACA,SAAS,YAAY,CAAC,QAAQ,GAAY,cAAc,EAAE,EAAO;AACjE;AACA;AACA,EAAE;AACF,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAE;AAC/B;AACA,IAAI,iBAAiB,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,WAAW,CAAA;AACvD,IAAI;AACJ;AACA,IAAI,eAAe,CAAC,QAAQ,EAAE,IAAI,GAAG,EAAE,CAAC,CAAA;AACxC,GAAE;AACF;AACA;AACA,EAAE,OAAO,iBAAiB,CAAC,QAAQ,CAAC,CAAA;AACpC,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,OAAO,EAAW,MAAM,GAAQ,YAAY,EAAE,EAAQ;AACzF;AACA,EAAE;AACF,IAAI,CAAC,eAAe,CAAC,OAAO,CAAE;AAC9B;AACA,IAAI,iBAAiB,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,WAAW,CAAA;AACtD,IAAI;AACJ;AACA,IAAI,MAAM,MAAO,GAAE,MAAM,CAAC,SAAS,EAAE,CAAA;AACrC;AACA,IAAI,MAAM,KAAM,GAAE,MAAM,CAAC,QAAQ,EAAE,CAAA;AACnC;AACA,IAAI,MAAM,cAAe,GAAE,MAAM,CAAC,iBAAiB,EAAE,CAAA;AACrD;AACA,IAAI,eAAe,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,EAAE,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;AACpF,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,uBAAuB,CAAC,QAAQ,EAA0C;AAC1F;AACA,EAAE,MAAM,QAAA,GAAW,cAAc,EAAE,CAAA;AACnC,EAAE,QAAQ,CAAC,UAAW,GAAE,QAAQ,CAAC,UAAA,IAAc,EAAE,CAAA;AACjD,EAAE,QAAQ,CAAC,UAAU,CAAC,GAAA,GAAM,QAAQ,CAAA;AACpC,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAI,QAAQ,EAAW,OAAO,GAA+B,EAAE,EAAK;AACvG,EAAE,MAAM,QAAA,GAAW,cAAc,EAAE,CAAA;AACnC;AACA,EAAE,IAAI,QAAQ,CAAC,UAAA,IAAc,QAAQ,CAAC,UAAU,CAAC,GAAG,EAAE;AACtD,IAAI,OAAO,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;AACzE,GAAE;AACF;AACA;AACA,EAAE,OAAO,QAAQ,EAAE,CAAA;AACnB,CAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,OAAO,EAAoB;AACpD,EAAE,OAAO,CAAC,EAAE,WAAW,OAAO,CAAC,UAAA,IAAc,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;AACpE,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,OAAO,EAAgB;AACzD;AACA,EAAE,OAAOG,wBAAkB,CAAM,KAAK,EAAE,MAAM,IAAI,GAAG,EAAE,EAAE,OAAO,CAAC,CAAA;AACjE,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe,CAAC,OAAO,EAAW,GAAG,EAAgB;AACrE,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,CAAA;AAC5B,EAAE,MAAM,UAAA,IAAc,OAAO,CAAC,UAAA,GAAa,OAAO,CAAC,UAAA,IAAc,EAAE,CAAC,CAAA;AACpE,EAAE,UAAU,CAAC,GAAI,GAAE,GAAG,CAAA;AACtB,EAAE,OAAO,IAAI,CAAA;AACb;;;;;;;;;;;;;;"}