Object.defineProperty(exports, '__esModule', { value: true });

const hubextensions = require('./tracing/hubextensions.js');
const idletransaction = require('./tracing/idletransaction.js');
const span$1 = require('./tracing/span.js');
const transaction = require('./tracing/transaction.js');
const utils = require('./tracing/utils.js');
const spanstatus = require('./tracing/spanstatus.js');
const trace = require('./tracing/trace.js');
const dynamicSamplingContext = require('./tracing/dynamicSamplingContext.js');
const measurement = require('./tracing/measurement.js');
const sampling = require('./tracing/sampling.js');
const semanticAttributes = require('./semanticAttributes.js');
const envelope = require('./envelope.js');
const exports$1 = require('./exports.js');
const hub = require('./hub.js');
const session = require('./session.js');
const sessionflusher = require('./sessionflusher.js');
const scope = require('./scope.js');
const eventProcessors = require('./eventProcessors.js');
const api = require('./api.js');
const baseclient = require('./baseclient.js');
const serverRuntimeClient = require('./server-runtime-client.js');
const sdk = require('./sdk.js');
const base = require('./transports/base.js');
const offline = require('./transports/offline.js');
const multiplexed = require('./transports/multiplexed.js');
const version = require('./version.js');
const integration = require('./integration.js');
const applyScopeDataToEvent = require('./utils/applyScopeDataToEvent.js');
const prepareEvent = require('./utils/prepareEvent.js');
const checkin = require('./checkin.js');
const span = require('./span.js');
const hasTracingEnabled = require('./utils/hasTracingEnabled.js');
const isSentryRequestUrl = require('./utils/isSentryRequestUrl.js');
const handleCallbackErrors = require('./utils/handleCallbackErrors.js');
const parameterize = require('./utils/parameterize.js');
const spanUtils = require('./utils/spanUtils.js');
const getRootSpan = require('./utils/getRootSpan.js');
const sdkMetadata = require('./utils/sdkMetadata.js');
const constants = require('./constants.js');
const metadata = require('./integrations/metadata.js');
const requestdata = require('./integrations/requestdata.js');
const inboundfilters = require('./integrations/inboundfilters.js');
const functiontostring = require('./integrations/functiontostring.js');
const linkederrors = require('./integrations/linkederrors.js');
const index = require('./integrations/index.js');
const exports$2 = require('./metrics/exports.js');

/** @deprecated Import the integration function directly, e.g. `inboundFiltersIntegration()` instead of `new Integrations.InboundFilter(). */
const Integrations = index;

exports.addTracingExtensions = hubextensions.addTracingExtensions;
exports.startIdleTransaction = hubextensions.startIdleTransaction;
exports.IdleTransaction = idletransaction.IdleTransaction;
exports.TRACING_DEFAULTS = idletransaction.TRACING_DEFAULTS;
exports.Span = span$1.Span;
exports.Transaction = transaction.Transaction;
exports.extractTraceparentData = utils.extractTraceparentData;
exports.getActiveTransaction = utils.getActiveTransaction;
Object.defineProperty(exports, 'SpanStatus', {
  enumerable: true,
  get: () => spanstatus.SpanStatus
});
exports.getSpanStatusFromHttpCode = spanstatus.getSpanStatusFromHttpCode;
exports.setHttpStatus = spanstatus.setHttpStatus;
exports.spanStatusfromHttpCode = spanstatus.spanStatusfromHttpCode;
exports.continueTrace = trace.continueTrace;
exports.getActiveSpan = trace.getActiveSpan;
exports.startActiveSpan = trace.startActiveSpan;
exports.startInactiveSpan = trace.startInactiveSpan;
exports.startSpan = trace.startSpan;
exports.startSpanManual = trace.startSpanManual;
exports.trace = trace.trace;
exports.getDynamicSamplingContextFromClient = dynamicSamplingContext.getDynamicSamplingContextFromClient;
exports.getDynamicSamplingContextFromSpan = dynamicSamplingContext.getDynamicSamplingContextFromSpan;
exports.setMeasurement = measurement.setMeasurement;
exports.isValidSampleRate = sampling.isValidSampleRate;
exports.SEMANTIC_ATTRIBUTE_PROFILE_ID = semanticAttributes.SEMANTIC_ATTRIBUTE_PROFILE_ID;
exports.SEMANTIC_ATTRIBUTE_SENTRY_OP = semanticAttributes.SEMANTIC_ATTRIBUTE_SENTRY_OP;
exports.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN = semanticAttributes.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN;
exports.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE = semanticAttributes.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE;
exports.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE = semanticAttributes.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE;
exports.createEventEnvelope = envelope.createEventEnvelope;
exports.createSessionEnvelope = envelope.createSessionEnvelope;
exports.addBreadcrumb = exports$1.addBreadcrumb;
exports.captureCheckIn = exports$1.captureCheckIn;
exports.captureEvent = exports$1.captureEvent;
exports.captureException = exports$1.captureException;
exports.captureMessage = exports$1.captureMessage;
exports.captureSession = exports$1.captureSession;
exports.close = exports$1.close;
exports.configureScope = exports$1.configureScope;
exports.endSession = exports$1.endSession;
exports.flush = exports$1.flush;
exports.getClient = exports$1.getClient;
exports.getCurrentScope = exports$1.getCurrentScope;
exports.isInitialized = exports$1.isInitialized;
exports.lastEventId = exports$1.lastEventId;
exports.setContext = exports$1.setContext;
exports.setExtra = exports$1.setExtra;
exports.setExtras = exports$1.setExtras;
exports.setTag = exports$1.setTag;
exports.setTags = exports$1.setTags;
exports.setUser = exports$1.setUser;
exports.startSession = exports$1.startSession;
exports.startTransaction = exports$1.startTransaction;
exports.withActiveSpan = exports$1.withActiveSpan;
exports.withIsolationScope = exports$1.withIsolationScope;
exports.withMonitor = exports$1.withMonitor;
exports.withScope = exports$1.withScope;
exports.Hub = hub.Hub;
exports.ensureHubOnCarrier = hub.ensureHubOnCarrier;
exports.getCurrentHub = hub.getCurrentHub;
exports.getHubFromCarrier = hub.getHubFromCarrier;
exports.getIsolationScope = hub.getIsolationScope;
exports.getMainCarrier = hub.getMainCarrier;
exports.makeMain = hub.makeMain;
exports.runWithAsyncContext = hub.runWithAsyncContext;
exports.setAsyncContextStrategy = hub.setAsyncContextStrategy;
exports.setHubOnCarrier = hub.setHubOnCarrier;
exports.closeSession = session.closeSession;
exports.makeSession = session.makeSession;
exports.updateSession = session.updateSession;
exports.SessionFlusher = sessionflusher.SessionFlusher;
exports.Scope = scope.Scope;
exports.getGlobalScope = scope.getGlobalScope;
exports.setGlobalScope = scope.setGlobalScope;
exports.addGlobalEventProcessor = eventProcessors.addGlobalEventProcessor;
exports.notifyEventProcessors = eventProcessors.notifyEventProcessors;
exports.getEnvelopeEndpointWithUrlEncodedAuth = api.getEnvelopeEndpointWithUrlEncodedAuth;
exports.getReportDialogEndpoint = api.getReportDialogEndpoint;
exports.BaseClient = baseclient.BaseClient;
exports.addEventProcessor = baseclient.addEventProcessor;
exports.ServerRuntimeClient = serverRuntimeClient.ServerRuntimeClient;
exports.initAndBind = sdk.initAndBind;
exports.setCurrentClient = sdk.setCurrentClient;
exports.createTransport = base.createTransport;
exports.makeOfflineTransport = offline.makeOfflineTransport;
exports.makeMultiplexedTransport = multiplexed.makeMultiplexedTransport;
exports.SDK_VERSION = version.SDK_VERSION;
exports.addIntegration = integration.addIntegration;
exports.convertIntegrationFnToClass = integration.convertIntegrationFnToClass;
exports.defineIntegration = integration.defineIntegration;
exports.getIntegrationsToSetup = integration.getIntegrationsToSetup;
exports.applyScopeDataToEvent = applyScopeDataToEvent.applyScopeDataToEvent;
exports.mergeScopeData = applyScopeDataToEvent.mergeScopeData;
exports.prepareEvent = prepareEvent.prepareEvent;
exports.createCheckInEnvelope = checkin.createCheckInEnvelope;
exports.createSpanEnvelope = span.createSpanEnvelope;
exports.hasTracingEnabled = hasTracingEnabled.hasTracingEnabled;
exports.isSentryRequestUrl = isSentryRequestUrl.isSentryRequestUrl;
exports.handleCallbackErrors = handleCallbackErrors.handleCallbackErrors;
exports.parameterize = parameterize.parameterize;
exports.spanIsSampled = spanUtils.spanIsSampled;
exports.spanToJSON = spanUtils.spanToJSON;
exports.spanToTraceContext = spanUtils.spanToTraceContext;
exports.spanToTraceHeader = spanUtils.spanToTraceHeader;
exports.getRootSpan = getRootSpan.getRootSpan;
exports.applySdkMetadata = sdkMetadata.applySdkMetadata;
exports.DEFAULT_ENVIRONMENT = constants.DEFAULT_ENVIRONMENT;
exports.ModuleMetadata = metadata.ModuleMetadata;
exports.moduleMetadataIntegration = metadata.moduleMetadataIntegration;
exports.RequestData = requestdata.RequestData;
exports.requestDataIntegration = requestdata.requestDataIntegration;
exports.InboundFilters = inboundfilters.InboundFilters;
exports.inboundFiltersIntegration = inboundfilters.inboundFiltersIntegration;
exports.FunctionToString = functiontostring.FunctionToString;
exports.functionToStringIntegration = functiontostring.functionToStringIntegration;
exports.LinkedErrors = linkederrors.LinkedErrors;
exports.linkedErrorsIntegration = linkederrors.linkedErrorsIntegration;
exports.metrics = exports$2.metrics;
exports.Integrations = Integrations;
//# sourceMappingURL=index.js.map
