{"version": 3, "file": "scope.js", "sources": ["../../src/scope.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport type {\n  Attachment,\n  Breadcrumb,\n  CaptureContext,\n  Client,\n  Context,\n  Contexts,\n  Event,\n  EventHint,\n  EventProcessor,\n  Extra,\n  Extras,\n  Primitive,\n  PropagationContext,\n  RequestSession,\n  Scope as ScopeInterface,\n  ScopeContext,\n  ScopeData,\n  Session,\n  Severity,\n  SeverityLevel,\n  Span,\n  Transaction,\n  User,\n} from '@sentry/types';\nimport { dateTimestampInSeconds, isPlainObject, logger, uuid4 } from '@sentry/utils';\n\nimport { getGlobalEventProcessors, notifyEventProcessors } from './eventProcessors';\nimport { updateSession } from './session';\nimport { applyScopeDataToEvent } from './utils/applyScopeDataToEvent';\n\n/**\n * Default value for maximum number of breadcrumbs added to an event.\n */\nconst DEFAULT_MAX_BREADCRUMBS = 100;\n\n/**\n * The global scope is kept in this module.\n * When accessing this via `getGlobalScope()` we'll make sure to set one if none is currently present.\n */\nlet globalScope: ScopeInterface | undefined;\n\n/**\n * Holds additional event information. {@link Scope.applyToEvent} will be\n * called by the client before an event will be sent.\n */\nexport class Scope implements ScopeInterface {\n  /** Flag if notifying is happening. */\n  protected _notifyingListeners: boolean;\n\n  /** Callback for client to receive scope changes. */\n  protected _scopeListeners: Array<(scope: Scope) => void>;\n\n  /** Callback list that will be called after {@link applyToEvent}. */\n  protected _eventProcessors: EventProcessor[];\n\n  /** Array of breadcrumbs. */\n  protected _breadcrumbs: Breadcrumb[];\n\n  /** User */\n  protected _user: User;\n\n  /** Tags */\n  protected _tags: { [key: string]: Primitive };\n\n  /** Extra */\n  protected _extra: Extras;\n\n  /** Contexts */\n  protected _contexts: Contexts;\n\n  /** Attachments */\n  protected _attachments: Attachment[];\n\n  /** Propagation Context for distributed tracing */\n  protected _propagationContext: PropagationContext;\n\n  /**\n   * A place to stash data which is needed at some point in the SDK's event processing pipeline but which shouldn't get\n   * sent to Sentry\n   */\n  protected _sdkProcessingMetadata: { [key: string]: unknown };\n\n  /** Fingerprint */\n  protected _fingerprint?: string[];\n\n  /** Severity */\n  // eslint-disable-next-line deprecation/deprecation\n  protected _level?: Severity | SeverityLevel;\n\n  /**\n   * Transaction Name\n   */\n  protected _transactionName?: string;\n\n  /** Span */\n  protected _span?: Span;\n\n  /** Session */\n  protected _session?: Session;\n\n  /** Request Mode Session Status */\n  protected _requestSession?: RequestSession;\n\n  /** The client on this scope */\n  protected _client?: Client;\n\n  // NOTE: Any field which gets added here should get added not only to the constructor but also to the `clone` method.\n\n  public constructor() {\n    this._notifyingListeners = false;\n    this._scopeListeners = [];\n    this._eventProcessors = [];\n    this._breadcrumbs = [];\n    this._attachments = [];\n    this._user = {};\n    this._tags = {};\n    this._extra = {};\n    this._contexts = {};\n    this._sdkProcessingMetadata = {};\n    this._propagationContext = generatePropagationContext();\n  }\n\n  /**\n   * Inherit values from the parent scope.\n   * @deprecated Use `scope.clone()` and `new Scope()` instead.\n   */\n  public static clone(scope?: Scope): Scope {\n    return scope ? scope.clone() : new Scope();\n  }\n\n  /**\n   * Clone this scope instance.\n   */\n  public clone(): Scope {\n    const newScope = new Scope();\n    newScope._breadcrumbs = [...this._breadcrumbs];\n    newScope._tags = { ...this._tags };\n    newScope._extra = { ...this._extra };\n    newScope._contexts = { ...this._contexts };\n    newScope._user = this._user;\n    newScope._level = this._level;\n    newScope._span = this._span;\n    newScope._session = this._session;\n    newScope._transactionName = this._transactionName;\n    newScope._fingerprint = this._fingerprint;\n    newScope._eventProcessors = [...this._eventProcessors];\n    newScope._requestSession = this._requestSession;\n    newScope._attachments = [...this._attachments];\n    newScope._sdkProcessingMetadata = { ...this._sdkProcessingMetadata };\n    newScope._propagationContext = { ...this._propagationContext };\n    newScope._client = this._client;\n\n    return newScope;\n  }\n\n  /** Update the client on the scope. */\n  public setClient(client: Client | undefined): void {\n    this._client = client;\n  }\n\n  /**\n   * Get the client assigned to this scope.\n   *\n   * It is generally recommended to use the global function `Sentry.getClient()` instead, unless you know what you are doing.\n   */\n  public getClient(): Client | undefined {\n    return this._client;\n  }\n\n  /**\n   * Add internal on change listener. Used for sub SDKs that need to store the scope.\n   * @hidden\n   */\n  public addScopeListener(callback: (scope: Scope) => void): void {\n    this._scopeListeners.push(callback);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addEventProcessor(callback: EventProcessor): this {\n    this._eventProcessors.push(callback);\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setUser(user: User | null): this {\n    // If null is passed we want to unset everything, but still define keys,\n    // so that later down in the pipeline any existing values are cleared.\n    this._user = user || {\n      email: undefined,\n      id: undefined,\n      ip_address: undefined,\n      segment: undefined,\n      username: undefined,\n    };\n\n    if (this._session) {\n      updateSession(this._session, { user });\n    }\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getUser(): User | undefined {\n    return this._user;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getRequestSession(): RequestSession | undefined {\n    return this._requestSession;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setRequestSession(requestSession?: RequestSession): this {\n    this._requestSession = requestSession;\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTags(tags: { [key: string]: Primitive }): this {\n    this._tags = {\n      ...this._tags,\n      ...tags,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTag(key: string, value: Primitive): this {\n    this._tags = { ...this._tags, [key]: value };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtras(extras: Extras): this {\n    this._extra = {\n      ...this._extra,\n      ...extras,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtra(key: string, extra: Extra): this {\n    this._extra = { ...this._extra, [key]: extra };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setFingerprint(fingerprint: string[]): this {\n    this._fingerprint = fingerprint;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setLevel(\n    // eslint-disable-next-line deprecation/deprecation\n    level: Severity | SeverityLevel,\n  ): this {\n    this._level = level;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets the transaction name on the scope for future events.\n   */\n  public setTransactionName(name?: string): this {\n    this._transactionName = name;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setContext(key: string, context: Context | null): this {\n    if (context === null) {\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this._contexts[key];\n    } else {\n      this._contexts[key] = context;\n    }\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets the Span on the scope.\n   * @param span Span\n   * @deprecated Instead of setting a span on a scope, use `startSpan()`/`startSpanManual()` instead.\n   */\n  public setSpan(span?: Span): this {\n    this._span = span;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Returns the `Span` if there is one.\n   * @deprecated Use `getActiveSpan()` instead.\n   */\n  public getSpan(): Span | undefined {\n    return this._span;\n  }\n\n  /**\n   * Returns the `Transaction` attached to the scope (if there is one).\n   * @deprecated You should not rely on the transaction, but just use `startSpan()` APIs instead.\n   */\n  public getTransaction(): Transaction | undefined {\n    // Often, this span (if it exists at all) will be a transaction, but it's not guaranteed to be. Regardless, it will\n    // have a pointer to the currently-active transaction.\n    const span = this._span;\n    // Cannot replace with getRootSpan because getRootSpan returns a span, not a transaction\n    // Also, this method will be removed anyway.\n    // eslint-disable-next-line deprecation/deprecation\n    return span && span.transaction;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setSession(session?: Session): this {\n    if (!session) {\n      delete this._session;\n    } else {\n      this._session = session;\n    }\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getSession(): Session | undefined {\n    return this._session;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public update(captureContext?: CaptureContext): this {\n    if (!captureContext) {\n      return this;\n    }\n\n    const scopeToMerge = typeof captureContext === 'function' ? captureContext(this) : captureContext;\n\n    if (scopeToMerge instanceof Scope) {\n      const scopeData = scopeToMerge.getScopeData();\n\n      this._tags = { ...this._tags, ...scopeData.tags };\n      this._extra = { ...this._extra, ...scopeData.extra };\n      this._contexts = { ...this._contexts, ...scopeData.contexts };\n      if (scopeData.user && Object.keys(scopeData.user).length) {\n        this._user = scopeData.user;\n      }\n      if (scopeData.level) {\n        this._level = scopeData.level;\n      }\n      if (scopeData.fingerprint.length) {\n        this._fingerprint = scopeData.fingerprint;\n      }\n      if (scopeToMerge.getRequestSession()) {\n        this._requestSession = scopeToMerge.getRequestSession();\n      }\n      if (scopeData.propagationContext) {\n        this._propagationContext = scopeData.propagationContext;\n      }\n    } else if (isPlainObject(scopeToMerge)) {\n      const scopeContext = captureContext as ScopeContext;\n      this._tags = { ...this._tags, ...scopeContext.tags };\n      this._extra = { ...this._extra, ...scopeContext.extra };\n      this._contexts = { ...this._contexts, ...scopeContext.contexts };\n      if (scopeContext.user) {\n        this._user = scopeContext.user;\n      }\n      if (scopeContext.level) {\n        this._level = scopeContext.level;\n      }\n      if (scopeContext.fingerprint) {\n        this._fingerprint = scopeContext.fingerprint;\n      }\n      if (scopeContext.requestSession) {\n        this._requestSession = scopeContext.requestSession;\n      }\n      if (scopeContext.propagationContext) {\n        this._propagationContext = scopeContext.propagationContext;\n      }\n    }\n\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clear(): this {\n    this._breadcrumbs = [];\n    this._tags = {};\n    this._extra = {};\n    this._user = {};\n    this._contexts = {};\n    this._level = undefined;\n    this._transactionName = undefined;\n    this._fingerprint = undefined;\n    this._requestSession = undefined;\n    this._span = undefined;\n    this._session = undefined;\n    this._notifyScopeListeners();\n    this._attachments = [];\n    this._propagationContext = generatePropagationContext();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addBreadcrumb(breadcrumb: Breadcrumb, maxBreadcrumbs?: number): this {\n    const maxCrumbs = typeof maxBreadcrumbs === 'number' ? maxBreadcrumbs : DEFAULT_MAX_BREADCRUMBS;\n\n    // No data has been changed, so don't notify scope listeners\n    if (maxCrumbs <= 0) {\n      return this;\n    }\n\n    const mergedBreadcrumb = {\n      timestamp: dateTimestampInSeconds(),\n      ...breadcrumb,\n    };\n\n    const breadcrumbs = this._breadcrumbs;\n    breadcrumbs.push(mergedBreadcrumb);\n    this._breadcrumbs = breadcrumbs.length > maxCrumbs ? breadcrumbs.slice(-maxCrumbs) : breadcrumbs;\n\n    this._notifyScopeListeners();\n\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getLastBreadcrumb(): Breadcrumb | undefined {\n    return this._breadcrumbs[this._breadcrumbs.length - 1];\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clearBreadcrumbs(): this {\n    this._breadcrumbs = [];\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addAttachment(attachment: Attachment): this {\n    this._attachments.push(attachment);\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   * @deprecated Use `getScopeData()` instead.\n   */\n  public getAttachments(): Attachment[] {\n    const data = this.getScopeData();\n\n    return data.attachments;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clearAttachments(): this {\n    this._attachments = [];\n    return this;\n  }\n\n  /** @inheritDoc */\n  public getScopeData(): ScopeData {\n    const {\n      _breadcrumbs,\n      _attachments,\n      _contexts,\n      _tags,\n      _extra,\n      _user,\n      _level,\n      _fingerprint,\n      _eventProcessors,\n      _propagationContext,\n      _sdkProcessingMetadata,\n      _transactionName,\n      _span,\n    } = this;\n\n    return {\n      breadcrumbs: _breadcrumbs,\n      attachments: _attachments,\n      contexts: _contexts,\n      tags: _tags,\n      extra: _extra,\n      user: _user,\n      level: _level,\n      fingerprint: _fingerprint || [],\n      eventProcessors: _eventProcessors,\n      propagationContext: _propagationContext,\n      sdkProcessingMetadata: _sdkProcessingMetadata,\n      transactionName: _transactionName,\n      span: _span,\n    };\n  }\n\n  /**\n   * Applies data from the scope to the event and runs all event processors on it.\n   *\n   * @param event Event\n   * @param hint Object containing additional information about the original exception, for use by the event processors.\n   * @hidden\n   * @deprecated Use `applyScopeDataToEvent()` directly\n   */\n  public applyToEvent(\n    event: Event,\n    hint: EventHint = {},\n    additionalEventProcessors: EventProcessor[] = [],\n  ): PromiseLike<Event | null> {\n    applyScopeDataToEvent(event, this.getScopeData());\n\n    // TODO (v8): Update this order to be: Global > Client > Scope\n    const eventProcessors: EventProcessor[] = [\n      ...additionalEventProcessors,\n      // eslint-disable-next-line deprecation/deprecation\n      ...getGlobalEventProcessors(),\n      ...this._eventProcessors,\n    ];\n\n    return notifyEventProcessors(eventProcessors, event, hint);\n  }\n\n  /**\n   * Add data which will be accessible during event processing but won't get sent to Sentry\n   */\n  public setSDKProcessingMetadata(newData: { [key: string]: unknown }): this {\n    this._sdkProcessingMetadata = { ...this._sdkProcessingMetadata, ...newData };\n\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setPropagationContext(context: PropagationContext): this {\n    this._propagationContext = context;\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getPropagationContext(): PropagationContext {\n    return this._propagationContext;\n  }\n\n  /**\n   * Capture an exception for this scope.\n   *\n   * @param exception The exception to capture.\n   * @param hint Optinal additional data to attach to the Sentry event.\n   * @returns the id of the captured Sentry event.\n   */\n  public captureException(exception: unknown, hint?: EventHint): string {\n    const eventId = hint && hint.event_id ? hint.event_id : uuid4();\n\n    if (!this._client) {\n      logger.warn('No client configured on scope - will not capture exception!');\n      return eventId;\n    }\n\n    const syntheticException = new Error('Sentry syntheticException');\n\n    this._client.captureException(\n      exception,\n      {\n        originalException: exception,\n        syntheticException,\n        ...hint,\n        event_id: eventId,\n      },\n      this,\n    );\n\n    return eventId;\n  }\n\n  /**\n   * Capture a message for this scope.\n   *\n   * @param message The message to capture.\n   * @param level An optional severity level to report the message with.\n   * @param hint Optional additional data to attach to the Sentry event.\n   * @returns the id of the captured message.\n   */\n  public captureMessage(message: string, level?: SeverityLevel, hint?: EventHint): string {\n    const eventId = hint && hint.event_id ? hint.event_id : uuid4();\n\n    if (!this._client) {\n      logger.warn('No client configured on scope - will not capture message!');\n      return eventId;\n    }\n\n    const syntheticException = new Error(message);\n\n    this._client.captureMessage(\n      message,\n      level,\n      {\n        originalException: message,\n        syntheticException,\n        ...hint,\n        event_id: eventId,\n      },\n      this,\n    );\n\n    return eventId;\n  }\n\n  /**\n   * Captures a manually created event for this scope and sends it to Sentry.\n   *\n   * @param exception The event to capture.\n   * @param hint Optional additional data to attach to the Sentry event.\n   * @returns the id of the captured event.\n   */\n  public captureEvent(event: Event, hint?: EventHint): string {\n    const eventId = hint && hint.event_id ? hint.event_id : uuid4();\n\n    if (!this._client) {\n      logger.warn('No client configured on scope - will not capture event!');\n      return eventId;\n    }\n\n    this._client.captureEvent(event, { ...hint, event_id: eventId }, this);\n\n    return eventId;\n  }\n\n  /**\n   * This will be called on every set call.\n   */\n  protected _notifyScopeListeners(): void {\n    // We need this check for this._notifyingListeners to be able to work on scope during updates\n    // If this check is not here we'll produce endless recursion when something is done with the scope\n    // during the callback.\n    if (!this._notifyingListeners) {\n      this._notifyingListeners = true;\n      this._scopeListeners.forEach(callback => {\n        callback(this);\n      });\n      this._notifyingListeners = false;\n    }\n  }\n}\n\n/**\n * Get the global scope.\n * This scope is applied to _all_ events.\n */\nexport function getGlobalScope(): ScopeInterface {\n  if (!globalScope) {\n    globalScope = new Scope();\n  }\n\n  return globalScope;\n}\n\n/**\n * This is mainly needed for tests.\n * DO NOT USE this, as this is an internal API and subject to change.\n * @hidden\n */\nexport function setGlobalScope(scope: ScopeInterface | undefined): void {\n  globalScope = scope;\n}\n\nfunction generatePropagationContext(): PropagationContext {\n  return {\n    traceId: uuid4(),\n    spanId: uuid4().substring(16),\n  };\n}\n"], "names": [], "mappings": ";;;;;AAgCA;AACA;AACA;AACA,MAAM,uBAAA,GAA0B,GAAG,CAAA;AACnC;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAA;AACf;AACA;AACA;AACA;AACA;AACO,MAAM,OAAgC;AAC7C;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;AACA;AACA;AACA;;AAGA;;AAGA;AACA;;AAGA;AACA;AACA;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;AACA;AACA,GAAS,WAAW,GAAG;AACvB,IAAI,IAAI,CAAC,mBAAoB,GAAE,KAAK,CAAA;AACpC,IAAI,IAAI,CAAC,eAAgB,GAAE,EAAE,CAAA;AAC7B,IAAI,IAAI,CAAC,gBAAiB,GAAE,EAAE,CAAA;AAC9B,IAAI,IAAI,CAAC,YAAa,GAAE,EAAE,CAAA;AAC1B,IAAI,IAAI,CAAC,YAAa,GAAE,EAAE,CAAA;AAC1B,IAAI,IAAI,CAAC,KAAM,GAAE,EAAE,CAAA;AACnB,IAAI,IAAI,CAAC,KAAM,GAAE,EAAE,CAAA;AACnB,IAAI,IAAI,CAAC,MAAO,GAAE,EAAE,CAAA;AACpB,IAAI,IAAI,CAAC,SAAU,GAAE,EAAE,CAAA;AACvB,IAAI,IAAI,CAAC,sBAAuB,GAAE,EAAE,CAAA;AACpC,IAAI,IAAI,CAAC,mBAAA,GAAsB,0BAA0B,EAAE,CAAA;AAC3D,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,OAAO,KAAK,CAAC,KAAK,EAAiB;AAC5C,IAAI,OAAO,KAAA,GAAQ,KAAK,CAAC,KAAK,EAAC,GAAI,IAAI,KAAK,EAAE,CAAA;AAC9C,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,KAAK,GAAU;AACxB,IAAI,MAAM,QAAS,GAAE,IAAI,KAAK,EAAE,CAAA;AAChC,IAAI,QAAQ,CAAC,YAAa,GAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAA;AAClD,IAAI,QAAQ,CAAC,KAAM,GAAE,EAAE,GAAG,IAAI,CAAC,KAAA,EAAO,CAAA;AACtC,IAAI,QAAQ,CAAC,MAAO,GAAE,EAAE,GAAG,IAAI,CAAC,MAAA,EAAQ,CAAA;AACxC,IAAI,QAAQ,CAAC,SAAU,GAAE,EAAE,GAAG,IAAI,CAAC,SAAA,EAAW,CAAA;AAC9C,IAAI,QAAQ,CAAC,KAAA,GAAQ,IAAI,CAAC,KAAK,CAAA;AAC/B,IAAI,QAAQ,CAAC,MAAA,GAAS,IAAI,CAAC,MAAM,CAAA;AACjC,IAAI,QAAQ,CAAC,KAAA,GAAQ,IAAI,CAAC,KAAK,CAAA;AAC/B,IAAI,QAAQ,CAAC,QAAA,GAAW,IAAI,CAAC,QAAQ,CAAA;AACrC,IAAI,QAAQ,CAAC,gBAAA,GAAmB,IAAI,CAAC,gBAAgB,CAAA;AACrD,IAAI,QAAQ,CAAC,YAAA,GAAe,IAAI,CAAC,YAAY,CAAA;AAC7C,IAAI,QAAQ,CAAC,gBAAiB,GAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAA;AAC1D,IAAI,QAAQ,CAAC,eAAA,GAAkB,IAAI,CAAC,eAAe,CAAA;AACnD,IAAI,QAAQ,CAAC,YAAa,GAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAA;AAClD,IAAI,QAAQ,CAAC,sBAAuB,GAAE,EAAE,GAAG,IAAI,CAAC,sBAAA,EAAwB,CAAA;AACxE,IAAI,QAAQ,CAAC,mBAAoB,GAAE,EAAE,GAAG,IAAI,CAAC,mBAAA,EAAqB,CAAA;AAClE,IAAI,QAAQ,CAAC,OAAA,GAAU,IAAI,CAAC,OAAO,CAAA;AACnC;AACA,IAAI,OAAO,QAAQ,CAAA;AACnB,GAAE;AACF;AACA;AACA,GAAS,SAAS,CAAC,MAAM,EAA4B;AACrD,IAAI,IAAI,CAAC,OAAQ,GAAE,MAAM,CAAA;AACzB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,SAAS,GAAuB;AACzC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAA;AACvB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,gBAAgB,CAAC,QAAQ,EAAgC;AAClE,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;AACvC,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,iBAAiB,CAAC,QAAQ,EAAwB;AAC3D,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;AACxC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,OAAO,CAAC,IAAI,EAAqB;AAC1C;AACA;AACA,IAAI,IAAI,CAAC,KAAM,GAAE,QAAQ;AACzB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,EAAE,EAAE,SAAS;AACnB,MAAM,UAAU,EAAE,SAAS;AAC3B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,SAAS;AACzB,KAAK,CAAA;AACL;AACA,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvB,MAAM,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAK,EAAC,CAAC,CAAA;AAC5C,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;AAChC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,OAAO,GAAqB;AACrC,IAAI,OAAO,IAAI,CAAC,KAAK,CAAA;AACrB,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,iBAAiB,GAA+B;AACzD,IAAI,OAAO,IAAI,CAAC,eAAe,CAAA;AAC/B,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,iBAAiB,CAAC,cAAc,EAAyB;AAClE,IAAI,IAAI,CAAC,eAAgB,GAAE,cAAc,CAAA;AACzC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,OAAO,CAAC,IAAI,EAAsC;AAC3D,IAAI,IAAI,CAAC,KAAA,GAAQ;AACjB,MAAM,GAAG,IAAI,CAAC,KAAK;AACnB,MAAM,GAAG,IAAI;AACb,KAAK,CAAA;AACL,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;AAChC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,MAAM,CAAC,GAAG,EAAU,KAAK,EAAmB;AACrD,IAAI,IAAI,CAAC,KAAM,GAAE,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,OAAO,CAAA;AAChD,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;AAChC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,SAAS,CAAC,MAAM,EAAgB;AACzC,IAAI,IAAI,CAAC,MAAA,GAAS;AAClB,MAAM,GAAG,IAAI,CAAC,MAAM;AACpB,MAAM,GAAG,MAAM;AACf,KAAK,CAAA;AACL,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;AAChC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,QAAQ,CAAC,GAAG,EAAU,KAAK,EAAe;AACnD,IAAI,IAAI,CAAC,MAAO,GAAE,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,OAAO,CAAA;AAClD,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;AAChC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,cAAc,CAAC,WAAW,EAAkB;AACrD,IAAI,IAAI,CAAC,YAAa,GAAE,WAAW,CAAA;AACnC,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;AAChC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,QAAQ;AACjB;AACA,IAAI,KAAK;AACT,IAAU;AACV,IAAI,IAAI,CAAC,MAAO,GAAE,KAAK,CAAA;AACvB,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;AAChC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,kBAAkB,CAAC,IAAI,EAAiB;AACjD,IAAI,IAAI,CAAC,gBAAiB,GAAE,IAAI,CAAA;AAChC,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;AAChC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,UAAU,CAAC,GAAG,EAAU,OAAO,EAAwB;AAChE,IAAI,IAAI,OAAQ,KAAI,IAAI,EAAE;AAC1B;AACA,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;AAChC,WAAW;AACX,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAA,GAAI,OAAO,CAAA;AACnC,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;AAChC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,OAAO,CAAC,IAAI,EAAe;AACpC,IAAI,IAAI,CAAC,KAAM,GAAE,IAAI,CAAA;AACrB,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;AAChC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,OAAO,GAAqB;AACrC,IAAI,OAAO,IAAI,CAAC,KAAK,CAAA;AACrB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,cAAc,GAA4B;AACnD;AACA;AACA,IAAI,MAAM,IAAA,GAAO,IAAI,CAAC,KAAK,CAAA;AAC3B;AACA;AACA;AACA,IAAI,OAAO,IAAA,IAAQ,IAAI,CAAC,WAAW,CAAA;AACnC,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,UAAU,CAAC,OAAO,EAAkB;AAC7C,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAA;AAC1B,WAAW;AACX,MAAM,IAAI,CAAC,QAAS,GAAE,OAAO,CAAA;AAC7B,KAAI;AACJ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;AAChC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,UAAU,GAAwB;AAC3C,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAA;AACxB,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,MAAM,CAAC,cAAc,EAAyB;AACvD,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,OAAO,IAAI,CAAA;AACjB,KAAI;AACJ;AACA,IAAI,MAAM,YAAA,GAAe,OAAO,cAAe,KAAI,UAAW,GAAE,cAAc,CAAC,IAAI,CAAA,GAAI,cAAc,CAAA;AACrG;AACA,IAAI,IAAI,YAAa,YAAW,KAAK,EAAE;AACvC,MAAM,MAAM,SAAU,GAAE,YAAY,CAAC,YAAY,EAAE,CAAA;AACnD;AACA,MAAM,IAAI,CAAC,KAAM,GAAE,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,SAAS,CAAC,MAAM,CAAA;AACvD,MAAM,IAAI,CAAC,MAAO,GAAE,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,OAAO,CAAA;AAC1D,MAAM,IAAI,CAAC,SAAU,GAAE,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,SAAS,CAAC,UAAU,CAAA;AACnE,MAAM,IAAI,SAAS,CAAC,IAAA,IAAQ,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;AAChE,QAAQ,IAAI,CAAC,KAAA,GAAQ,SAAS,CAAC,IAAI,CAAA;AACnC,OAAM;AACN,MAAM,IAAI,SAAS,CAAC,KAAK,EAAE;AAC3B,QAAQ,IAAI,CAAC,MAAA,GAAS,SAAS,CAAC,KAAK,CAAA;AACrC,OAAM;AACN,MAAM,IAAI,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE;AACxC,QAAQ,IAAI,CAAC,YAAA,GAAe,SAAS,CAAC,WAAW,CAAA;AACjD,OAAM;AACN,MAAM,IAAI,YAAY,CAAC,iBAAiB,EAAE,EAAE;AAC5C,QAAQ,IAAI,CAAC,eAAgB,GAAE,YAAY,CAAC,iBAAiB,EAAE,CAAA;AAC/D,OAAM;AACN,MAAM,IAAI,SAAS,CAAC,kBAAkB,EAAE;AACxC,QAAQ,IAAI,CAAC,mBAAA,GAAsB,SAAS,CAAC,kBAAkB,CAAA;AAC/D,OAAM;AACN,KAAI,MAAO,IAAI,aAAa,CAAC,YAAY,CAAC,EAAE;AAC5C,MAAM,MAAM,YAAa,GAAE,cAAe,EAAA;AAC1C,MAAM,IAAI,CAAC,KAAM,GAAE,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,YAAY,CAAC,MAAM,CAAA;AAC1D,MAAM,IAAI,CAAC,MAAO,GAAE,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,OAAO,CAAA;AAC7D,MAAM,IAAI,CAAC,SAAU,GAAE,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,YAAY,CAAC,UAAU,CAAA;AACtE,MAAM,IAAI,YAAY,CAAC,IAAI,EAAE;AAC7B,QAAQ,IAAI,CAAC,KAAA,GAAQ,YAAY,CAAC,IAAI,CAAA;AACtC,OAAM;AACN,MAAM,IAAI,YAAY,CAAC,KAAK,EAAE;AAC9B,QAAQ,IAAI,CAAC,MAAA,GAAS,YAAY,CAAC,KAAK,CAAA;AACxC,OAAM;AACN,MAAM,IAAI,YAAY,CAAC,WAAW,EAAE;AACpC,QAAQ,IAAI,CAAC,YAAA,GAAe,YAAY,CAAC,WAAW,CAAA;AACpD,OAAM;AACN,MAAM,IAAI,YAAY,CAAC,cAAc,EAAE;AACvC,QAAQ,IAAI,CAAC,eAAA,GAAkB,YAAY,CAAC,cAAc,CAAA;AAC1D,OAAM;AACN,MAAM,IAAI,YAAY,CAAC,kBAAkB,EAAE;AAC3C,QAAQ,IAAI,CAAC,mBAAA,GAAsB,YAAY,CAAC,kBAAkB,CAAA;AAClE,OAAM;AACN,KAAI;AACJ;AACA,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,KAAK,GAAS;AACvB,IAAI,IAAI,CAAC,YAAa,GAAE,EAAE,CAAA;AAC1B,IAAI,IAAI,CAAC,KAAM,GAAE,EAAE,CAAA;AACnB,IAAI,IAAI,CAAC,MAAO,GAAE,EAAE,CAAA;AACpB,IAAI,IAAI,CAAC,KAAM,GAAE,EAAE,CAAA;AACnB,IAAI,IAAI,CAAC,SAAU,GAAE,EAAE,CAAA;AACvB,IAAI,IAAI,CAAC,MAAO,GAAE,SAAS,CAAA;AAC3B,IAAI,IAAI,CAAC,gBAAiB,GAAE,SAAS,CAAA;AACrC,IAAI,IAAI,CAAC,YAAa,GAAE,SAAS,CAAA;AACjC,IAAI,IAAI,CAAC,eAAgB,GAAE,SAAS,CAAA;AACpC,IAAI,IAAI,CAAC,KAAM,GAAE,SAAS,CAAA;AAC1B,IAAI,IAAI,CAAC,QAAS,GAAE,SAAS,CAAA;AAC7B,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;AAChC,IAAI,IAAI,CAAC,YAAa,GAAE,EAAE,CAAA;AAC1B,IAAI,IAAI,CAAC,mBAAA,GAAsB,0BAA0B,EAAE,CAAA;AAC3D,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,aAAa,CAAC,UAAU,EAAc,cAAc,EAAiB;AAC9E,IAAI,MAAM,SAAU,GAAE,OAAO,cAAA,KAAmB,QAAS,GAAE,cAAe,GAAE,uBAAuB,CAAA;AACnG;AACA;AACA,IAAI,IAAI,SAAU,IAAG,CAAC,EAAE;AACxB,MAAM,OAAO,IAAI,CAAA;AACjB,KAAI;AACJ;AACA,IAAI,MAAM,mBAAmB;AAC7B,MAAM,SAAS,EAAE,sBAAsB,EAAE;AACzC,MAAM,GAAG,UAAU;AACnB,KAAK,CAAA;AACL;AACA,IAAI,MAAM,WAAA,GAAc,IAAI,CAAC,YAAY,CAAA;AACzC,IAAI,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;AACtC,IAAI,IAAI,CAAC,YAAA,GAAe,WAAW,CAAC,SAAS,SAAA,GAAY,WAAW,CAAC,KAAK,CAAC,CAAC,SAAS,CAAA,GAAI,WAAW,CAAA;AACpG;AACA,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;AAChC;AACA,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,iBAAiB,GAA2B;AACrD,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAA,GAAS,CAAC,CAAC,CAAA;AAC1D,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,gBAAgB,GAAS;AAClC,IAAI,IAAI,CAAC,YAAa,GAAE,EAAE,CAAA;AAC1B,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;AAChC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,aAAa,CAAC,UAAU,EAAoB;AACrD,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;AACtC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,cAAc,GAAiB;AACxC,IAAI,MAAM,IAAK,GAAE,IAAI,CAAC,YAAY,EAAE,CAAA;AACpC;AACA,IAAI,OAAO,IAAI,CAAC,WAAW,CAAA;AAC3B,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,gBAAgB,GAAS;AAClC,IAAI,IAAI,CAAC,YAAa,GAAE,EAAE,CAAA;AAC1B,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA,GAAS,YAAY,GAAc;AACnC,IAAI,MAAM;AACV,MAAM,YAAY;AAClB,MAAM,YAAY;AAClB,MAAM,SAAS;AACf,MAAM,KAAK;AACX,MAAM,MAAM;AACZ,MAAM,KAAK;AACX,MAAM,MAAM;AACZ,MAAM,YAAY;AAClB,MAAM,gBAAgB;AACtB,MAAM,mBAAmB;AACzB,MAAM,sBAAsB;AAC5B,MAAM,gBAAgB;AACtB,MAAM,KAAK;AACX,KAAI,GAAI,IAAI,CAAA;AACZ;AACA,IAAI,OAAO;AACX,MAAM,WAAW,EAAE,YAAY;AAC/B,MAAM,WAAW,EAAE,YAAY;AAC/B,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,WAAW,EAAE,YAAa,IAAG,EAAE;AACrC,MAAM,eAAe,EAAE,gBAAgB;AACvC,MAAM,kBAAkB,EAAE,mBAAmB;AAC7C,MAAM,qBAAqB,EAAE,sBAAsB;AACnD,MAAM,eAAe,EAAE,gBAAgB;AACvC,MAAM,IAAI,EAAE,KAAK;AACjB,KAAK,CAAA;AACL,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,YAAY;AACrB,IAAI,KAAK;AACT,IAAI,IAAI,GAAc,EAAE;AACxB,IAAI,yBAAyB,GAAqB,EAAE;AACpD,IAA+B;AAC/B,IAAI,qBAAqB,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAA;AACrD;AACA;AACA,IAAI,MAAM,eAAe,GAAqB;AAC9C,MAAM,GAAG,yBAAyB;AAClC;AACA,MAAM,GAAG,wBAAwB,EAAE;AACnC,MAAM,GAAG,IAAI,CAAC,gBAAgB;AAC9B,KAAK,CAAA;AACL;AACA,IAAI,OAAO,qBAAqB,CAAC,eAAe,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;AAC9D,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,wBAAwB,CAAC,OAAO,EAAoC;AAC7E,IAAI,IAAI,CAAC,sBAAuB,GAAE,EAAE,GAAG,IAAI,CAAC,sBAAsB,EAAE,GAAG,OAAA,EAAS,CAAA;AAChF;AACA,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,qBAAqB,CAAC,OAAO,EAA4B;AAClE,IAAI,IAAI,CAAC,mBAAoB,GAAE,OAAO,CAAA;AACtC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,qBAAqB,GAAuB;AACrD,IAAI,OAAO,IAAI,CAAC,mBAAmB,CAAA;AACnC,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,gBAAgB,CAAC,SAAS,EAAW,IAAI,EAAsB;AACxE,IAAI,MAAM,OAAA,GAAU,IAAA,IAAQ,IAAI,CAAC,QAAS,GAAE,IAAI,CAAC,QAAA,GAAW,KAAK,EAAE,CAAA;AACnE;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAM,MAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAA;AAChF,MAAM,OAAO,OAAO,CAAA;AACpB,KAAI;AACJ;AACA,IAAI,MAAM,kBAAmB,GAAE,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;AACrE;AACA,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB;AACjC,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,iBAAiB,EAAE,SAAS;AACpC,QAAQ,kBAAkB;AAC1B,QAAQ,GAAG,IAAI;AACf,QAAQ,QAAQ,EAAE,OAAO;AACzB,OAAO;AACP,MAAM,IAAI;AACV,KAAK,CAAA;AACL;AACA,IAAI,OAAO,OAAO,CAAA;AAClB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,cAAc,CAAC,OAAO,EAAU,KAAK,EAAkB,IAAI,EAAsB;AAC1F,IAAI,MAAM,OAAA,GAAU,IAAA,IAAQ,IAAI,CAAC,QAAS,GAAE,IAAI,CAAC,QAAA,GAAW,KAAK,EAAE,CAAA;AACnE;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAM,MAAM,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAA;AAC9E,MAAM,OAAO,OAAO,CAAA;AACpB,KAAI;AACJ;AACA,IAAI,MAAM,kBAAmB,GAAE,IAAI,KAAK,CAAC,OAAO,CAAC,CAAA;AACjD;AACA,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc;AAC/B,MAAM,OAAO;AACb,MAAM,KAAK;AACX,MAAM;AACN,QAAQ,iBAAiB,EAAE,OAAO;AAClC,QAAQ,kBAAkB;AAC1B,QAAQ,GAAG,IAAI;AACf,QAAQ,QAAQ,EAAE,OAAO;AACzB,OAAO;AACP,MAAM,IAAI;AACV,KAAK,CAAA;AACL;AACA,IAAI,OAAO,OAAO,CAAA;AAClB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,YAAY,CAAC,KAAK,EAAS,IAAI,EAAsB;AAC9D,IAAI,MAAM,OAAA,GAAU,IAAA,IAAQ,IAAI,CAAC,QAAS,GAAE,IAAI,CAAC,QAAA,GAAW,KAAK,EAAE,CAAA;AACnE;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAM,MAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAA;AAC5E,MAAM,OAAO,OAAO,CAAA;AACpB,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,GAAG,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;AAC1E;AACA,IAAI,OAAO,OAAO,CAAA;AAClB,GAAE;AACF;AACA;AACA;AACA;AACA,GAAY,qBAAqB,GAAS;AAC1C;AACA;AACA;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;AACnC,MAAM,IAAI,CAAC,mBAAoB,GAAE,IAAI,CAAA;AACrC,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,YAAY;AAC/C,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAA;AACtB,OAAO,CAAC,CAAA;AACR,MAAM,IAAI,CAAC,mBAAoB,GAAE,KAAK,CAAA;AACtC,KAAI;AACJ,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,GAAmB;AACjD,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,WAAY,GAAE,IAAI,KAAK,EAAE,CAAA;AAC7B,GAAE;AACF;AACA,EAAE,OAAO,WAAW,CAAA;AACpB,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,KAAK,EAAoC;AACxE,EAAE,WAAA,GAAc,KAAK,CAAA;AACrB,CAAA;AACA;AACA,SAAS,0BAA0B,GAAuB;AAC1D,EAAE,OAAO;AACT,IAAI,OAAO,EAAE,KAAK,EAAE;AACpB,IAAI,MAAM,EAAE,KAAK,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;AACjC,GAAG,CAAA;AACH;;;;"}