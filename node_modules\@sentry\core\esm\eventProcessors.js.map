{"version": 3, "file": "eventProcessors.js", "sources": ["../../src/eventProcessors.ts"], "sourcesContent": ["import type { Event, EventHint, EventProcessor } from '@sentry/types';\nimport { SyncPromise, getGlobalSingleton, isThenable, logger } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from './debug-build';\n\n/**\n * Returns the global event processors.\n * @deprecated Global event processors will be removed in v8.\n */\nexport function getGlobalEventProcessors(): EventProcessor[] {\n  return getGlobalSingleton<EventProcessor[]>('globalEventProcessors', () => []);\n}\n\n/**\n * Add a EventProcessor to be kept globally.\n * @deprecated Use `addEventProcessor` instead. Global event processors will be removed in v8.\n */\nexport function addGlobalEventProcessor(callback: EventProcessor): void {\n  // eslint-disable-next-line deprecation/deprecation\n  getGlobalEventProcessors().push(callback);\n}\n\n/**\n * Process an array of event processors, returning the processed event (or `null` if the event was dropped).\n */\nexport function notifyEventProcessors(\n  processors: EventProcessor[],\n  event: Event | null,\n  hint: EventHint,\n  index: number = 0,\n): PromiseLike<Event | null> {\n  return new SyncPromise<Event | null>((resolve, reject) => {\n    const processor = processors[index];\n    if (event === null || typeof processor !== 'function') {\n      resolve(event);\n    } else {\n      const result = processor({ ...event }, hint) as Event | null;\n\n      DEBUG_BUILD && processor.id && result === null && logger.log(`Event processor \"${processor.id}\" dropped event`);\n\n      if (isThenable(result)) {\n        void result\n          .then(final => notifyEventProcessors(processors, final, hint, index + 1).then(resolve))\n          .then(null, reject);\n      } else {\n        void notifyEventProcessors(processors, result, hint, index + 1)\n          .then(resolve)\n          .then(null, reject);\n      }\n    }\n  });\n}\n"], "names": [], "mappings": ";;;AAKA;AACA;AACA;AACA;AACO,SAAS,wBAAwB,GAAqB;AAC7D,EAAE,OAAO,kBAAkB,CAAmB,uBAAuB,EAAE,MAAM,EAAE,CAAC,CAAA;AAChF,CAAA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,uBAAuB,CAAC,QAAQ,EAAwB;AACxE;AACA,EAAE,wBAAwB,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;AAC3C,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,qBAAqB;AACrC,EAAE,UAAU;AACZ,EAAE,KAAK;AACP,EAAE,IAAI;AACN,EAAE,KAAK,GAAW,CAAC;AACnB,EAA6B;AAC7B,EAAE,OAAO,IAAI,WAAW,CAAe,CAAC,OAAO,EAAE,MAAM,KAAK;AAC5D,IAAI,MAAM,SAAU,GAAE,UAAU,CAAC,KAAK,CAAC,CAAA;AACvC,IAAI,IAAI,KAAA,KAAU,IAAA,IAAQ,OAAO,SAAA,KAAc,UAAU,EAAE;AAC3D,MAAM,OAAO,CAAC,KAAK,CAAC,CAAA;AACpB,WAAW;AACX,MAAM,MAAM,MAAO,GAAE,SAAS,CAAC,EAAE,GAAG,KAAM,EAAC,EAAE,IAAI,CAAE,EAAA;AACnD;AACA,MAAM,WAAA,IAAe,SAAS,CAAC,MAAM,MAAA,KAAW,IAAA,IAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,SAAS,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAA;AACrH;AACA,MAAM,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE;AAC9B,QAAQ,KAAK,MAAA;AACb,WAAW,IAAI,CAAC,KAAA,IAAS,qBAAqB,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;AAChG,WAAW,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;AAC7B,aAAa;AACb,QAAQ,KAAK,qBAAqB,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,KAAM,GAAE,CAAC,CAAA;AACtE,WAAW,IAAI,CAAC,OAAO,CAAA;AACvB,WAAW,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;AAC7B,OAAM;AACN,KAAI;AACJ,GAAG,CAAC,CAAA;AACJ;;;;"}