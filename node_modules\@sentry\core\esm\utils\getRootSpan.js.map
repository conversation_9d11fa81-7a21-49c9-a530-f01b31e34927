{"version": 3, "file": "getRootSpan.js", "sources": ["../../../src/utils/getRootSpan.ts"], "sourcesContent": ["import type { Span } from '@sentry/types';\n\n/**\n * Returns the root span of a given span.\n *\n * As long as we use `Transaction`s internally, the returned root span\n * will be a `Transaction` but be aware that this might change in the future.\n *\n * If the given span has no root span or transaction, `undefined` is returned.\n */\nexport function getRootSpan(span: Span): Span | undefined {\n  // TODO (v8): Remove this check and just return span\n  // eslint-disable-next-line deprecation/deprecation\n  return span.transaction;\n}\n"], "names": [], "mappings": "AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,CAAC,IAAI,EAA0B;AAC1D;AACA;AACA,EAAE,OAAO,IAAI,CAAC,WAAW,CAAA;AACzB;;;;"}