{"version": 3, "file": "hubextensions.js", "sources": ["../../../src/tracing/hubextensions.ts"], "sourcesContent": ["import type { ClientOptions, CustomSamplingContext, TransactionContext } from '@sentry/types';\nimport { logger } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../debug-build';\nimport type { Hub } from '../hub';\nimport { getMainCarrier } from '../hub';\nimport { spanToTraceHeader } from '../utils/spanUtils';\nimport { registerErrorInstrumentation } from './errors';\nimport { IdleTransaction } from './idletransaction';\nimport { sampleTransaction } from './sampling';\nimport { Transaction } from './transaction';\n\n/** Returns all trace headers that are currently on the top scope. */\n// eslint-disable-next-line deprecation/deprecation\nfunction traceHeaders(this: Hub): { [key: string]: string } {\n  // eslint-disable-next-line deprecation/deprecation\n  const scope = this.getScope();\n  // eslint-disable-next-line deprecation/deprecation\n  const span = scope.getSpan();\n\n  return span\n    ? {\n        'sentry-trace': spanToTraceHeader(span),\n      }\n    : {};\n}\n\n/**\n * Creates a new transaction and adds a sampling decision if it doesn't yet have one.\n *\n * The Hub.startTransaction method delegates to this method to do its work, passing the Hub instance in as `this`, as if\n * it had been called on the hub directly. Exists as a separate function so that it can be injected into the class as an\n * \"extension method.\"\n *\n * @param this: The Hub starting the transaction\n * @param transactionContext: Data used to configure the transaction\n * @param CustomSamplingContext: Optional data to be provided to the `tracesSampler` function (if any)\n *\n * @returns The new transaction\n *\n * @see {@link Hub.startTransaction}\n */\nfunction _startTransaction(\n  // eslint-disable-next-line deprecation/deprecation\n  this: Hub,\n  transactionContext: TransactionContext,\n  customSamplingContext?: CustomSamplingContext,\n): Transaction {\n  // eslint-disable-next-line deprecation/deprecation\n  const client = this.getClient();\n  const options: Partial<ClientOptions> = (client && client.getOptions()) || {};\n\n  const configInstrumenter = options.instrumenter || 'sentry';\n  const transactionInstrumenter = transactionContext.instrumenter || 'sentry';\n\n  if (configInstrumenter !== transactionInstrumenter) {\n    DEBUG_BUILD &&\n      logger.error(\n        `A transaction was started with instrumenter=\\`${transactionInstrumenter}\\`, but the SDK is configured with the \\`${configInstrumenter}\\` instrumenter.\nThe transaction will not be sampled. Please use the ${configInstrumenter} instrumentation to start transactions.`,\n      );\n\n    // eslint-disable-next-line deprecation/deprecation\n    transactionContext.sampled = false;\n  }\n\n  // eslint-disable-next-line deprecation/deprecation\n  let transaction = new Transaction(transactionContext, this);\n  transaction = sampleTransaction(transaction, options, {\n    name: transactionContext.name,\n    parentSampled: transactionContext.parentSampled,\n    transactionContext,\n    attributes: {\n      // eslint-disable-next-line deprecation/deprecation\n      ...transactionContext.data,\n      ...transactionContext.attributes,\n    },\n    ...customSamplingContext,\n  });\n  if (transaction.isRecording()) {\n    transaction.initSpanRecorder(options._experiments && (options._experiments.maxSpans as number));\n  }\n  if (client && client.emit) {\n    client.emit('startTransaction', transaction);\n  }\n  return transaction;\n}\n\n/**\n * Create new idle transaction.\n */\nexport function startIdleTransaction(\n  // eslint-disable-next-line deprecation/deprecation\n  hub: Hub,\n  transactionContext: TransactionContext,\n  idleTimeout: number,\n  finalTimeout: number,\n  onScope?: boolean,\n  customSamplingContext?: CustomSamplingContext,\n  heartbeatInterval?: number,\n  delayAutoFinishUntilSignal: boolean = false,\n): IdleTransaction {\n  // eslint-disable-next-line deprecation/deprecation\n  const client = hub.getClient();\n  const options: Partial<ClientOptions> = (client && client.getOptions()) || {};\n\n  // eslint-disable-next-line deprecation/deprecation\n  let transaction = new IdleTransaction(\n    transactionContext,\n    hub,\n    idleTimeout,\n    finalTimeout,\n    heartbeatInterval,\n    onScope,\n    delayAutoFinishUntilSignal,\n  );\n  transaction = sampleTransaction(transaction, options, {\n    name: transactionContext.name,\n    parentSampled: transactionContext.parentSampled,\n    transactionContext,\n    attributes: {\n      // eslint-disable-next-line deprecation/deprecation\n      ...transactionContext.data,\n      ...transactionContext.attributes,\n    },\n    ...customSamplingContext,\n  });\n  if (transaction.isRecording()) {\n    transaction.initSpanRecorder(options._experiments && (options._experiments.maxSpans as number));\n  }\n  if (client && client.emit) {\n    client.emit('startTransaction', transaction);\n  }\n  return transaction;\n}\n\n/**\n * Adds tracing extensions to the global hub.\n */\nexport function addTracingExtensions(): void {\n  const carrier = getMainCarrier();\n  if (!carrier.__SENTRY__) {\n    return;\n  }\n  carrier.__SENTRY__.extensions = carrier.__SENTRY__.extensions || {};\n  if (!carrier.__SENTRY__.extensions.startTransaction) {\n    carrier.__SENTRY__.extensions.startTransaction = _startTransaction;\n  }\n  if (!carrier.__SENTRY__.extensions.traceHeaders) {\n    carrier.__SENTRY__.extensions.traceHeaders = traceHeaders;\n  }\n\n  registerErrorInstrumentation();\n}\n"], "names": ["spanToTraceHeader", "DEBUG_BUILD", "logger", "transaction", "Transaction", "sampleTransaction", "IdleTransaction", "getMainCarrier", "registerErrorInstrumentation"], "mappings": ";;;;;;;;;;;AAYA;AACA;AACA,SAAS,YAAY,GAAuC;AAC5D;AACA,EAAE,MAAM,KAAM,GAAE,IAAI,CAAC,QAAQ,EAAE,CAAA;AAC/B;AACA,EAAE,MAAM,IAAK,GAAE,KAAK,CAAC,OAAO,EAAE,CAAA;AAC9B;AACA,EAAE,OAAO,IAAA;AACT,MAAM;AACN,QAAQ,cAAc,EAAEA,2BAAiB,CAAC,IAAI,CAAC;AAC/C,OAAM;AACN,MAAM,EAAE,CAAA;AACR,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,iBAAiB;AAC1B;;AAEA,EAAE,kBAAkB;AACpB,EAAE,qBAAqB;AACvB,EAAe;AACf;AACA,EAAE,MAAM,MAAO,GAAE,IAAI,CAAC,SAAS,EAAE,CAAA;AACjC,EAAE,MAAM,OAAO,GAA2B,CAAC,MAAO,IAAG,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,CAAA;AAC/E;AACA,EAAE,MAAM,kBAAmB,GAAE,OAAO,CAAC,YAAA,IAAgB,QAAQ,CAAA;AAC7D,EAAE,MAAM,uBAAwB,GAAE,kBAAkB,CAAC,YAAA,IAAgB,QAAQ,CAAA;AAC7E;AACA,EAAE,IAAI,kBAAmB,KAAI,uBAAuB,EAAE;AACtD,IAAIC,sBAAY;AAChB,MAAMC,YAAM,CAAC,KAAK;AAClB,QAAQ,CAAC,8CAA8C,EAAE,uBAAuB,CAAC,yCAAyC,EAAE,kBAAkB,CAAC;AAC/I,oDAAoD,EAAE,kBAAkB,CAAC,uCAAuC,CAAC;AACjH,OAAO,CAAA;AACP;AACA;AACA,IAAI,kBAAkB,CAAC,OAAQ,GAAE,KAAK,CAAA;AACtC,GAAE;AACF;AACA;AACA,EAAE,IAAIC,gBAAc,IAAIC,uBAAW,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAA;AAC7D,EAAED,gBAAcE,0BAAiB,CAACF,aAAW,EAAE,OAAO,EAAE;AACxD,IAAI,IAAI,EAAE,kBAAkB,CAAC,IAAI;AACjC,IAAI,aAAa,EAAE,kBAAkB,CAAC,aAAa;AACnD,IAAI,kBAAkB;AACtB,IAAI,UAAU,EAAE;AAChB;AACA,MAAM,GAAG,kBAAkB,CAAC,IAAI;AAChC,MAAM,GAAG,kBAAkB,CAAC,UAAU;AACtC,KAAK;AACL,IAAI,GAAG,qBAAqB;AAC5B,GAAG,CAAC,CAAA;AACJ,EAAE,IAAIA,aAAW,CAAC,WAAW,EAAE,EAAE;AACjC,IAAIA,aAAW,CAAC,gBAAgB,CAAC,OAAO,CAAC,YAAA,KAAiB,OAAO,CAAC,YAAY,CAAC,QAAA,EAAmB,CAAC,CAAA;AACnG,GAAE;AACF,EAAE,IAAI,MAAA,IAAU,MAAM,CAAC,IAAI,EAAE;AAC7B,IAAI,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAEA,aAAW,CAAC,CAAA;AAChD,GAAE;AACF,EAAE,OAAOA,aAAW,CAAA;AACpB,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,oBAAoB;AACpC;AACA,EAAE,GAAG;AACL,EAAE,kBAAkB;AACpB,EAAE,WAAW;AACb,EAAE,YAAY;AACd,EAAE,OAAO;AACT,EAAE,qBAAqB;AACvB,EAAE,iBAAiB;AACnB,EAAE,0BAA0B,GAAY,KAAK;AAC7C,EAAmB;AACnB;AACA,EAAE,MAAM,MAAO,GAAE,GAAG,CAAC,SAAS,EAAE,CAAA;AAChC,EAAE,MAAM,OAAO,GAA2B,CAAC,MAAO,IAAG,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,CAAA;AAC/E;AACA;AACA,EAAE,IAAI,WAAA,GAAc,IAAIG,+BAAe;AACvC,IAAI,kBAAkB;AACtB,IAAI,GAAG;AACP,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,iBAAiB;AACrB,IAAI,OAAO;AACX,IAAI,0BAA0B;AAC9B,GAAG,CAAA;AACH,EAAE,cAAcD,0BAAiB,CAAC,WAAW,EAAE,OAAO,EAAE;AACxD,IAAI,IAAI,EAAE,kBAAkB,CAAC,IAAI;AACjC,IAAI,aAAa,EAAE,kBAAkB,CAAC,aAAa;AACnD,IAAI,kBAAkB;AACtB,IAAI,UAAU,EAAE;AAChB;AACA,MAAM,GAAG,kBAAkB,CAAC,IAAI;AAChC,MAAM,GAAG,kBAAkB,CAAC,UAAU;AACtC,KAAK;AACL,IAAI,GAAG,qBAAqB;AAC5B,GAAG,CAAC,CAAA;AACJ,EAAE,IAAI,WAAW,CAAC,WAAW,EAAE,EAAE;AACjC,IAAI,WAAW,CAAC,gBAAgB,CAAC,OAAO,CAAC,YAAA,KAAiB,OAAO,CAAC,YAAY,CAAC,QAAA,EAAmB,CAAC,CAAA;AACnG,GAAE;AACF,EAAE,IAAI,MAAA,IAAU,MAAM,CAAC,IAAI,EAAE;AAC7B,IAAI,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAA;AAChD,GAAE;AACF,EAAE,OAAO,WAAW,CAAA;AACpB,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,oBAAoB,GAAS;AAC7C,EAAE,MAAM,OAAA,GAAUE,kBAAc,EAAE,CAAA;AAClC,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;AAC3B,IAAI,OAAM;AACV,GAAE;AACF,EAAE,OAAO,CAAC,UAAU,CAAC,UAAW,GAAE,OAAO,CAAC,UAAU,CAAC,UAAW,IAAG,EAAE,CAAA;AACrE,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,gBAAgB,EAAE;AACvD,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,gBAAA,GAAmB,iBAAiB,CAAA;AACtE,GAAE;AACF,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,YAAY,EAAE;AACnD,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,YAAA,GAAe,YAAY,CAAA;AAC7D,GAAE;AACF;AACA,EAAEC,mCAA4B,EAAE,CAAA;AAChC;;;;;"}