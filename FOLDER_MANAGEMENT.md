# 文件夹管理功能使用说明

## 功能概述

新增的文件夹管理功能让您可以像使用网盘一样组织和管理您的文件，包括：

- 创建文件夹
- 将文件移动到文件夹
- 文件夹视图浏览
- 面包屑导航
- 文件重命名

## 使用方法

### 1. 创建文件夹

1. 在管理页面顶部工具栏中，点击文件夹图标 📁
2. 选择"新建文件夹"
3. 输入文件夹名称（最多50个字符）
4. 点击确定即可创建

### 2. 移动文件到文件夹

#### 方法一：单个文件移动
1. 将鼠标悬停在文件卡片上
2. 点击"移动"按钮
3. 在弹出的对话框中选择目标文件夹
4. 点击"移动"确认

#### 方法二：批量移动文件
1. 选中要移动的文件（勾选文件卡片上的复选框）
2. 点击文件夹图标 📁，选择"移动到文件夹"
3. 在弹出的对话框中选择目标文件夹
4. 点击"移动"确认

### 3. 文件夹视图

1. 点击文件夹图标 📁，选择"文件夹视图"
2. 在文件夹视图中可以看到所有创建的文件夹
3. 点击文件夹即可进入该文件夹查看其中的文件
4. 使用面包屑导航可以快速返回上级目录

### 4. 文件重命名

1. 将鼠标悬停在文件卡片上
2. 点击"编辑"按钮
3. 输入新的文件名（最多64个字符）
4. 点击确定保存

## 技术实现

### API 接口

- `GET /api/manage/createFolder` - 创建文件夹
- `GET /api/manage/getFolders` - 获取文件夹列表
- `GET /api/manage/moveToFolder/[id]` - 移动文件到文件夹
- `GET /api/manage/deleteFolder/[id]` - 删除文件夹
- `GET /api/manage/editName/[id]` - 重命名文件

### 数据存储

文件夹信息存储在 Cloudflare KV 中，包含以下字段：

```json
{
  "type": "folder",
  "folderName": "文件夹名称",
  "folderPath": "完整路径",
  "parentFolder": "父文件夹路径",
  "createdAt": 1642089600000,
  "fileCount": 5
}
```

文件的文件夹信息存储在文件元数据中：

```json
{
  "folderPath": "文件夹路径",
  "fileName": "显示名称",
  "movedAt": 1642089600000
}
```

## 注意事项

1. 文件夹名称不能包含特殊字符：`< > : " / \ | ? *`
2. 删除文件夹前需要确保文件夹为空
3. 移动文件时会更新文件的元数据
4. 文件夹路径使用 `/` 作为分隔符

## 故障排除

### 重命名功能不工作
- 检查后端 API 文件是否正确部署
- 确认版权声明格式正确（使用 `//` 而不是 `#`）

### 文件夹创建失败
- 检查文件夹名称是否符合要求
- 确认 Cloudflare KV 存储配置正确

### 文件移动失败
- 确认目标文件夹存在
- 检查文件是否存在
- 查看浏览器控制台错误信息
