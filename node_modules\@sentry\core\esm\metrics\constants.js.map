{"version": 3, "file": "constants.js", "sources": ["../../../src/metrics/constants.ts"], "sourcesContent": ["export const COUNTER_METRIC_TYPE = 'c' as const;\nexport const GAUGE_METRIC_TYPE = 'g' as const;\nexport const SET_METRIC_TYPE = 's' as const;\nexport const DISTRIBUTION_METRIC_TYPE = 'd' as const;\n\n/**\n * This does not match spec in https://develop.sentry.dev/sdk/metrics\n * but was chosen to optimize for the most common case in browser environments.\n */\nexport const DEFAULT_BROWSER_FLUSH_INTERVAL = 5000;\n\n/**\n * SDKs are required to bucket into 10 second intervals (rollup in seconds)\n * which is the current lower bound of metric accuracy.\n */\nexport const DEFAULT_FLUSH_INTERVAL = 10000;\n\n/**\n * The maximum number of metrics that should be stored in memory.\n */\nexport const MAX_WEIGHT = 10000;\n"], "names": [], "mappings": "AAAO,MAAM,mBAAoB,GAAE,GAAI,EAAA;AAChC,MAAM,iBAAkB,GAAE,GAAI,EAAA;AAC9B,MAAM,eAAgB,GAAE,GAAI,EAAA;AAC5B,MAAM,wBAAyB,GAAE,GAAI,EAAA;AAC5C;AACA;AACA;AACA;AACA;AACO,MAAM,8BAA+B,GAAE,KAAI;AAClD;AACA;AACA;AACA;AACA;AACO,MAAM,sBAAuB,GAAE,MAAK;AAC3C;AACA;AACA;AACA;AACO,MAAM,UAAW,GAAE;;;;"}