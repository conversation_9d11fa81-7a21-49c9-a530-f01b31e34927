{"version": 3, "file": "browser-aggregator.js", "sources": ["../../../src/metrics/browser-aggregator.ts"], "sourcesContent": ["import type { Client, ClientOptions, MeasurementUnit, MetricsAggregator, Primitive } from '@sentry/types';\nimport { timestampInSeconds } from '@sentry/utils';\nimport { DEFAULT_BROWSER_FLUSH_INTERVAL, SET_METRIC_TYPE } from './constants';\nimport { METRIC_MAP } from './instance';\nimport { updateMetricSummaryOnActiveSpan } from './metric-summary';\nimport type { MetricBucket, MetricType } from './types';\nimport { getBucketKey, sanitizeMetricKey, sanitizeTags, sanitizeUnit } from './utils';\n\n/**\n * A simple metrics aggregator that aggregates metrics in memory and flushes them periodically.\n * Default flush interval is 5 seconds.\n *\n * @experimental This API is experimental and might change in the future.\n */\nexport class BrowserMetricsAggregator implements MetricsAggregator {\n  // TODO(@anonrig): Use FinalizationRegistry to have a proper way of flushing the buckets\n  // when the aggregator is garbage collected.\n  // Ref: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/FinalizationRegistry\n  private _buckets: MetricBucket;\n  private readonly _interval: ReturnType<typeof setInterval>;\n\n  public constructor(private readonly _client: Client<ClientOptions>) {\n    this._buckets = new Map();\n    this._interval = setInterval(() => this.flush(), DEFAULT_BROWSER_FLUSH_INTERVAL);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public add(\n    metricType: MetricType,\n    unsanitizedName: string,\n    value: number | string,\n    unsanitizedUnit: MeasurementUnit | undefined = 'none',\n    unsanitizedTags: Record<string, Primitive> | undefined = {},\n    maybeFloatTimestamp: number | undefined = timestampInSeconds(),\n  ): void {\n    const timestamp = Math.floor(maybeFloatTimestamp);\n    const name = sanitizeMetricKey(unsanitizedName);\n    const tags = sanitizeTags(unsanitizedTags);\n    const unit = sanitizeUnit(unsanitizedUnit as string);\n\n    const bucketKey = getBucketKey(metricType, name, unit, tags);\n\n    let bucketItem = this._buckets.get(bucketKey);\n    // If this is a set metric, we need to calculate the delta from the previous weight.\n    const previousWeight = bucketItem && metricType === SET_METRIC_TYPE ? bucketItem.metric.weight : 0;\n\n    if (bucketItem) {\n      bucketItem.metric.add(value);\n      // TODO(abhi): Do we need this check?\n      if (bucketItem.timestamp < timestamp) {\n        bucketItem.timestamp = timestamp;\n      }\n    } else {\n      bucketItem = {\n        // @ts-expect-error we don't need to narrow down the type of value here, saves bundle size.\n        metric: new METRIC_MAP[metricType](value),\n        timestamp,\n        metricType,\n        name,\n        unit,\n        tags,\n      };\n      this._buckets.set(bucketKey, bucketItem);\n    }\n\n    // If value is a string, it's a set metric so calculate the delta from the previous weight.\n    const val = typeof value === 'string' ? bucketItem.metric.weight - previousWeight : value;\n    updateMetricSummaryOnActiveSpan(metricType, name, val, unit, unsanitizedTags, bucketKey);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public flush(): void {\n    // short circuit if buckets are empty.\n    if (this._buckets.size === 0) {\n      return;\n    }\n\n    if (this._client.captureAggregateMetrics) {\n      // TODO(@anonrig): Use Object.values() when we support ES6+\n      const metricBuckets = Array.from(this._buckets).map(([, bucketItem]) => bucketItem);\n      this._client.captureAggregateMetrics(metricBuckets);\n    }\n\n    this._buckets.clear();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public close(): void {\n    clearInterval(this._interval);\n    this.flush();\n  }\n}\n"], "names": ["DEFAULT_BROWSER_FLUSH_INTERVAL", "timestampInSeconds", "sanitizeMetricKey", "sanitizeTags", "sanitizeUnit", "getBucketKey", "SET_METRIC_TYPE", "METRIC_MAP", "updateMetricSummaryOnActiveSpan"], "mappings": ";;;;;;;;AAQA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,0BAAsD;AACnE;AACA;AACA;;AAIA,GAAS,WAAW,GAAkB,OAAO,EAAyB,CAAA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA;AACtE,IAAI,IAAI,CAAC,QAAA,GAAW,IAAI,GAAG,EAAE,CAAA;AAC7B,IAAI,IAAI,CAAC,SAAA,GAAY,WAAW,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,EAAEA,wCAA8B,CAAC,CAAA;AACpF,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,GAAG;AACZ,IAAI,UAAU;AACd,IAAI,eAAe;AACnB,IAAI,KAAK;AACT,IAAI,eAAe,GAAgC,MAAM;AACzD,IAAI,eAAe,GAA0C,EAAE;AAC/D,IAAI,mBAAmB,GAAuBC,0BAAkB,EAAE;AAClE,IAAU;AACV,IAAI,MAAM,YAAY,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;AACrD,IAAI,MAAM,IAAK,GAAEC,uBAAiB,CAAC,eAAe,CAAC,CAAA;AACnD,IAAI,MAAM,IAAK,GAAEC,kBAAY,CAAC,eAAe,CAAC,CAAA;AAC9C,IAAI,MAAM,IAAK,GAAEC,kBAAY,CAAC,iBAA0B,CAAA;AACxD;AACA,IAAI,MAAM,SAAA,GAAYC,kBAAY,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAChE;AACA,IAAI,IAAI,UAAW,GAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;AACjD;AACA,IAAI,MAAM,cAAA,GAAiB,UAAA,IAAc,UAAW,KAAIC,yBAAgB,GAAE,UAAU,CAAC,MAAM,CAAC,MAAA,GAAS,CAAC,CAAA;AACtG;AACA,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;AAClC;AACA,MAAM,IAAI,UAAU,CAAC,SAAU,GAAE,SAAS,EAAE;AAC5C,QAAQ,UAAU,CAAC,SAAU,GAAE,SAAS,CAAA;AACxC,OAAM;AACN,WAAW;AACX,MAAM,aAAa;AACnB;AACA,QAAQ,MAAM,EAAE,IAAIC,mBAAU,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC;AACjD,QAAQ,SAAS;AACjB,QAAQ,UAAU;AAClB,QAAQ,IAAI;AACZ,QAAQ,IAAI;AACZ,QAAQ,IAAI;AACZ,OAAO,CAAA;AACP,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAA;AAC9C,KAAI;AACJ;AACA;AACA,IAAI,MAAM,GAAI,GAAE,OAAO,KAAA,KAAU,QAAS,GAAE,UAAU,CAAC,MAAM,CAAC,MAAA,GAAS,cAAA,GAAiB,KAAK,CAAA;AAC7F,IAAIC,6CAA+B,CAAC,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,CAAC,CAAA;AAC5F,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,KAAK,GAAS;AACvB;AACA,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAA,KAAS,CAAC,EAAE;AAClC,MAAM,OAAM;AACZ,KAAI;AACJ;AACA,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE;AAC9C;AACA,MAAM,MAAM,gBAAgB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,UAAU,CAAC,CAAA;AACzF,MAAM,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAA;AACzD,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;AACzB,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,KAAK,GAAS;AACvB,IAAI,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;AACjC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAA;AAChB,GAAE;AACF;;;;"}