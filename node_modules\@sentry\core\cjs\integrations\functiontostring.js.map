{"version": 3, "file": "functiontostring.js", "sources": ["../../../src/integrations/functiontostring.ts"], "sourcesContent": ["import type { Client, Integration, IntegrationClass, IntegrationFn, WrappedFunction } from '@sentry/types';\nimport { getOriginalFunction } from '@sentry/utils';\nimport { getClient } from '../exports';\nimport { convertIntegrationFnToClass, defineIntegration } from '../integration';\n\nlet originalFunctionToString: () => void;\n\nconst INTEGRATION_NAME = 'FunctionToString';\n\nconst SETUP_CLIENTS = new WeakMap<Client, boolean>();\n\nconst _functionToStringIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      // eslint-disable-next-line @typescript-eslint/unbound-method\n      originalFunctionToString = Function.prototype.toString;\n\n      // intrinsics (like Function.prototype) might be immutable in some environments\n      // e.g. Node with --frozen-intrinsics, XS (an embedded JavaScript engine) or SES (a JavaScript proposal)\n      try {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        Function.prototype.toString = function (this: WrappedFunction, ...args: any[]): string {\n          const originalFunction = getOriginalFunction(this);\n          const context =\n            SETUP_CLIENTS.has(getClient() as Client) && originalFunction !== undefined ? originalFunction : this;\n          return originalFunctionToString.apply(context, args);\n        };\n      } catch {\n        // ignore errors here, just don't patch this\n      }\n    },\n    setup(client) {\n      SETUP_CLIENTS.set(client, true);\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Patch toString calls to return proper name for wrapped functions.\n *\n * ```js\n * Sentry.init({\n *   integrations: [\n *     functionToStringIntegration(),\n *   ],\n * });\n * ```\n */\nexport const functionToStringIntegration = defineIntegration(_functionToStringIntegration);\n\n/**\n * Patch toString calls to return proper name for wrapped functions.\n *\n * @deprecated Use `functionToStringIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const FunctionToString = convertIntegrationFnToClass(\n  INTEGRATION_NAME,\n  functionToStringIntegration,\n) as IntegrationClass<Integration & { setupOnce: () => void }>;\n\n// eslint-disable-next-line deprecation/deprecation\nexport type FunctionToString = typeof FunctionToString;\n"], "names": ["getOriginalFunction", "getClient", "defineIntegration", "convertIntegrationFnToClass"], "mappings": ";;;;;;AAKA,IAAI,wBAAwB,CAAA;AAC5B;AACA,MAAM,gBAAA,GAAmB,kBAAkB,CAAA;AAC3C;AACA,MAAM,aAAc,GAAE,IAAI,OAAO,EAAmB,CAAA;AACpD;AACA,MAAM,4BAA6B,IAAG,MAAM;AAC5C,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,SAAS,GAAG;AAChB;AACA,MAAM,2BAA2B,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAA;AAC5D;AACA;AACA;AACA,MAAM,IAAI;AACV;AACA,QAAQ,QAAQ,CAAC,SAAS,CAAC,QAAS,GAAE,WAAiC,GAAG,IAAI,EAAiB;AAC/F,UAAU,MAAM,gBAAiB,GAAEA,yBAAmB,CAAC,IAAI,CAAC,CAAA;AAC5D,UAAU,MAAM,OAAQ;AACxB,YAAY,aAAa,CAAC,GAAG,CAACC,mBAAS,EAAC,EAAa,IAAG,qBAAqB,SAAA,GAAY,gBAAA,GAAmB,IAAI,CAAA;AAChH,UAAU,OAAO,wBAAwB,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;AAC9D,SAAS,CAAA;AACT,QAAQ,OAAM,CAAA,EAAA;AACd;AACA,OAAM;AACN,KAAK;AACL,IAAI,KAAK,CAAC,MAAM,EAAE;AAClB,MAAM,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;AACrC,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACa,2BAA4B,GAAEC,6BAAiB,CAAC,4BAA4B,EAAC;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,gBAAiB,GAAEC,uCAA2B;AAC3D,EAAE,gBAAgB;AAClB,EAAE,2BAA2B;AAC7B,CAAE,EAAA;AACF;AACA;;;;;"}