{"version": 3, "file": "spanstatus.js", "sources": ["../../../src/tracing/spanstatus.ts"], "sourcesContent": ["import type { Span } from '@sentry/types';\n\n/** The status of an Span.\n *\n * @deprecated Use string literals - if you require type casting, cast to SpanStatusType type\n */\nexport enum SpanStatus {\n  /** The operation completed successfully. */\n  Ok = 'ok',\n  /** Deadline expired before operation could complete. */\n  DeadlineExceeded = 'deadline_exceeded',\n  /** 401 Unauthorized (actually does mean unauthenticated according to RFC 7235) */\n  Unauthenticated = 'unauthenticated',\n  /** 403 Forbidden */\n  PermissionDenied = 'permission_denied',\n  /** 404 Not Found. Some requested entity (file or directory) was not found. */\n  NotFound = 'not_found',\n  /** 429 Too Many Requests */\n  ResourceExhausted = 'resource_exhausted',\n  /** Client specified an invalid argument. 4xx. */\n  InvalidArgument = 'invalid_argument',\n  /** 501 Not Implemented */\n  Unimplemented = 'unimplemented',\n  /** 503 Service Unavailable */\n  Unavailable = 'unavailable',\n  /** Other/generic 5xx. */\n  InternalError = 'internal_error',\n  /** Unknown. Any non-standard HTTP status code. */\n  UnknownError = 'unknown_error',\n  /** The operation was cancelled (typically by the user). */\n  Cancelled = 'cancelled',\n  /** Already exists (409) */\n  AlreadyExists = 'already_exists',\n  /** Operation was rejected because the system is not in a state required for the operation's */\n  FailedPrecondition = 'failed_precondition',\n  /** The operation was aborted, typically due to a concurrency issue. */\n  Aborted = 'aborted',\n  /** Operation was attempted past the valid range. */\n  OutOfRange = 'out_of_range',\n  /** Unrecoverable data loss or corruption */\n  DataLoss = 'data_loss',\n}\n\nexport type SpanStatusType =\n  /** The operation completed successfully. */\n  | 'ok'\n  /** Deadline expired before operation could complete. */\n  | 'deadline_exceeded'\n  /** 401 Unauthorized (actually does mean unauthenticated according to RFC 7235) */\n  | 'unauthenticated'\n  /** 403 Forbidden */\n  | 'permission_denied'\n  /** 404 Not Found. Some requested entity (file or directory) was not found. */\n  | 'not_found'\n  /** 429 Too Many Requests */\n  | 'resource_exhausted'\n  /** Client specified an invalid argument. 4xx. */\n  | 'invalid_argument'\n  /** 501 Not Implemented */\n  | 'unimplemented'\n  /** 503 Service Unavailable */\n  | 'unavailable'\n  /** Other/generic 5xx. */\n  | 'internal_error'\n  /** Unknown. Any non-standard HTTP status code. */\n  | 'unknown_error'\n  /** The operation was cancelled (typically by the user). */\n  | 'cancelled'\n  /** Already exists (409) */\n  | 'already_exists'\n  /** Operation was rejected because the system is not in a state required for the operation's */\n  | 'failed_precondition'\n  /** The operation was aborted, typically due to a concurrency issue. */\n  | 'aborted'\n  /** Operation was attempted past the valid range. */\n  | 'out_of_range'\n  /** Unrecoverable data loss or corruption */\n  | 'data_loss';\n\n/**\n * Converts a HTTP status code into a {@link SpanStatusType}.\n *\n * @param httpStatus The HTTP response status code.\n * @returns The span status or unknown_error.\n */\nexport function getSpanStatusFromHttpCode(httpStatus: number): SpanStatusType {\n  if (httpStatus < 400 && httpStatus >= 100) {\n    return 'ok';\n  }\n\n  if (httpStatus >= 400 && httpStatus < 500) {\n    switch (httpStatus) {\n      case 401:\n        return 'unauthenticated';\n      case 403:\n        return 'permission_denied';\n      case 404:\n        return 'not_found';\n      case 409:\n        return 'already_exists';\n      case 413:\n        return 'failed_precondition';\n      case 429:\n        return 'resource_exhausted';\n      default:\n        return 'invalid_argument';\n    }\n  }\n\n  if (httpStatus >= 500 && httpStatus < 600) {\n    switch (httpStatus) {\n      case 501:\n        return 'unimplemented';\n      case 503:\n        return 'unavailable';\n      case 504:\n        return 'deadline_exceeded';\n      default:\n        return 'internal_error';\n    }\n  }\n\n  return 'unknown_error';\n}\n\n/**\n * Converts a HTTP status code into a {@link SpanStatusType}.\n *\n * @deprecated Use {@link spanStatusFromHttpCode} instead.\n * This export will be removed in v8 as the signature contains a typo.\n *\n * @param httpStatus The HTTP response status code.\n * @returns The span status or unknown_error.\n */\nexport const spanStatusfromHttpCode = getSpanStatusFromHttpCode;\n\n/**\n * Sets the Http status attributes on the current span based on the http code.\n * Additionally, the span's status is updated, depending on the http code.\n */\nexport function setHttpStatus(span: Span, httpStatus: number): void {\n  // TODO (v8): Remove these calls\n  // Relay does not require us to send the status code as a tag\n  // For now, just because users might expect it to land as a tag we keep sending it.\n  // Same with data.\n  // In v8, we replace both, simply with\n  // span.setAttribute('http.response.status_code', httpStatus);\n\n  // eslint-disable-next-line deprecation/deprecation\n  span.setTag('http.status_code', String(httpStatus));\n  // eslint-disable-next-line deprecation/deprecation\n  span.setData('http.response.status_code', httpStatus);\n\n  const spanStatus = getSpanStatusFromHttpCode(httpStatus);\n  if (spanStatus !== 'unknown_error') {\n    span.setStatus(spanStatus);\n  }\n}\n"], "names": [], "mappings": "AAEA;AACA;AACA;AACA;AACuB,IAAA,WAAA,CAAA,CAAA,UAAA,UAAA,EAAA;AACvB;AACA,EAAE,MAAA,EAAA,GAAK,IAAI,CAAA,CAAA,UAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA;AACX;AACA,EAAE,MAAA,gBAAA,GAAmB,mBAAmB,CAAA,CAAA,UAAA,CAAA,kBAAA,CAAA,GAAA,gBAAA,CAAA;AACxC;AACA,EAAE,MAAA,eAAA,GAAkB,iBAAiB,CAAA,CAAA,UAAA,CAAA,iBAAA,CAAA,GAAA,eAAA,CAAA;AACrC;AACA,EAAE,MAAA,gBAAA,GAAmB,mBAAmB,CAAA,CAAA,UAAA,CAAA,kBAAA,CAAA,GAAA,gBAAA,CAAA;AACxC;AACA,EAAE,MAAA,QAAA,GAAW,WAAW,CAAA,CAAA,UAAA,CAAA,UAAA,CAAA,GAAA,QAAA,CAAA;AACxB;AACA,EAAE,MAAA,iBAAA,GAAoB,oBAAoB,CAAA,CAAA,UAAA,CAAA,mBAAA,CAAA,GAAA,iBAAA,CAAA;AAC1C;AACA,EAAE,MAAA,eAAA,GAAkB,kBAAkB,CAAA,CAAA,UAAA,CAAA,iBAAA,CAAA,GAAA,eAAA,CAAA;AACtC;AACA,EAAE,MAAA,aAAA,GAAgB,eAAe,CAAA,CAAA,UAAA,CAAA,eAAA,CAAA,GAAA,aAAA,CAAA;AACjC;AACA,EAAE,MAAA,WAAA,GAAc,aAAa,CAAA,CAAA,UAAA,CAAA,aAAA,CAAA,GAAA,WAAA,CAAA;AAC7B;AACA,EAAE,MAAA,aAAA,GAAgB,gBAAgB,CAAA,CAAA,UAAA,CAAA,eAAA,CAAA,GAAA,aAAA,CAAA;AAClC;AACA,EAAE,MAAA,YAAA,GAAe,eAAe,CAAA,CAAA,UAAA,CAAA,cAAA,CAAA,GAAA,YAAA,CAAA;AAChC;AACA,EAAE,MAAA,SAAA,GAAY,WAAW,CAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,SAAA,CAAA;AACzB;AACA,EAAE,MAAA,aAAA,GAAgB,gBAAgB,CAAA,CAAA,UAAA,CAAA,eAAA,CAAA,GAAA,aAAA,CAAA;AAClC;AACA,EAAE,MAAA,kBAAA,GAAqB,qBAAqB,CAAA,CAAA,UAAA,CAAA,oBAAA,CAAA,GAAA,kBAAA,CAAA;AAC5C;AACA,EAAE,MAAA,OAAA,GAAU,SAAS,CAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,OAAA,CAAA;AACrB;AACA,EAAE,MAAA,UAAA,GAAa,cAAc,CAAA,CAAA,UAAA,CAAA,YAAA,CAAA,GAAA,UAAA,CAAA;AAC7B;AACA,EAAE,MAAA,QAAA,GAAW,WAAW,CAAA,CAAA,UAAA,CAAA,UAAA,CAAA,GAAA,QAAA,CAAA;AACxB,CAAA,EAAA,UAAA,KAAA,UAAA,GAAA,EAAA,CAAA,CAAA,CAAA;;AAsCA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,yBAAyB,CAAC,UAAU,EAA0B;AAC9E,EAAE,IAAI,UAAW,GAAE,OAAO,UAAA,IAAc,GAAG,EAAE;AAC7C,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA,EAAE,IAAI,UAAW,IAAG,OAAO,UAAA,GAAa,GAAG,EAAE;AAC7C,IAAI,QAAQ,UAAU;AACtB,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,iBAAiB,CAAA;AAChC,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,mBAAmB,CAAA;AAClC,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,WAAW,CAAA;AAC1B,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,gBAAgB,CAAA;AAC/B,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,qBAAqB,CAAA;AACpC,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,oBAAoB,CAAA;AACnC,MAAM;AACN,QAAQ,OAAO,kBAAkB,CAAA;AACjC,KAAI;AACJ,GAAE;AACF;AACA,EAAE,IAAI,UAAW,IAAG,OAAO,UAAA,GAAa,GAAG,EAAE;AAC7C,IAAI,QAAQ,UAAU;AACtB,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,eAAe,CAAA;AAC9B,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,aAAa,CAAA;AAC5B,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,mBAAmB,CAAA;AAClC,MAAM;AACN,QAAQ,OAAO,gBAAgB,CAAA;AAC/B,KAAI;AACJ,GAAE;AACF;AACA,EAAE,OAAO,eAAe,CAAA;AACxB,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,sBAAuB,GAAE,0BAAyB;AAC/D;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAC,IAAI,EAAQ,UAAU,EAAgB;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAA;AACrD;AACA,EAAE,IAAI,CAAC,OAAO,CAAC,2BAA2B,EAAE,UAAU,CAAC,CAAA;AACvD;AACA,EAAE,MAAM,UAAW,GAAE,yBAAyB,CAAC,UAAU,CAAC,CAAA;AAC1D,EAAE,IAAI,UAAW,KAAI,eAAe,EAAE;AACtC,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;AAC9B,GAAE;AACF;;;;"}