{"version": 3, "file": "trace.js", "sources": ["../../../src/tracing/trace.ts"], "sourcesContent": ["import type { Scope, Span, SpanTimeInput, StartSpanOptions, TransactionContext } from '@sentry/types';\n\nimport { addNonEnumerableProperty, dropUndefinedKeys, logger, tracingContextFromHeaders } from '@sentry/utils';\nimport { getDynamicSamplingContextFromSpan } from '.';\n\nimport { DEBUG_BUILD } from '../debug-build';\nimport { getCurrentScope, withScope } from '../exports';\nimport type { Hub } from '../hub';\nimport { runWithAsyncContext } from '../hub';\nimport { getIsolationScope } from '../hub';\nimport { getCurrentHub } from '../hub';\nimport type { Scope as ScopeClass } from '../scope';\nimport { handleCallbackErrors } from '../utils/handleCallbackErrors';\nimport { hasTracingEnabled } from '../utils/hasTracingEnabled';\nimport { spanIsSampled, spanTimeInputToSeconds, spanToJSON } from '../utils/spanUtils';\n\n/**\n * Wraps a function with a transaction/span and finishes the span after the function is done.\n *\n * Note that if you have not enabled tracing extensions via `addTracingExtensions`\n * or you didn't set `tracesSampleRate`, this function will not generate spans\n * and the `span` returned from the callback will be undefined.\n *\n * This function is meant to be used internally and may break at any time. Use at your own risk.\n *\n * @internal\n * @private\n *\n * @deprecated Use `startSpan` instead.\n */\nexport function trace<T>(\n  context: TransactionContext,\n  callback: (span?: Span) => T,\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onError: (error: unknown, span?: Span) => void = () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  afterFinish: () => void = () => {},\n): T {\n  // eslint-disable-next-line deprecation/deprecation\n  const hub = getCurrentHub();\n  const scope = getCurrentScope();\n  // eslint-disable-next-line deprecation/deprecation\n  const parentSpan = scope.getSpan();\n\n  const spanContext = normalizeContext(context);\n  const activeSpan = createChildSpanOrTransaction(hub, {\n    parentSpan,\n    spanContext,\n    forceTransaction: false,\n    scope,\n  });\n\n  // eslint-disable-next-line deprecation/deprecation\n  scope.setSpan(activeSpan);\n\n  return handleCallbackErrors(\n    () => callback(activeSpan),\n    error => {\n      activeSpan && activeSpan.setStatus('internal_error');\n      onError(error, activeSpan);\n    },\n    () => {\n      activeSpan && activeSpan.end();\n      // eslint-disable-next-line deprecation/deprecation\n      scope.setSpan(parentSpan);\n      afterFinish();\n    },\n  );\n}\n\n/**\n * Wraps a function with a transaction/span and finishes the span after the function is done.\n * The created span is the active span and will be used as parent by other spans created inside the function\n * and can be accessed via `Sentry.getSpan()`, as long as the function is executed while the scope is active.\n *\n * If you want to create a span that is not set as active, use {@link startInactiveSpan}.\n *\n * Note that if you have not enabled tracing extensions via `addTracingExtensions`\n * or you didn't set `tracesSampleRate`, this function will not generate spans\n * and the `span` returned from the callback will be undefined.\n */\nexport function startSpan<T>(context: StartSpanOptions, callback: (span: Span | undefined) => T): T {\n  const spanContext = normalizeContext(context);\n\n  return runWithAsyncContext(() => {\n    return withScope(context.scope, scope => {\n      // eslint-disable-next-line deprecation/deprecation\n      const hub = getCurrentHub();\n      // eslint-disable-next-line deprecation/deprecation\n      const parentSpan = scope.getSpan();\n\n      const shouldSkipSpan = context.onlyIfParent && !parentSpan;\n      const activeSpan = shouldSkipSpan\n        ? undefined\n        : createChildSpanOrTransaction(hub, {\n            parentSpan,\n            spanContext,\n            forceTransaction: context.forceTransaction,\n            scope,\n          });\n\n      return handleCallbackErrors(\n        () => callback(activeSpan),\n        () => {\n          // Only update the span status if it hasn't been changed yet\n          if (activeSpan) {\n            const { status } = spanToJSON(activeSpan);\n            if (!status || status === 'ok') {\n              activeSpan.setStatus('internal_error');\n            }\n          }\n        },\n        () => activeSpan && activeSpan.end(),\n      );\n    });\n  });\n}\n\n/**\n * @deprecated Use {@link startSpan} instead.\n */\nexport const startActiveSpan = startSpan;\n\n/**\n * Similar to `Sentry.startSpan`. Wraps a function with a transaction/span, but does not finish the span\n * after the function is done automatically. You'll have to call `span.end()` manually.\n *\n * The created span is the active span and will be used as parent by other spans created inside the function\n * and can be accessed via `Sentry.getActiveSpan()`, as long as the function is executed while the scope is active.\n *\n * Note that if you have not enabled tracing extensions via `addTracingExtensions`\n * or you didn't set `tracesSampleRate`, this function will not generate spans\n * and the `span` returned from the callback will be undefined.\n */\nexport function startSpanManual<T>(\n  context: StartSpanOptions,\n  callback: (span: Span | undefined, finish: () => void) => T,\n): T {\n  const spanContext = normalizeContext(context);\n\n  return runWithAsyncContext(() => {\n    return withScope(context.scope, scope => {\n      // eslint-disable-next-line deprecation/deprecation\n      const hub = getCurrentHub();\n      // eslint-disable-next-line deprecation/deprecation\n      const parentSpan = scope.getSpan();\n\n      const shouldSkipSpan = context.onlyIfParent && !parentSpan;\n      const activeSpan = shouldSkipSpan\n        ? undefined\n        : createChildSpanOrTransaction(hub, {\n            parentSpan,\n            spanContext,\n            forceTransaction: context.forceTransaction,\n            scope,\n          });\n\n      function finishAndSetSpan(): void {\n        activeSpan && activeSpan.end();\n      }\n\n      return handleCallbackErrors(\n        () => callback(activeSpan, finishAndSetSpan),\n        () => {\n          // Only update the span status if it hasn't been changed yet, and the span is not yet finished\n          if (activeSpan && activeSpan.isRecording()) {\n            const { status } = spanToJSON(activeSpan);\n            if (!status || status === 'ok') {\n              activeSpan.setStatus('internal_error');\n            }\n          }\n        },\n      );\n    });\n  });\n}\n\n/**\n * Creates a span. This span is not set as active, so will not get automatic instrumentation spans\n * as children or be able to be accessed via `Sentry.getSpan()`.\n *\n * If you want to create a span that is set as active, use {@link startSpan}.\n *\n * Note that if you have not enabled tracing extensions via `addTracingExtensions`\n * or you didn't set `tracesSampleRate` or `tracesSampler`, this function will not generate spans\n * and the `span` returned from the callback will be undefined.\n */\nexport function startInactiveSpan(context: StartSpanOptions): Span | undefined {\n  if (!hasTracingEnabled()) {\n    return undefined;\n  }\n\n  const spanContext = normalizeContext(context);\n  // eslint-disable-next-line deprecation/deprecation\n  const hub = getCurrentHub();\n  const parentSpan = context.scope\n    ? // eslint-disable-next-line deprecation/deprecation\n      context.scope.getSpan()\n    : getActiveSpan();\n\n  const shouldSkipSpan = context.onlyIfParent && !parentSpan;\n\n  if (shouldSkipSpan) {\n    return undefined;\n  }\n\n  const scope = context.scope || getCurrentScope();\n\n  // Even though we don't actually want to make this span active on the current scope,\n  // we need to make it active on a temporary scope that we use for event processing\n  // as otherwise, it won't pick the correct span for the event when processing it\n  const temporaryScope = (scope as ScopeClass).clone();\n\n  return createChildSpanOrTransaction(hub, {\n    parentSpan,\n    spanContext,\n    forceTransaction: context.forceTransaction,\n    scope: temporaryScope,\n  });\n}\n\n/**\n * Returns the currently active span.\n */\nexport function getActiveSpan(): Span | undefined {\n  // eslint-disable-next-line deprecation/deprecation\n  return getCurrentScope().getSpan();\n}\n\ninterface ContinueTrace {\n  /**\n   * Continue a trace from `sentry-trace` and `baggage` values.\n   * These values can be obtained from incoming request headers,\n   * or in the browser from `<meta name=\"sentry-trace\">` and `<meta name=\"baggage\">` HTML tags.\n   *\n   * @deprecated Use the version of this function taking a callback as second parameter instead:\n   *\n   * ```\n   * Sentry.continueTrace(sentryTrace: '...', baggage: '...' }, () => {\n   *    // ...\n   * })\n   * ```\n   *\n   */\n  ({\n    sentryTrace,\n    baggage,\n  }: {\n    // eslint-disable-next-line deprecation/deprecation\n    sentryTrace: Parameters<typeof tracingContextFromHeaders>[0];\n    // eslint-disable-next-line deprecation/deprecation\n    baggage: Parameters<typeof tracingContextFromHeaders>[1];\n  }): Partial<TransactionContext>;\n\n  /**\n   * Continue a trace from `sentry-trace` and `baggage` values.\n   * These values can be obtained from incoming request headers, or in the browser from `<meta name=\"sentry-trace\">`\n   * and `<meta name=\"baggage\">` HTML tags.\n   *\n   * Spans started with `startSpan`, `startSpanManual` and `startInactiveSpan`, within the callback will automatically\n   * be attached to the incoming trace.\n   *\n   * Deprecation notice: In the next major version of the SDK the provided callback will not receive a transaction\n   * context argument.\n   */\n  <V>(\n    {\n      sentryTrace,\n      baggage,\n    }: {\n      // eslint-disable-next-line deprecation/deprecation\n      sentryTrace: Parameters<typeof tracingContextFromHeaders>[0];\n      // eslint-disable-next-line deprecation/deprecation\n      baggage: Parameters<typeof tracingContextFromHeaders>[1];\n    },\n    // TODO(v8): Remove parameter from this callback.\n    callback: (transactionContext: Partial<TransactionContext>) => V,\n  ): V;\n}\n\nexport const continueTrace: ContinueTrace = <V>(\n  {\n    sentryTrace,\n    baggage,\n  }: {\n    // eslint-disable-next-line deprecation/deprecation\n    sentryTrace: Parameters<typeof tracingContextFromHeaders>[0];\n    // eslint-disable-next-line deprecation/deprecation\n    baggage: Parameters<typeof tracingContextFromHeaders>[1];\n  },\n  callback?: (transactionContext: Partial<TransactionContext>) => V,\n): V | Partial<TransactionContext> => {\n  // TODO(v8): Change this function so it doesn't do anything besides setting the propagation context on the current scope:\n  /*\n    return withScope((scope) => {\n      const propagationContext = propagationContextFromHeaders(sentryTrace, baggage);\n      scope.setPropagationContext(propagationContext);\n      return callback();\n    })\n  */\n\n  const currentScope = getCurrentScope();\n\n  // eslint-disable-next-line deprecation/deprecation\n  const { traceparentData, dynamicSamplingContext, propagationContext } = tracingContextFromHeaders(\n    sentryTrace,\n    baggage,\n  );\n\n  currentScope.setPropagationContext(propagationContext);\n\n  if (DEBUG_BUILD && traceparentData) {\n    logger.log(`[Tracing] Continuing trace ${traceparentData.traceId}.`);\n  }\n\n  const transactionContext: Partial<TransactionContext> = {\n    ...traceparentData,\n    metadata: dropUndefinedKeys({\n      dynamicSamplingContext,\n    }),\n  };\n\n  if (!callback) {\n    return transactionContext;\n  }\n\n  return runWithAsyncContext(() => {\n    return callback(transactionContext);\n  });\n};\n\nfunction createChildSpanOrTransaction(\n  // eslint-disable-next-line deprecation/deprecation\n  hub: Hub,\n  {\n    parentSpan,\n    spanContext,\n    forceTransaction,\n    scope,\n  }: {\n    parentSpan: Span | undefined;\n    spanContext: TransactionContext;\n    forceTransaction?: boolean;\n    scope: Scope;\n  },\n): Span | undefined {\n  if (!hasTracingEnabled()) {\n    return undefined;\n  }\n\n  const isolationScope = getIsolationScope();\n\n  let span: Span | undefined;\n  if (parentSpan && !forceTransaction) {\n    // eslint-disable-next-line deprecation/deprecation\n    span = parentSpan.startChild(spanContext);\n  } else if (parentSpan) {\n    // If we forced a transaction but have a parent span, make sure to continue from the parent span, not the scope\n    const dsc = getDynamicSamplingContextFromSpan(parentSpan);\n    const { traceId, spanId: parentSpanId } = parentSpan.spanContext();\n    const sampled = spanIsSampled(parentSpan);\n\n    // eslint-disable-next-line deprecation/deprecation\n    span = hub.startTransaction({\n      traceId,\n      parentSpanId,\n      parentSampled: sampled,\n      ...spanContext,\n      metadata: {\n        dynamicSamplingContext: dsc,\n        // eslint-disable-next-line deprecation/deprecation\n        ...spanContext.metadata,\n      },\n    });\n  } else {\n    const { traceId, dsc, parentSpanId, sampled } = {\n      ...isolationScope.getPropagationContext(),\n      ...scope.getPropagationContext(),\n    };\n\n    // eslint-disable-next-line deprecation/deprecation\n    span = hub.startTransaction({\n      traceId,\n      parentSpanId,\n      parentSampled: sampled,\n      ...spanContext,\n      metadata: {\n        dynamicSamplingContext: dsc,\n        // eslint-disable-next-line deprecation/deprecation\n        ...spanContext.metadata,\n      },\n    });\n  }\n\n  // We always set this as active span on the scope\n  // In the case of this being an inactive span, we ensure to pass a detached scope in here in the first place\n  // But by having this here, we can ensure that the lookup through `getCapturedScopesOnSpan` results in the correct scope & span combo\n  // eslint-disable-next-line deprecation/deprecation\n  scope.setSpan(span);\n\n  setCapturedScopesOnSpan(span, scope, isolationScope);\n\n  return span;\n}\n\n/**\n * This converts StartSpanOptions to TransactionContext.\n * For the most part (for now) we accept the same options,\n * but some of them need to be transformed.\n *\n * Eventually the StartSpanOptions will be more aligned with OpenTelemetry.\n */\nfunction normalizeContext(context: StartSpanOptions): TransactionContext {\n  if (context.startTime) {\n    const ctx: TransactionContext & { startTime?: SpanTimeInput } = { ...context };\n    ctx.startTimestamp = spanTimeInputToSeconds(context.startTime);\n    delete ctx.startTime;\n    return ctx;\n  }\n\n  return context;\n}\n\nconst SCOPE_ON_START_SPAN_FIELD = '_sentryScope';\nconst ISOLATION_SCOPE_ON_START_SPAN_FIELD = '_sentryIsolationScope';\n\ntype SpanWithScopes = Span & {\n  [SCOPE_ON_START_SPAN_FIELD]?: Scope;\n  [ISOLATION_SCOPE_ON_START_SPAN_FIELD]?: Scope;\n};\n\nfunction setCapturedScopesOnSpan(span: Span | undefined, scope: Scope, isolationScope: Scope): void {\n  if (span) {\n    addNonEnumerableProperty(span, ISOLATION_SCOPE_ON_START_SPAN_FIELD, isolationScope);\n    addNonEnumerableProperty(span, SCOPE_ON_START_SPAN_FIELD, scope);\n  }\n}\n\n/**\n * Grabs the scope and isolation scope off a span that were active when the span was started.\n */\nexport function getCapturedScopesOnSpan(span: Span): { scope?: Scope; isolationScope?: Scope } {\n  return {\n    scope: (span as SpanWithScopes)[SCOPE_ON_START_SPAN_FIELD],\n    isolationScope: (span as SpanWithScopes)[ISOLATION_SCOPE_ON_START_SPAN_FIELD],\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,KAAK;AACrB,EAAE,OAAO;AACT,EAAE,QAAQ;AACV;AACA,EAAE,OAAO,GAA0C,MAAM,EAAE;AAC3D;AACA,EAAE,WAAW,GAAe,MAAM,EAAE;AACpC,EAAK;AACL;AACA,EAAE,MAAM,GAAA,GAAM,aAAa,EAAE,CAAA;AAC7B,EAAE,MAAM,KAAA,GAAQ,eAAe,EAAE,CAAA;AACjC;AACA,EAAE,MAAM,UAAW,GAAE,KAAK,CAAC,OAAO,EAAE,CAAA;AACpC;AACA,EAAE,MAAM,WAAY,GAAE,gBAAgB,CAAC,OAAO,CAAC,CAAA;AAC/C,EAAE,MAAM,UAAW,GAAE,4BAA4B,CAAC,GAAG,EAAE;AACvD,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,gBAAgB,EAAE,KAAK;AAC3B,IAAI,KAAK;AACT,GAAG,CAAC,CAAA;AACJ;AACA;AACA,EAAE,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;AAC3B;AACA,EAAE,OAAO,oBAAoB;AAC7B,IAAI,MAAM,QAAQ,CAAC,UAAU,CAAC;AAC9B,IAAI,SAAS;AACb,MAAM,cAAc,UAAU,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAA;AAC1D,MAAM,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;AAChC,KAAK;AACL,IAAI,MAAM;AACV,MAAM,cAAc,UAAU,CAAC,GAAG,EAAE,CAAA;AACpC;AACA,MAAM,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;AAC/B,MAAM,WAAW,EAAE,CAAA;AACnB,KAAK;AACL,GAAG,CAAA;AACH,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,SAAS,CAAI,OAAO,EAAoB,QAAQ,EAAoC;AACpG,EAAE,MAAM,WAAY,GAAE,gBAAgB,CAAC,OAAO,CAAC,CAAA;AAC/C;AACA,EAAE,OAAO,mBAAmB,CAAC,MAAM;AACnC,IAAI,OAAO,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS;AAC7C;AACA,MAAM,MAAM,GAAA,GAAM,aAAa,EAAE,CAAA;AACjC;AACA,MAAM,MAAM,UAAW,GAAE,KAAK,CAAC,OAAO,EAAE,CAAA;AACxC;AACA,MAAM,MAAM,iBAAiB,OAAO,CAAC,YAAa,IAAG,CAAC,UAAU,CAAA;AAChE,MAAM,MAAM,aAAa,cAAA;AACzB,UAAU,SAAA;AACV,UAAU,4BAA4B,CAAC,GAAG,EAAE;AAC5C,YAAY,UAAU;AACtB,YAAY,WAAW;AACvB,YAAY,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;AACtD,YAAY,KAAK;AACjB,WAAW,CAAC,CAAA;AACZ;AACA,MAAM,OAAO,oBAAoB;AACjC,QAAQ,MAAM,QAAQ,CAAC,UAAU,CAAC;AAClC,QAAQ,MAAM;AACd;AACA,UAAU,IAAI,UAAU,EAAE;AAC1B,YAAY,MAAM,EAAE,MAAO,EAAA,GAAI,UAAU,CAAC,UAAU,CAAC,CAAA;AACrD,YAAY,IAAI,CAAC,MAAA,IAAU,MAAO,KAAI,IAAI,EAAE;AAC5C,cAAc,UAAU,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAA;AACpD,aAAY;AACZ,WAAU;AACV,SAAS;AACT,QAAQ,MAAM,UAAA,IAAc,UAAU,CAAC,GAAG,EAAE;AAC5C,OAAO,CAAA;AACP,KAAK,CAAC,CAAA;AACN,GAAG,CAAC,CAAA;AACJ,CAAA;AACA;AACA;AACA;AACA;AACO,MAAM,eAAgB,GAAE,UAAS;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe;AAC/B,EAAE,OAAO;AACT,EAAE,QAAQ;AACV,EAAK;AACL,EAAE,MAAM,WAAY,GAAE,gBAAgB,CAAC,OAAO,CAAC,CAAA;AAC/C;AACA,EAAE,OAAO,mBAAmB,CAAC,MAAM;AACnC,IAAI,OAAO,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS;AAC7C;AACA,MAAM,MAAM,GAAA,GAAM,aAAa,EAAE,CAAA;AACjC;AACA,MAAM,MAAM,UAAW,GAAE,KAAK,CAAC,OAAO,EAAE,CAAA;AACxC;AACA,MAAM,MAAM,iBAAiB,OAAO,CAAC,YAAa,IAAG,CAAC,UAAU,CAAA;AAChE,MAAM,MAAM,aAAa,cAAA;AACzB,UAAU,SAAA;AACV,UAAU,4BAA4B,CAAC,GAAG,EAAE;AAC5C,YAAY,UAAU;AACtB,YAAY,WAAW;AACvB,YAAY,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;AACtD,YAAY,KAAK;AACjB,WAAW,CAAC,CAAA;AACZ;AACA,MAAM,SAAS,gBAAgB,GAAS;AACxC,QAAQ,cAAc,UAAU,CAAC,GAAG,EAAE,CAAA;AACtC,OAAM;AACN;AACA,MAAM,OAAO,oBAAoB;AACjC,QAAQ,MAAM,QAAQ,CAAC,UAAU,EAAE,gBAAgB,CAAC;AACpD,QAAQ,MAAM;AACd;AACA,UAAU,IAAI,UAAW,IAAG,UAAU,CAAC,WAAW,EAAE,EAAE;AACtD,YAAY,MAAM,EAAE,MAAO,EAAA,GAAI,UAAU,CAAC,UAAU,CAAC,CAAA;AACrD,YAAY,IAAI,CAAC,MAAA,IAAU,MAAO,KAAI,IAAI,EAAE;AAC5C,cAAc,UAAU,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAA;AACpD,aAAY;AACZ,WAAU;AACV,SAAS;AACT,OAAO,CAAA;AACP,KAAK,CAAC,CAAA;AACN,GAAG,CAAC,CAAA;AACJ,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,OAAO,EAAsC;AAC/E,EAAE,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC5B,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;AACA,EAAE,MAAM,WAAY,GAAE,gBAAgB,CAAC,OAAO,CAAC,CAAA;AAC/C;AACA,EAAE,MAAM,GAAA,GAAM,aAAa,EAAE,CAAA;AAC7B,EAAE,MAAM,UAAA,GAAa,OAAO,CAAC,KAAA;AAC7B;AACA,MAAM,OAAO,CAAC,KAAK,CAAC,OAAO,EAAC;AAC5B,MAAM,aAAa,EAAE,CAAA;AACrB;AACA,EAAE,MAAM,iBAAiB,OAAO,CAAC,YAAa,IAAG,CAAC,UAAU,CAAA;AAC5D;AACA,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;AACA,EAAE,MAAM,QAAQ,OAAO,CAAC,KAAM,IAAG,eAAe,EAAE,CAAA;AAClD;AACA;AACA;AACA;AACA,EAAE,MAAM,iBAAiB,CAAC,QAAqB,KAAK,EAAE,CAAA;AACtD;AACA,EAAE,OAAO,4BAA4B,CAAC,GAAG,EAAE;AAC3C,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;AAC9C,IAAI,KAAK,EAAE,cAAc;AACzB,GAAG,CAAC,CAAA;AACJ,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,GAAqB;AAClD;AACA,EAAE,OAAO,eAAe,EAAE,CAAC,OAAO,EAAE,CAAA;AACpC,CAAA;;AAqDO,MAAM,aAAa,GAAkB;AAC5C,EAAE;AACF,IAAI,WAAW;AACf,IAAI,OAAO;AACX,GAAG;;AAKD;AACF,EAAE,QAAQ;AACV,KAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,YAAA,GAAe,eAAe,EAAE,CAAA;AACxC;AACA;AACA,EAAE,MAAM,EAAE,eAAe,EAAE,sBAAsB,EAAE,kBAAA,EAAqB,GAAE,yBAAyB;AACnG,IAAI,WAAW;AACf,IAAI,OAAO;AACX,GAAG,CAAA;AACH;AACA,EAAE,YAAY,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,CAAA;AACxD;AACA,EAAE,IAAI,WAAY,IAAG,eAAe,EAAE;AACtC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;AACxE,GAAE;AACF;AACA,EAAE,MAAM,kBAAkB,GAAgC;AAC1D,IAAI,GAAG,eAAe;AACtB,IAAI,QAAQ,EAAE,iBAAiB,CAAC;AAChC,MAAM,sBAAsB;AAC5B,KAAK,CAAC;AACN,GAAG,CAAA;AACH;AACA,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,OAAO,kBAAkB,CAAA;AAC7B,GAAE;AACF;AACA,EAAE,OAAO,mBAAmB,CAAC,MAAM;AACnC,IAAI,OAAO,QAAQ,CAAC,kBAAkB,CAAC,CAAA;AACvC,GAAG,CAAC,CAAA;AACJ,EAAC;AACD;AACA,SAAS,4BAA4B;AACrC;AACA,EAAE,GAAG;AACL,EAAE;AACF,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,gBAAgB;AACpB,IAAI,KAAK;AACT,GAAG;;AAKD;AACF,EAAoB;AACpB,EAAE,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC5B,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;AACA,EAAE,MAAM,cAAA,GAAiB,iBAAiB,EAAE,CAAA;AAC5C;AACA,EAAE,IAAI,IAAI,CAAA;AACV,EAAE,IAAI,UAAA,IAAc,CAAC,gBAAgB,EAAE;AACvC;AACA,IAAI,OAAO,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,CAAA;AAC7C,GAAI,MAAK,IAAI,UAAU,EAAE;AACzB;AACA,IAAI,MAAM,GAAI,GAAE,iCAAiC,CAAC,UAAU,CAAC,CAAA;AAC7D,IAAI,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,YAAa,EAAA,GAAI,UAAU,CAAC,WAAW,EAAE,CAAA;AACtE,IAAI,MAAM,OAAQ,GAAE,aAAa,CAAC,UAAU,CAAC,CAAA;AAC7C;AACA;AACA,IAAI,IAAK,GAAE,GAAG,CAAC,gBAAgB,CAAC;AAChC,MAAM,OAAO;AACb,MAAM,YAAY;AAClB,MAAM,aAAa,EAAE,OAAO;AAC5B,MAAM,GAAG,WAAW;AACpB,MAAM,QAAQ,EAAE;AAChB,QAAQ,sBAAsB,EAAE,GAAG;AACnC;AACA,QAAQ,GAAG,WAAW,CAAC,QAAQ;AAC/B,OAAO;AACP,KAAK,CAAC,CAAA;AACN,SAAS;AACT,IAAI,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,YAAY,EAAE,OAAQ,EAAA,GAAI;AACpD,MAAM,GAAG,cAAc,CAAC,qBAAqB,EAAE;AAC/C,MAAM,GAAG,KAAK,CAAC,qBAAqB,EAAE;AACtC,KAAK,CAAA;AACL;AACA;AACA,IAAI,IAAK,GAAE,GAAG,CAAC,gBAAgB,CAAC;AAChC,MAAM,OAAO;AACb,MAAM,YAAY;AAClB,MAAM,aAAa,EAAE,OAAO;AAC5B,MAAM,GAAG,WAAW;AACpB,MAAM,QAAQ,EAAE;AAChB,QAAQ,sBAAsB,EAAE,GAAG;AACnC;AACA,QAAQ,GAAG,WAAW,CAAC,QAAQ;AAC/B,OAAO;AACP,KAAK,CAAC,CAAA;AACN,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;AACrB;AACA,EAAE,uBAAuB,CAAC,IAAI,EAAE,KAAK,EAAE,cAAc,CAAC,CAAA;AACtD;AACA,EAAE,OAAO,IAAI,CAAA;AACb,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,OAAO,EAAwC;AACzE,EAAE,IAAI,OAAO,CAAC,SAAS,EAAE;AACzB,IAAI,MAAM,GAAG,GAAuD,EAAE,GAAG,SAAS,CAAA;AAClF,IAAI,GAAG,CAAC,cAAe,GAAE,sBAAsB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;AAClE,IAAI,OAAO,GAAG,CAAC,SAAS,CAAA;AACxB,IAAI,OAAO,GAAG,CAAA;AACd,GAAE;AACF;AACA,EAAE,OAAO,OAAO,CAAA;AAChB,CAAA;AACA;AACA,MAAM,yBAAA,GAA4B,cAAc,CAAA;AAChD,MAAM,mCAAA,GAAsC,uBAAuB,CAAA;;AAOnE,SAAS,uBAAuB,CAAC,IAAI,EAAoB,KAAK,EAAS,cAAc,EAAe;AACpG,EAAE,IAAI,IAAI,EAAE;AACZ,IAAI,wBAAwB,CAAC,IAAI,EAAE,mCAAmC,EAAE,cAAc,CAAC,CAAA;AACvF,IAAI,wBAAwB,CAAC,IAAI,EAAE,yBAAyB,EAAE,KAAK,CAAC,CAAA;AACpE,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,uBAAuB,CAAC,IAAI,EAAmD;AAC/F,EAAE,OAAO;AACT,IAAI,KAAK,EAAE,CAAC,OAAwB,yBAAyB,CAAC;AAC9D,IAAI,cAAc,EAAE,CAAC,OAAwB,mCAAmC,CAAC;AACjF,GAAG,CAAA;AACH;;;;"}