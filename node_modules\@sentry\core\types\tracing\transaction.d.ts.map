{"version": 3, "file": "transaction.d.ts", "sourceRoot": "", "sources": ["../../../src/tracing/transaction.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,OAAO,EAEP,sBAAsB,EACtB,eAAe,EACf,aAAa,EACb,WAAW,IAAI,oBAAoB,EACnC,kBAAkB,EAClB,gBAAgB,EAChB,mBAAmB,EACpB,MAAM,eAAe,CAAC;AAIvB,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,QAAQ,CAAC;AAMlC,OAAO,EAAE,IAAI,IAAI,SAAS,EAAgB,MAAM,QAAQ,CAAC;AAGzD,YAAY;AACZ,qBAAa,WAAY,SAAQ,SAAU,YAAW,oBAAoB;IACxE;;OAEG;IAEI,IAAI,EAAE,GAAG,CAAC;IAEjB,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC;IAExB,OAAO,CAAC,SAAS,CAAW;IAE5B,OAAO,CAAC,QAAQ,CAAC,CAAsB;IAGvC,OAAO,CAAC,6BAA6B,CAAwD;IAE7F,OAAO,CAAC,SAAS,CAA+B;IAEhD;;;;;;;;OAQG;gBAEgB,kBAAkB,EAAE,kBAAkB,EAAE,GAAG,CAAC,EAAE,GAAG;IAiCpE;;;OAGG;IACH,IAAW,IAAI,IAAI,MAAM,CAExB;IAED;;;OAGG;IACH,IAAW,IAAI,CAAC,OAAO,EAAE,MAAM,EAG9B;IAED;;;OAGG;IACH,IAAW,QAAQ,IAAI,mBAAmB,CAmBzC;IAED;;;OAGG;IACH,IAAW,QAAQ,CAAC,QAAQ,EAAE,mBAAmB,EAEhD;IAID;;;;OAIG;IACI,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,GAAE,mBAAmB,CAAC,QAAQ,CAAY,GAAG,IAAI;IAKpF,kBAAkB;IACX,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAKrC;;;OAGG;IACI,gBAAgB,CAAC,MAAM,GAAE,MAAa,GAAG,IAAI;IAUpD;;;OAGG;IACI,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI;IAS7D;;;;OAIG;IACI,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,GAAE,eAAoB,GAAG,IAAI;IAIpF;;;OAGG;IACI,WAAW,CAAC,WAAW,EAAE,OAAO,CAAC,mBAAmB,CAAC,GAAG,IAAI;IAInE;;OAEG;IACI,GAAG,CAAC,YAAY,CAAC,EAAE,aAAa,GAAG,MAAM,GAAG,SAAS;IAU5D;;OAEG;IACI,SAAS,IAAI,kBAAkB;IAWtC;;OAEG;IACI,iBAAiB,CAAC,kBAAkB,EAAE,kBAAkB,GAAG,IAAI;IAUtE;;;;;;OAMG;IACI,yBAAyB,IAAI,QAAQ,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;IAI7E;;;;;OAKG;IAEI,MAAM,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI;IAI7B;;OAEG;IACI,YAAY,IAAI,MAAM,GAAG,SAAS;IAOzC;;OAEG;IACH,SAAS,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAE,MAAM,GAAG,gBAAgB,GAAG,SAAS;CAiGlF"}