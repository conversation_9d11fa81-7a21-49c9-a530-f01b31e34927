{"version": 3, "file": "exports.js", "sources": ["../../src/exports.ts"], "sourcesContent": ["import type {\n  Breadcrumb,\n  BreadcrumbHint,\n  CaptureContext,\n  CheckIn,\n  Client,\n  CustomSamplingContext,\n  Event,\n  EventHint,\n  Extra,\n  Extras,\n  FinishedCheckIn,\n  MonitorConfig,\n  Primitive,\n  Scope as ScopeInterface,\n  Session,\n  SessionContext,\n  Severity,\n  SeverityLevel,\n  Span,\n  TransactionContext,\n  User,\n} from '@sentry/types';\nimport { GLOBAL_OBJ, isThenable, logger, timestampInSeconds, uuid4 } from '@sentry/utils';\n\nimport { DEFAULT_ENVIRONMENT } from './constants';\nimport { DEBUG_BUILD } from './debug-build';\nimport type { Hub } from './hub';\nimport { runWithAsyncContext } from './hub';\nimport { getCurrentHub, getIsolationScope } from './hub';\nimport type { Scope } from './scope';\nimport { closeSession, makeSession, updateSession } from './session';\nimport type { ExclusiveEventHintOrCaptureContext } from './utils/prepareEvent';\nimport { parseEventHintOrCaptureContext } from './utils/prepareEvent';\n\n/**\n * Captures an exception event and sends it to Sentry.\n *\n * @param exception The exception to capture.\n * @param hint Optional additional data to attach to the Sentry event.\n * @returns the id of the captured Sentry event.\n */\nexport function captureException(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  exception: any,\n  hint?: ExclusiveEventHintOrCaptureContext,\n): string {\n  // eslint-disable-next-line deprecation/deprecation\n  return getCurrentHub().captureException(exception, parseEventHintOrCaptureContext(hint));\n}\n\n/**\n * Captures a message event and sends it to Sentry.\n *\n * @param exception The exception to capture.\n * @param captureContext Define the level of the message or pass in additional data to attach to the message.\n * @returns the id of the captured message.\n */\nexport function captureMessage(\n  message: string,\n  // eslint-disable-next-line deprecation/deprecation\n  captureContext?: CaptureContext | Severity | SeverityLevel,\n): string {\n  // This is necessary to provide explicit scopes upgrade, without changing the original\n  // arity of the `captureMessage(message, level)` method.\n  const level = typeof captureContext === 'string' ? captureContext : undefined;\n  const context = typeof captureContext !== 'string' ? { captureContext } : undefined;\n  // eslint-disable-next-line deprecation/deprecation\n  return getCurrentHub().captureMessage(message, level, context);\n}\n\n/**\n * Captures a manually created event and sends it to Sentry.\n *\n * @param exception The event to send to Sentry.\n * @param hint Optional additional data to attach to the Sentry event.\n * @returns the id of the captured event.\n */\nexport function captureEvent(event: Event, hint?: EventHint): string {\n  // eslint-disable-next-line deprecation/deprecation\n  return getCurrentHub().captureEvent(event, hint);\n}\n\n/**\n * Callback to set context information onto the scope.\n * @param callback Callback function that receives Scope.\n *\n * @deprecated Use getCurrentScope() directly.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport function configureScope(callback: (scope: Scope) => void): ReturnType<Hub['configureScope']> {\n  // eslint-disable-next-line deprecation/deprecation\n  getCurrentHub().configureScope(callback);\n}\n\n/**\n * Records a new breadcrumb which will be attached to future events.\n *\n * Breadcrumbs will be added to subsequent events to provide more context on\n * user's actions prior to an error or crash.\n *\n * @param breadcrumb The breadcrumb to record.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport function addBreadcrumb(breadcrumb: Breadcrumb, hint?: BreadcrumbHint): ReturnType<Hub['addBreadcrumb']> {\n  // eslint-disable-next-line deprecation/deprecation\n  getCurrentHub().addBreadcrumb(breadcrumb, hint);\n}\n\n/**\n * Sets context data with the given name.\n * @param name of the context\n * @param context Any kind of data. This data will be normalized.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any, deprecation/deprecation\nexport function setContext(name: string, context: { [key: string]: any } | null): ReturnType<Hub['setContext']> {\n  // eslint-disable-next-line deprecation/deprecation\n  getCurrentHub().setContext(name, context);\n}\n\n/**\n * Set an object that will be merged sent as extra data with the event.\n * @param extras Extras object to merge into current context.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport function setExtras(extras: Extras): ReturnType<Hub['setExtras']> {\n  // eslint-disable-next-line deprecation/deprecation\n  getCurrentHub().setExtras(extras);\n}\n\n/**\n * Set key:value that will be sent as extra data with the event.\n * @param key String of extra\n * @param extra Any kind of data. This data will be normalized.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport function setExtra(key: string, extra: Extra): ReturnType<Hub['setExtra']> {\n  // eslint-disable-next-line deprecation/deprecation\n  getCurrentHub().setExtra(key, extra);\n}\n\n/**\n * Set an object that will be merged sent as tags data with the event.\n * @param tags Tags context object to merge into current context.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport function setTags(tags: { [key: string]: Primitive }): ReturnType<Hub['setTags']> {\n  // eslint-disable-next-line deprecation/deprecation\n  getCurrentHub().setTags(tags);\n}\n\n/**\n * Set key:value that will be sent as tags data with the event.\n *\n * Can also be used to unset a tag, by passing `undefined`.\n *\n * @param key String key of tag\n * @param value Value of tag\n */\n// eslint-disable-next-line deprecation/deprecation\nexport function setTag(key: string, value: Primitive): ReturnType<Hub['setTag']> {\n  // eslint-disable-next-line deprecation/deprecation\n  getCurrentHub().setTag(key, value);\n}\n\n/**\n * Updates user context information for future events.\n *\n * @param user User context object to be set in the current context. Pass `null` to unset the user.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport function setUser(user: User | null): ReturnType<Hub['setUser']> {\n  // eslint-disable-next-line deprecation/deprecation\n  getCurrentHub().setUser(user);\n}\n\n/**\n * Creates a new scope with and executes the given operation within.\n * The scope is automatically removed once the operation\n * finishes or throws.\n *\n * This is essentially a convenience function for:\n *\n *     pushScope();\n *     callback();\n *     popScope();\n */\nexport function withScope<T>(callback: (scope: Scope) => T): T;\n/**\n * Set the given scope as the active scope in the callback.\n */\nexport function withScope<T>(scope: ScopeInterface | undefined, callback: (scope: Scope) => T): T;\n/**\n * Either creates a new active scope, or sets the given scope as active scope in the given callback.\n */\nexport function withScope<T>(\n  ...rest: [callback: (scope: Scope) => T] | [scope: ScopeInterface | undefined, callback: (scope: Scope) => T]\n): T {\n  // eslint-disable-next-line deprecation/deprecation\n  const hub = getCurrentHub();\n\n  // If a scope is defined, we want to make this the active scope instead of the default one\n  if (rest.length === 2) {\n    const [scope, callback] = rest;\n    if (!scope) {\n      // eslint-disable-next-line deprecation/deprecation\n      return hub.withScope(callback);\n    }\n\n    // eslint-disable-next-line deprecation/deprecation\n    return hub.withScope(() => {\n      // eslint-disable-next-line deprecation/deprecation\n      hub.getStackTop().scope = scope as Scope;\n      return callback(scope as Scope);\n    });\n  }\n\n  // eslint-disable-next-line deprecation/deprecation\n  return hub.withScope(rest[0]);\n}\n\n/**\n * Attempts to fork the current isolation scope and the current scope based on the current async context strategy. If no\n * async context strategy is set, the isolation scope and the current scope will not be forked (this is currently the\n * case, for example, in the browser).\n *\n * Usage of this function in environments without async context strategy is discouraged and may lead to unexpected behaviour.\n *\n * This function is intended for Sentry SDK and SDK integration development. It is not recommended to be used in \"normal\"\n * applications directly because it comes with pitfalls. Use at your own risk!\n *\n * @param callback The callback in which the passed isolation scope is active. (Note: In environments without async\n * context strategy, the currently active isolation scope may change within execution of the callback.)\n * @returns The same value that `callback` returns.\n */\nexport function withIsolationScope<T>(callback: (isolationScope: Scope) => T): T {\n  return runWithAsyncContext(() => {\n    return callback(getIsolationScope());\n  });\n}\n\n/**\n * Forks the current scope and sets the provided span as active span in the context of the provided callback.\n *\n * @param span Spans started in the context of the provided callback will be children of this span.\n * @param callback Execution context in which the provided span will be active. Is passed the newly forked scope.\n * @returns the value returned from the provided callback function.\n */\nexport function withActiveSpan<T>(span: Span, callback: (scope: Scope) => T): T {\n  return withScope(scope => {\n    // eslint-disable-next-line deprecation/deprecation\n    scope.setSpan(span);\n    return callback(scope);\n  });\n}\n\n/**\n * Starts a new `Transaction` and returns it. This is the entry point to manual tracing instrumentation.\n *\n * A tree structure can be built by adding child spans to the transaction, and child spans to other spans. To start a\n * new child span within the transaction or any span, call the respective `.startChild()` method.\n *\n * Every child span must be finished before the transaction is finished, otherwise the unfinished spans are discarded.\n *\n * The transaction must be finished with a call to its `.end()` method, at which point the transaction with all its\n * finished child spans will be sent to Sentry.\n *\n * NOTE: This function should only be used for *manual* instrumentation. Auto-instrumentation should call\n * `startTransaction` directly on the hub.\n *\n * @param context Properties of the new `Transaction`.\n * @param customSamplingContext Information given to the transaction sampling function (along with context-dependent\n * default values). See {@link Options.tracesSampler}.\n *\n * @returns The transaction which was just started\n *\n * @deprecated Use `startSpan()`, `startSpanManual()` or `startInactiveSpan()` instead.\n */\nexport function startTransaction(\n  context: TransactionContext,\n  customSamplingContext?: CustomSamplingContext,\n  // eslint-disable-next-line deprecation/deprecation\n): ReturnType<Hub['startTransaction']> {\n  // eslint-disable-next-line deprecation/deprecation\n  return getCurrentHub().startTransaction({ ...context }, customSamplingContext);\n}\n\n/**\n * Create a cron monitor check in and send it to Sentry.\n *\n * @param checkIn An object that describes a check in.\n * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n * to create a monitor automatically when sending a check in.\n */\nexport function captureCheckIn(checkIn: CheckIn, upsertMonitorConfig?: MonitorConfig): string {\n  const scope = getCurrentScope();\n  const client = getClient();\n  if (!client) {\n    DEBUG_BUILD && logger.warn('Cannot capture check-in. No client defined.');\n  } else if (!client.captureCheckIn) {\n    DEBUG_BUILD && logger.warn('Cannot capture check-in. Client does not support sending check-ins.');\n  } else {\n    return client.captureCheckIn(checkIn, upsertMonitorConfig, scope);\n  }\n\n  return uuid4();\n}\n\n/**\n * Wraps a callback with a cron monitor check in. The check in will be sent to Sentry when the callback finishes.\n *\n * @param monitorSlug The distinct slug of the monitor.\n * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n * to create a monitor automatically when sending a check in.\n */\nexport function withMonitor<T>(\n  monitorSlug: CheckIn['monitorSlug'],\n  callback: () => T,\n  upsertMonitorConfig?: MonitorConfig,\n): T {\n  const checkInId = captureCheckIn({ monitorSlug, status: 'in_progress' }, upsertMonitorConfig);\n  const now = timestampInSeconds();\n\n  function finishCheckIn(status: FinishedCheckIn['status']): void {\n    captureCheckIn({ monitorSlug, status, checkInId, duration: timestampInSeconds() - now });\n  }\n\n  let maybePromiseResult: T;\n  try {\n    maybePromiseResult = callback();\n  } catch (e) {\n    finishCheckIn('error');\n    throw e;\n  }\n\n  if (isThenable(maybePromiseResult)) {\n    Promise.resolve(maybePromiseResult).then(\n      () => {\n        finishCheckIn('ok');\n      },\n      () => {\n        finishCheckIn('error');\n      },\n    );\n  } else {\n    finishCheckIn('ok');\n  }\n\n  return maybePromiseResult;\n}\n\n/**\n * Call `flush()` on the current client, if there is one. See {@link Client.flush}.\n *\n * @param timeout Maximum time in ms the client should wait to flush its event queue. Omitting this parameter will cause\n * the client to wait until all events are sent before resolving the promise.\n * @returns A promise which resolves to `true` if the queue successfully drains before the timeout, or `false` if it\n * doesn't (or if there's no client defined).\n */\nexport async function flush(timeout?: number): Promise<boolean> {\n  const client = getClient();\n  if (client) {\n    return client.flush(timeout);\n  }\n  DEBUG_BUILD && logger.warn('Cannot flush events. No client defined.');\n  return Promise.resolve(false);\n}\n\n/**\n * Call `close()` on the current client, if there is one. See {@link Client.close}.\n *\n * @param timeout Maximum time in ms the client should wait to flush its event queue before shutting down. Omitting this\n * parameter will cause the client to wait until all events are sent before disabling itself.\n * @returns A promise which resolves to `true` if the queue successfully drains before the timeout, or `false` if it\n * doesn't (or if there's no client defined).\n */\nexport async function close(timeout?: number): Promise<boolean> {\n  const client = getClient();\n  if (client) {\n    return client.close(timeout);\n  }\n  DEBUG_BUILD && logger.warn('Cannot flush events and disable SDK. No client defined.');\n  return Promise.resolve(false);\n}\n\n/**\n * This is the getter for lastEventId.\n *\n * @returns The last event id of a captured event.\n * @deprecated This function will be removed in the next major version of the Sentry SDK.\n */\nexport function lastEventId(): string | undefined {\n  // eslint-disable-next-line deprecation/deprecation\n  return getCurrentHub().lastEventId();\n}\n\n/**\n * Get the currently active client.\n */\nexport function getClient<C extends Client>(): C | undefined {\n  // eslint-disable-next-line deprecation/deprecation\n  return getCurrentHub().getClient<C>();\n}\n\n/**\n * Returns true if Sentry has been properly initialized.\n */\nexport function isInitialized(): boolean {\n  return !!getClient();\n}\n\n/**\n * Get the currently active scope.\n */\nexport function getCurrentScope(): Scope {\n  // eslint-disable-next-line deprecation/deprecation\n  return getCurrentHub().getScope();\n}\n\n/**\n * Start a session on the current isolation scope.\n *\n * @param context (optional) additional properties to be applied to the returned session object\n *\n * @returns the new active session\n */\nexport function startSession(context?: SessionContext): Session {\n  const client = getClient();\n  const isolationScope = getIsolationScope();\n  const currentScope = getCurrentScope();\n\n  const { release, environment = DEFAULT_ENVIRONMENT } = (client && client.getOptions()) || {};\n\n  // Will fetch userAgent if called from browser sdk\n  const { userAgent } = GLOBAL_OBJ.navigator || {};\n\n  const session = makeSession({\n    release,\n    environment,\n    user: currentScope.getUser() || isolationScope.getUser(),\n    ...(userAgent && { userAgent }),\n    ...context,\n  });\n\n  // End existing session if there's one\n  const currentSession = isolationScope.getSession();\n  if (currentSession && currentSession.status === 'ok') {\n    updateSession(currentSession, { status: 'exited' });\n  }\n\n  endSession();\n\n  // Afterwards we set the new session on the scope\n  isolationScope.setSession(session);\n\n  // TODO (v8): Remove this and only use the isolation scope(?).\n  // For v7 though, we can't \"soft-break\" people using getCurrentHub().getScope().setSession()\n  currentScope.setSession(session);\n\n  return session;\n}\n\n/**\n * End the session on the current isolation scope.\n */\nexport function endSession(): void {\n  const isolationScope = getIsolationScope();\n  const currentScope = getCurrentScope();\n\n  const session = currentScope.getSession() || isolationScope.getSession();\n  if (session) {\n    closeSession(session);\n  }\n  _sendSessionUpdate();\n\n  // the session is over; take it off of the scope\n  isolationScope.setSession();\n\n  // TODO (v8): Remove this and only use the isolation scope(?).\n  // For v7 though, we can't \"soft-break\" people using getCurrentHub().getScope().setSession()\n  currentScope.setSession();\n}\n\n/**\n * Sends the current Session on the scope\n */\nfunction _sendSessionUpdate(): void {\n  const isolationScope = getIsolationScope();\n  const currentScope = getCurrentScope();\n  const client = getClient();\n  // TODO (v8): Remove currentScope and only use the isolation scope(?).\n  // For v7 though, we can't \"soft-break\" people using getCurrentHub().getScope().setSession()\n  const session = currentScope.getSession() || isolationScope.getSession();\n  if (session && client && client.captureSession) {\n    client.captureSession(session);\n  }\n}\n\n/**\n * Sends the current session on the scope to Sentry\n *\n * @param end If set the session will be marked as exited and removed from the scope.\n *            Defaults to `false`.\n */\nexport function captureSession(end: boolean = false): void {\n  // both send the update and pull the session from the scope\n  if (end) {\n    endSession();\n    return;\n  }\n\n  // only send the update\n  _sendSessionUpdate();\n}\n"], "names": [], "mappings": ";;;;;;;AAmCA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,gBAAgB;AAChC;AACA,EAAE,SAAS;AACX,EAAE,IAAI;AACN,EAAU;AACV;AACA,EAAE,OAAO,aAAa,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,8BAA8B,CAAC,IAAI,CAAC,CAAC,CAAA;AAC1F,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc;AAC9B,EAAE,OAAO;AACT;AACA,EAAE,cAAc;AAChB,EAAU;AACV;AACA;AACA,EAAE,MAAM,KAAM,GAAE,OAAO,cAAA,KAAmB,QAAS,GAAE,cAAe,GAAE,SAAS,CAAA;AAC/E,EAAE,MAAM,OAAA,GAAU,OAAO,cAAe,KAAI,QAAS,GAAE,EAAE,cAAA,EAAiB,GAAE,SAAS,CAAA;AACrF;AACA,EAAE,OAAO,aAAa,EAAE,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;AAChE,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,KAAK,EAAS,IAAI,EAAsB;AACrE;AACA,EAAE,OAAO,aAAa,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;AAClD,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,QAAQ,EAA6D;AACpG;AACA,EAAE,aAAa,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;AAC1C,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAC,UAAU,EAAc,IAAI,EAAqD;AAC/G;AACA,EAAE,aAAa,EAAE,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;AACjD,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,CAAC,IAAI,EAAU,OAAO,EAAgE;AAChH;AACA,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;AAC3C,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,SAAS,CAAC,MAAM,EAAwC;AACxE;AACA,EAAE,aAAa,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;AACnC,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,GAAG,EAAU,KAAK,EAAsC;AACjF;AACA,EAAE,aAAa,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;AACtC,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,IAAI,EAA4D;AACxF;AACA,EAAE,aAAa,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;AAC/B,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,MAAM,CAAC,GAAG,EAAU,KAAK,EAAwC;AACjF;AACA,EAAE,aAAa,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;AACpC,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,IAAI,EAA2C;AACvE;AACA,EAAE,aAAa,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;AAC/B,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA;AACA;AACA;AACO,SAAS,SAAS;AACzB,EAAE,GAAG,IAAI;AACT,EAAK;AACL;AACA,EAAE,MAAM,GAAA,GAAM,aAAa,EAAE,CAAA;AAC7B;AACA;AACA,EAAE,IAAI,IAAI,CAAC,MAAO,KAAI,CAAC,EAAE;AACzB,IAAI,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAA,GAAI,IAAI,CAAA;AAClC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB;AACA,MAAM,OAAO,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;AACpC,KAAI;AACJ;AACA;AACA,IAAI,OAAO,GAAG,CAAC,SAAS,CAAC,MAAM;AAC/B;AACA,MAAM,GAAG,CAAC,WAAW,EAAE,CAAC,KAAA,GAAQ,KAAM,EAAA;AACtC,MAAM,OAAO,QAAQ,CAAC,KAAA,EAAe,CAAA;AACrC,KAAK,CAAC,CAAA;AACN,GAAE;AACF;AACA;AACA,EAAE,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;AAC/B,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,kBAAkB,CAAI,QAAQ,EAAmC;AACjF,EAAE,OAAO,mBAAmB,CAAC,MAAM;AACnC,IAAI,OAAO,QAAQ,CAAC,iBAAiB,EAAE,CAAC,CAAA;AACxC,GAAG,CAAC,CAAA;AACJ,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAI,IAAI,EAAQ,QAAQ,EAA0B;AAChF,EAAE,OAAO,SAAS,CAAC,KAAA,IAAS;AAC5B;AACA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;AACvB,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAA;AAC1B,GAAG,CAAC,CAAA;AACJ,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,gBAAgB;AAChC,EAAE,OAAO;AACT,EAAE,qBAAqB;AACvB;AACA,EAAuC;AACvC;AACA,EAAE,OAAO,aAAa,EAAE,CAAC,gBAAgB,CAAC,EAAE,GAAG,OAAA,EAAS,EAAE,qBAAqB,CAAC,CAAA;AAChF,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,OAAO,EAAW,mBAAmB,EAA0B;AAC9F,EAAE,MAAM,KAAA,GAAQ,eAAe,EAAE,CAAA;AACjC,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE,CAAA;AAC5B,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,eAAe,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAA;AAC7E,GAAE,MAAO,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;AACrC,IAAI,eAAe,MAAM,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAA;AACrG,SAAS;AACT,IAAI,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAA;AACrE,GAAE;AACF;AACA,EAAE,OAAO,KAAK,EAAE,CAAA;AAChB,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW;AAC3B,EAAE,WAAW;AACb,EAAE,QAAQ;AACV,EAAE,mBAAmB;AACrB,EAAK;AACL,EAAE,MAAM,SAAA,GAAY,cAAc,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,aAAA,EAAe,EAAE,mBAAmB,CAAC,CAAA;AAC/F,EAAE,MAAM,GAAA,GAAM,kBAAkB,EAAE,CAAA;AAClC;AACA,EAAE,SAAS,aAAa,CAAC,MAAM,EAAmC;AAClE,IAAI,cAAc,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,kBAAkB,KAAK,GAAA,EAAK,CAAC,CAAA;AAC5F,GAAE;AACF;AACA,EAAE,IAAI,kBAAkB,CAAA;AACxB,EAAE,IAAI;AACN,IAAI,kBAAmB,GAAE,QAAQ,EAAE,CAAA;AACnC,GAAI,CAAA,OAAO,CAAC,EAAE;AACd,IAAI,aAAa,CAAC,OAAO,CAAC,CAAA;AAC1B,IAAI,MAAM,CAAC,CAAA;AACX,GAAE;AACF;AACA,EAAE,IAAI,UAAU,CAAC,kBAAkB,CAAC,EAAE;AACtC,IAAI,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI;AAC5C,MAAM,MAAM;AACZ,QAAQ,aAAa,CAAC,IAAI,CAAC,CAAA;AAC3B,OAAO;AACP,MAAM,MAAM;AACZ,QAAQ,aAAa,CAAC,OAAO,CAAC,CAAA;AAC9B,OAAO;AACP,KAAK,CAAA;AACL,SAAS;AACT,IAAI,aAAa,CAAC,IAAI,CAAC,CAAA;AACvB,GAAE;AACF;AACA,EAAE,OAAO,kBAAkB,CAAA;AAC3B,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,eAAe,KAAK,CAAC,OAAO,EAA6B;AAChE,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE,CAAA;AAC5B,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;AAChC,GAAE;AACF,EAAE,eAAe,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAA;AACvE,EAAE,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;AAC/B,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,eAAe,KAAK,CAAC,OAAO,EAA6B;AAChE,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE,CAAA;AAC5B,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;AAChC,GAAE;AACF,EAAE,eAAe,MAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAA;AACvF,EAAE,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;AAC/B,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,GAAuB;AAClD;AACA,EAAE,OAAO,aAAa,EAAE,CAAC,WAAW,EAAE,CAAA;AACtC,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,SAAS,GAAoC;AAC7D;AACA,EAAE,OAAO,aAAa,EAAE,CAAC,SAAS,EAAK,CAAA;AACvC,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,GAAY;AACzC,EAAE,OAAO,CAAC,CAAC,SAAS,EAAE,CAAA;AACtB,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe,GAAU;AACzC;AACA,EAAE,OAAO,aAAa,EAAE,CAAC,QAAQ,EAAE,CAAA;AACnC,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,OAAO,EAA4B;AAChE,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE,CAAA;AAC5B,EAAE,MAAM,cAAA,GAAiB,iBAAiB,EAAE,CAAA;AAC5C,EAAE,MAAM,YAAA,GAAe,eAAe,EAAE,CAAA;AACxC;AACA,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,mBAAA,KAAwB,CAAC,UAAU,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,CAAA;AAC9F;AACA;AACA,EAAE,MAAM,EAAE,SAAA,EAAY,GAAE,UAAU,CAAC,SAAA,IAAa,EAAE,CAAA;AAClD;AACA,EAAE,MAAM,OAAA,GAAU,WAAW,CAAC;AAC9B,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,IAAI,EAAE,YAAY,CAAC,OAAO,EAAG,IAAG,cAAc,CAAC,OAAO,EAAE;AAC5D,IAAI,IAAI,SAAA,IAAa,EAAE,SAAA,EAAW,CAAC;AACnC,IAAI,GAAG,OAAO;AACd,GAAG,CAAC,CAAA;AACJ;AACA;AACA,EAAE,MAAM,cAAe,GAAE,cAAc,CAAC,UAAU,EAAE,CAAA;AACpD,EAAE,IAAI,cAAe,IAAG,cAAc,CAAC,MAAA,KAAW,IAAI,EAAE;AACxD,IAAI,aAAa,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,QAAS,EAAC,CAAC,CAAA;AACvD,GAAE;AACF;AACA,EAAE,UAAU,EAAE,CAAA;AACd;AACA;AACA,EAAE,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;AACpC;AACA;AACA;AACA,EAAE,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;AAClC;AACA,EAAE,OAAO,OAAO,CAAA;AAChB,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,GAAS;AACnC,EAAE,MAAM,cAAA,GAAiB,iBAAiB,EAAE,CAAA;AAC5C,EAAE,MAAM,YAAA,GAAe,eAAe,EAAE,CAAA;AACxC;AACA,EAAE,MAAM,OAAA,GAAU,YAAY,CAAC,UAAU,EAAC,IAAK,cAAc,CAAC,UAAU,EAAE,CAAA;AAC1E,EAAE,IAAI,OAAO,EAAE;AACf,IAAI,YAAY,CAAC,OAAO,CAAC,CAAA;AACzB,GAAE;AACF,EAAE,kBAAkB,EAAE,CAAA;AACtB;AACA;AACA,EAAE,cAAc,CAAC,UAAU,EAAE,CAAA;AAC7B;AACA;AACA;AACA,EAAE,YAAY,CAAC,UAAU,EAAE,CAAA;AAC3B,CAAA;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,GAAS;AACpC,EAAE,MAAM,cAAA,GAAiB,iBAAiB,EAAE,CAAA;AAC5C,EAAE,MAAM,YAAA,GAAe,eAAe,EAAE,CAAA;AACxC,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE,CAAA;AAC5B;AACA;AACA,EAAE,MAAM,OAAA,GAAU,YAAY,CAAC,UAAU,EAAC,IAAK,cAAc,CAAC,UAAU,EAAE,CAAA;AAC1E,EAAE,IAAI,OAAQ,IAAG,UAAU,MAAM,CAAC,cAAc,EAAE;AAClD,IAAI,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;AAClC,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,GAAG,GAAY,KAAK,EAAQ;AAC3D;AACA,EAAE,IAAI,GAAG,EAAE;AACX,IAAI,UAAU,EAAE,CAAA;AAChB,IAAI,OAAM;AACV,GAAE;AACF;AACA;AACA,EAAE,kBAAkB,EAAE,CAAA;AACtB;;;;"}