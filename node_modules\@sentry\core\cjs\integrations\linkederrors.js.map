{"version": 3, "file": "linkederrors.js", "sources": ["../../../src/integrations/linkederrors.ts"], "sourcesContent": ["import type { Client, Event, EventHint, Integration, IntegrationClass, IntegrationFn } from '@sentry/types';\nimport { applyAggregateErrorsToEvent, exceptionFromError } from '@sentry/utils';\nimport { convertIntegrationFnToClass, defineIntegration } from '../integration';\n\ninterface LinkedErrorsOptions {\n  key?: string;\n  limit?: number;\n}\n\nconst DEFAULT_KEY = 'cause';\nconst DEFAULT_LIMIT = 5;\n\nconst INTEGRATION_NAME = 'LinkedErrors';\n\nconst _linkedErrorsIntegration = ((options: LinkedErrorsOptions = {}) => {\n  const limit = options.limit || DEFAULT_LIMIT;\n  const key = options.key || DEFAULT_KEY;\n\n  return {\n    name: INTEGRATION_NAME,\n    // TODO v8: Remove this\n    setupOnce() {}, // eslint-disable-line @typescript-eslint/no-empty-function\n    preprocessEvent(event, hint, client) {\n      const options = client.getOptions();\n\n      applyAggregateErrorsToEvent(\n        exceptionFromError,\n        options.stackParser,\n        options.maxValueLength,\n        key,\n        limit,\n        event,\n        hint,\n      );\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const linkedErrorsIntegration = defineIntegration(_linkedErrorsIntegration);\n\n/**\n * Adds SDK info to an event.\n * @deprecated Use `linkedErrorsIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const LinkedErrors = convertIntegrationFnToClass(INTEGRATION_NAME, linkedErrorsIntegration) as IntegrationClass<\n  Integration & { preprocessEvent: (event: Event, hint: EventHint, client: Client) => void }\n> & { new (options?: { key?: string; limit?: number }): Integration };\n"], "names": ["applyAggregateErrorsToEvent", "exceptionFromError", "defineIntegration", "convertIntegrationFnToClass"], "mappings": ";;;;;AASA,MAAM,WAAA,GAAc,OAAO,CAAA;AAC3B,MAAM,aAAA,GAAgB,CAAC,CAAA;AACvB;AACA,MAAM,gBAAA,GAAmB,cAAc,CAAA;AACvC;AACA,MAAM,wBAAA,IAA4B,CAAC,OAAO,GAAwB,EAAE,KAAK;AACzE,EAAE,MAAM,KAAM,GAAE,OAAO,CAAC,KAAA,IAAS,aAAa,CAAA;AAC9C,EAAE,MAAM,GAAI,GAAE,OAAO,CAAC,GAAA,IAAO,WAAW,CAAA;AACxC;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B;AACA,IAAI,SAAS,GAAG,EAAE;AAClB,IAAI,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE;AACzC,MAAM,MAAM,OAAQ,GAAE,MAAM,CAAC,UAAU,EAAE,CAAA;AACzC;AACA,MAAMA,iCAA2B;AACjC,QAAQC,wBAAkB;AAC1B,QAAQ,OAAO,CAAC,WAAW;AAC3B,QAAQ,OAAO,CAAC,cAAc;AAC9B,QAAQ,GAAG;AACX,QAAQ,KAAK;AACb,QAAQ,KAAK;AACb,QAAQ,IAAI;AACZ,OAAO,CAAA;AACP,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;MACa,uBAAwB,GAAEC,6BAAiB,CAAC,wBAAwB,EAAC;AAClF;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,eAAeC,uCAA2B,CAAC,gBAAgB,EAAE,uBAAuB,CAAE;;;;;;;"}