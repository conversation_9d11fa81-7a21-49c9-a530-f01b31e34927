{"version": 3, "file": "applyScopeDataToEvent.js", "sources": ["../../../src/utils/applyScopeDataToEvent.ts"], "sourcesContent": ["import type { Breadcrumb, Event, ScopeData, Span } from '@sentry/types';\nimport { arrayify, dropUndefinedKeys } from '@sentry/utils';\nimport { getDynamicSamplingContextFromSpan } from '../tracing/dynamicSamplingContext';\nimport { getRootSpan } from './getRootSpan';\nimport { spanToJSON, spanToTraceContext } from './spanUtils';\n\n/**\n * Applies data from the scope to the event and runs all event processors on it.\n */\nexport function applyScopeDataToEvent(event: Event, data: ScopeData): void {\n  const { fingerprint, span, breadcrumbs, sdkProcessingMetadata } = data;\n\n  // Apply general data\n  applyDataToEvent(event, data);\n\n  // We want to set the trace context for normal events only if there isn't already\n  // a trace context on the event. There is a product feature in place where we link\n  // errors with transaction and it relies on that.\n  if (span) {\n    applySpanToEvent(event, span);\n  }\n\n  applyFingerprintToEvent(event, fingerprint);\n  applyBreadcrumbsToEvent(event, breadcrumbs);\n  applySdkMetadataToEvent(event, sdkProcessingMetadata);\n}\n\n/** Merge data of two scopes together. */\nexport function mergeScopeData(data: ScopeData, mergeData: ScopeData): void {\n  const {\n    extra,\n    tags,\n    user,\n    contexts,\n    level,\n    sdkProcessingMetadata,\n    breadcrumbs,\n    fingerprint,\n    eventProcessors,\n    attachments,\n    propagationContext,\n    // eslint-disable-next-line deprecation/deprecation\n    transactionName,\n    span,\n  } = mergeData;\n\n  mergeAndOverwriteScopeData(data, 'extra', extra);\n  mergeAndOverwriteScopeData(data, 'tags', tags);\n  mergeAndOverwriteScopeData(data, 'user', user);\n  mergeAndOverwriteScopeData(data, 'contexts', contexts);\n  mergeAndOverwriteScopeData(data, 'sdkProcessingMetadata', sdkProcessingMetadata);\n\n  if (level) {\n    data.level = level;\n  }\n\n  if (transactionName) {\n    // eslint-disable-next-line deprecation/deprecation\n    data.transactionName = transactionName;\n  }\n\n  if (span) {\n    data.span = span;\n  }\n\n  if (breadcrumbs.length) {\n    data.breadcrumbs = [...data.breadcrumbs, ...breadcrumbs];\n  }\n\n  if (fingerprint.length) {\n    data.fingerprint = [...data.fingerprint, ...fingerprint];\n  }\n\n  if (eventProcessors.length) {\n    data.eventProcessors = [...data.eventProcessors, ...eventProcessors];\n  }\n\n  if (attachments.length) {\n    data.attachments = [...data.attachments, ...attachments];\n  }\n\n  data.propagationContext = { ...data.propagationContext, ...propagationContext };\n}\n\n/**\n * Merges certain scope data. Undefined values will overwrite any existing values.\n * Exported only for tests.\n */\nexport function mergeAndOverwriteScopeData<\n  Prop extends 'extra' | 'tags' | 'user' | 'contexts' | 'sdkProcessingMetadata',\n  Data extends ScopeData,\n>(data: Data, prop: Prop, mergeVal: Data[Prop]): void {\n  if (mergeVal && Object.keys(mergeVal).length) {\n    // Clone object\n    data[prop] = { ...data[prop] };\n    for (const key in mergeVal) {\n      if (Object.prototype.hasOwnProperty.call(mergeVal, key)) {\n        data[prop][key] = mergeVal[key];\n      }\n    }\n  }\n}\n\n/** Exported only for tests */\nexport function mergeArray<Prop extends 'breadcrumbs' | 'fingerprint'>(\n  event: Event,\n  prop: Prop,\n  mergeVal: ScopeData[Prop],\n): void {\n  const prevVal = event[prop];\n  // If we are not merging any new values,\n  // we only need to proceed if there was an empty array before (as we want to replace it with undefined)\n  if (!mergeVal.length && (!prevVal || prevVal.length)) {\n    return;\n  }\n\n  const merged = [...(prevVal || []), ...mergeVal] as ScopeData[Prop];\n  event[prop] = merged.length ? merged : undefined;\n}\n\nfunction applyDataToEvent(event: Event, data: ScopeData): void {\n  const {\n    extra,\n    tags,\n    user,\n    contexts,\n    level,\n    // eslint-disable-next-line deprecation/deprecation\n    transactionName,\n  } = data;\n\n  const cleanedExtra = dropUndefinedKeys(extra);\n  if (cleanedExtra && Object.keys(cleanedExtra).length) {\n    event.extra = { ...cleanedExtra, ...event.extra };\n  }\n\n  const cleanedTags = dropUndefinedKeys(tags);\n  if (cleanedTags && Object.keys(cleanedTags).length) {\n    event.tags = { ...cleanedTags, ...event.tags };\n  }\n\n  const cleanedUser = dropUndefinedKeys(user);\n  if (cleanedUser && Object.keys(cleanedUser).length) {\n    event.user = { ...cleanedUser, ...event.user };\n  }\n\n  const cleanedContexts = dropUndefinedKeys(contexts);\n  if (cleanedContexts && Object.keys(cleanedContexts).length) {\n    event.contexts = { ...cleanedContexts, ...event.contexts };\n  }\n\n  if (level) {\n    event.level = level;\n  }\n\n  if (transactionName) {\n    event.transaction = transactionName;\n  }\n}\n\nfunction applyBreadcrumbsToEvent(event: Event, breadcrumbs: Breadcrumb[]): void {\n  const mergedBreadcrumbs = [...(event.breadcrumbs || []), ...breadcrumbs];\n  event.breadcrumbs = mergedBreadcrumbs.length ? mergedBreadcrumbs : undefined;\n}\n\nfunction applySdkMetadataToEvent(event: Event, sdkProcessingMetadata: ScopeData['sdkProcessingMetadata']): void {\n  event.sdkProcessingMetadata = {\n    ...event.sdkProcessingMetadata,\n    ...sdkProcessingMetadata,\n  };\n}\n\nfunction applySpanToEvent(event: Event, span: Span): void {\n  event.contexts = { trace: spanToTraceContext(span), ...event.contexts };\n  const rootSpan = getRootSpan(span);\n  if (rootSpan) {\n    event.sdkProcessingMetadata = {\n      dynamicSamplingContext: getDynamicSamplingContextFromSpan(span),\n      ...event.sdkProcessingMetadata,\n    };\n    const transactionName = spanToJSON(rootSpan).description;\n    if (transactionName) {\n      event.tags = { transaction: transactionName, ...event.tags };\n    }\n  }\n}\n\n/**\n * Applies fingerprint from the scope to the event if there's one,\n * uses message if there's one instead or get rid of empty fingerprint\n */\nfunction applyFingerprintToEvent(event: Event, fingerprint: ScopeData['fingerprint'] | undefined): void {\n  // Make sure it's an array first and we actually have something in place\n  event.fingerprint = event.fingerprint ? arrayify(event.fingerprint) : [];\n\n  // If we have something on the scope, then merge it with event\n  if (fingerprint) {\n    event.fingerprint = event.fingerprint.concat(fingerprint);\n  }\n\n  // If we have no data at all, remove empty array default\n  if (event.fingerprint && !event.fingerprint.length) {\n    delete event.fingerprint;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAMA;AACA;AACA;AACO,SAAS,qBAAqB,CAAC,KAAK,EAAS,IAAI,EAAmB;AAC3E,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE,qBAAA,EAAwB,GAAE,IAAI,CAAA;AACxE;AACA;AACA,EAAE,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;AAC/B;AACA;AACA;AACA;AACA,EAAE,IAAI,IAAI,EAAE;AACZ,IAAI,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;AACjC,GAAE;AACF;AACA,EAAE,uBAAuB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;AAC7C,EAAE,uBAAuB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;AAC7C,EAAE,uBAAuB,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAA;AACvD,CAAA;AACA;AACA;AACO,SAAS,cAAc,CAAC,IAAI,EAAa,SAAS,EAAmB;AAC5E,EAAE,MAAM;AACR,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,qBAAqB;AACzB,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,eAAe;AACnB,IAAI,WAAW;AACf,IAAI,kBAAkB;AACtB;AACA,IAAI,eAAe;AACnB,IAAI,IAAI;AACR,GAAE,GAAI,SAAS,CAAA;AACf;AACA,EAAE,0BAA0B,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;AAClD,EAAE,0BAA0B,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;AAChD,EAAE,0BAA0B,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;AAChD,EAAE,0BAA0B,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAA;AACxD,EAAE,0BAA0B,CAAC,IAAI,EAAE,uBAAuB,EAAE,qBAAqB,CAAC,CAAA;AAClF;AACA,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,IAAI,CAAC,KAAM,GAAE,KAAK,CAAA;AACtB,GAAE;AACF;AACA,EAAE,IAAI,eAAe,EAAE;AACvB;AACA,IAAI,IAAI,CAAC,eAAgB,GAAE,eAAe,CAAA;AAC1C,GAAE;AACF;AACA,EAAE,IAAI,IAAI,EAAE;AACZ,IAAI,IAAI,CAAC,IAAK,GAAE,IAAI,CAAA;AACpB,GAAE;AACF;AACA,EAAE,IAAI,WAAW,CAAC,MAAM,EAAE;AAC1B,IAAI,IAAI,CAAC,WAAY,GAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,WAAW,CAAC,CAAA;AAC5D,GAAE;AACF;AACA,EAAE,IAAI,WAAW,CAAC,MAAM,EAAE;AAC1B,IAAI,IAAI,CAAC,WAAY,GAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,WAAW,CAAC,CAAA;AAC5D,GAAE;AACF;AACA,EAAE,IAAI,eAAe,CAAC,MAAM,EAAE;AAC9B,IAAI,IAAI,CAAC,eAAgB,GAAE,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,eAAe,CAAC,CAAA;AACxE,GAAE;AACF;AACA,EAAE,IAAI,WAAW,CAAC,MAAM,EAAE;AAC1B,IAAI,IAAI,CAAC,WAAY,GAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,WAAW,CAAC,CAAA;AAC5D,GAAE;AACF;AACA,EAAE,IAAI,CAAC,kBAAmB,GAAE,EAAE,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,kBAAA,EAAoB,CAAA;AACjF,CAAA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,0BAA0B;;AAG1C,CAAE,IAAI,EAAQ,IAAI,EAAQ,QAAQ,EAAoB;AACtD,EAAE,IAAI,QAAS,IAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE;AAChD;AACA,IAAI,IAAI,CAAC,IAAI,CAAE,GAAE,EAAE,GAAG,IAAI,CAAC,IAAI,CAAA,EAAG,CAAA;AAClC,IAAI,KAAK,MAAM,GAAI,IAAG,QAAQ,EAAE;AAChC,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;AAC/D,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAA,GAAI,QAAQ,CAAC,GAAG,CAAC,CAAA;AACvC,OAAM;AACN,KAAI;AACJ,GAAE;AACF,CAAA;AAkBA;AACA,SAAS,gBAAgB,CAAC,KAAK,EAAS,IAAI,EAAmB;AAC/D,EAAE,MAAM;AACR,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT;AACA,IAAI,eAAe;AACnB,GAAE,GAAI,IAAI,CAAA;AACV;AACA,EAAE,MAAM,YAAa,GAAE,iBAAiB,CAAC,KAAK,CAAC,CAAA;AAC/C,EAAE,IAAI,YAAa,IAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE;AACxD,IAAI,KAAK,CAAC,KAAM,GAAE,EAAE,GAAG,YAAY,EAAE,GAAG,KAAK,CAAC,KAAA,EAAO,CAAA;AACrD,GAAE;AACF;AACA,EAAE,MAAM,WAAY,GAAE,iBAAiB,CAAC,IAAI,CAAC,CAAA;AAC7C,EAAE,IAAI,WAAY,IAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE;AACtD,IAAI,KAAK,CAAC,IAAK,GAAE,EAAE,GAAG,WAAW,EAAE,GAAG,KAAK,CAAC,IAAA,EAAM,CAAA;AAClD,GAAE;AACF;AACA,EAAE,MAAM,WAAY,GAAE,iBAAiB,CAAC,IAAI,CAAC,CAAA;AAC7C,EAAE,IAAI,WAAY,IAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE;AACtD,IAAI,KAAK,CAAC,IAAK,GAAE,EAAE,GAAG,WAAW,EAAE,GAAG,KAAK,CAAC,IAAA,EAAM,CAAA;AAClD,GAAE;AACF;AACA,EAAE,MAAM,eAAgB,GAAE,iBAAiB,CAAC,QAAQ,CAAC,CAAA;AACrD,EAAE,IAAI,eAAgB,IAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE;AAC9D,IAAI,KAAK,CAAC,QAAS,GAAE,EAAE,GAAG,eAAe,EAAE,GAAG,KAAK,CAAC,QAAA,EAAU,CAAA;AAC9D,GAAE;AACF;AACA,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,KAAK,CAAC,KAAM,GAAE,KAAK,CAAA;AACvB,GAAE;AACF;AACA,EAAE,IAAI,eAAe,EAAE;AACvB,IAAI,KAAK,CAAC,WAAY,GAAE,eAAe,CAAA;AACvC,GAAE;AACF,CAAA;AACA;AACA,SAAS,uBAAuB,CAAC,KAAK,EAAS,WAAW,EAAsB;AAChF,EAAE,MAAM,iBAAkB,GAAE,CAAC,IAAI,KAAK,CAAC,WAAY,IAAG,EAAE,CAAC,EAAE,GAAG,WAAW,CAAC,CAAA;AAC1E,EAAE,KAAK,CAAC,WAAA,GAAc,iBAAiB,CAAC,MAAO,GAAE,iBAAkB,GAAE,SAAS,CAAA;AAC9E,CAAA;AACA;AACA,SAAS,uBAAuB,CAAC,KAAK,EAAS,qBAAqB,EAA4C;AAChH,EAAE,KAAK,CAAC,qBAAA,GAAwB;AAChC,IAAI,GAAG,KAAK,CAAC,qBAAqB;AAClC,IAAI,GAAG,qBAAqB;AAC5B,GAAG,CAAA;AACH,CAAA;AACA;AACA,SAAS,gBAAgB,CAAC,KAAK,EAAS,IAAI,EAAc;AAC1D,EAAE,KAAK,CAAC,QAAA,GAAW,EAAE,KAAK,EAAE,kBAAkB,CAAC,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,CAAA;AACzE,EAAE,MAAM,QAAS,GAAE,WAAW,CAAC,IAAI,CAAC,CAAA;AACpC,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,KAAK,CAAC,qBAAA,GAAwB;AAClC,MAAM,sBAAsB,EAAE,iCAAiC,CAAC,IAAI,CAAC;AACrE,MAAM,GAAG,KAAK,CAAC,qBAAqB;AACpC,KAAK,CAAA;AACL,IAAI,MAAM,kBAAkB,UAAU,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAA;AAC5D,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,KAAK,CAAC,IAAK,GAAE,EAAE,WAAW,EAAE,eAAe,EAAE,GAAG,KAAK,CAAC,MAAM,CAAA;AAClE,KAAI;AACJ,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,uBAAuB,CAAC,KAAK,EAAS,WAAW,EAA8C;AACxG;AACA,EAAE,KAAK,CAAC,WAAA,GAAc,KAAK,CAAC,WAAY,GAAE,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAE,GAAE,EAAE,CAAA;AAC1E;AACA;AACA,EAAE,IAAI,WAAW,EAAE;AACnB,IAAI,KAAK,CAAC,WAAA,GAAc,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;AAC7D,GAAE;AACF;AACA;AACA,EAAE,IAAI,KAAK,CAAC,WAAY,IAAG,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE;AACtD,IAAI,OAAO,KAAK,CAAC,WAAW,CAAA;AAC5B,GAAE;AACF;;;;"}