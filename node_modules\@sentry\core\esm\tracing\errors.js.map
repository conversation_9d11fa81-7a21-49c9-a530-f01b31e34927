{"version": 3, "file": "errors.js", "sources": ["../../../src/tracing/errors.ts"], "sourcesContent": ["import {\n  addGlobalError<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  addGlobalUnhandledRejectionInstrumentationHandler,\n  logger,\n} from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../debug-build';\nimport type { SpanStatusType } from './spanstatus';\nimport { getActiveTransaction } from './utils';\n\nlet errorsInstrumented = false;\n\n/**\n * Configures global error listeners\n */\nexport function registerErrorInstrumentation(): void {\n  if (errorsInstrumented) {\n    return;\n  }\n\n  errorsInstrumented = true;\n  addGlobalErrorInstrumentationHandler(errorCallback);\n  addGlobalUnhandledRejectionInstrumentationHandler(errorCallback);\n}\n\n/**\n * If an error or unhandled promise occurs, we mark the active transaction as failed\n */\nfunction errorCallback(): void {\n  // eslint-disable-next-line deprecation/deprecation\n  const activeTransaction = getActiveTransaction();\n  if (activeTransaction) {\n    const status: SpanStatusType = 'internal_error';\n    DEBUG_BUILD && logger.log(`[Tracing] Transaction: ${status} -> Global error occured`);\n    activeTransaction.setStatus(status);\n  }\n}\n\n// The function name will be lost when bundling but we need to be able to identify this listener later to maintain the\n// node.js default exit behaviour\nerrorCallback.tag = 'sentry_tracingErrorCallback';\n"], "names": [], "mappings": ";;;;AAUA,IAAI,kBAAA,GAAqB,KAAK,CAAA;AAC9B;AACA;AACA;AACA;AACO,SAAS,4BAA4B,GAAS;AACrD,EAAE,IAAI,kBAAkB,EAAE;AAC1B,IAAI,OAAM;AACV,GAAE;AACF;AACA,EAAE,kBAAA,GAAqB,IAAI,CAAA;AAC3B,EAAE,oCAAoC,CAAC,aAAa,CAAC,CAAA;AACrD,EAAE,iDAAiD,CAAC,aAAa,CAAC,CAAA;AAClE,CAAA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa,GAAS;AAC/B;AACA,EAAE,MAAM,iBAAA,GAAoB,oBAAoB,EAAE,CAAA;AAClD,EAAE,IAAI,iBAAiB,EAAE;AACzB,IAAI,MAAM,MAAM,GAAmB,gBAAgB,CAAA;AACnD,IAAI,WAAY,IAAG,MAAM,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,MAAM,CAAC,wBAAwB,CAAC,CAAC,CAAA;AACzF,IAAI,iBAAiB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;AACvC,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA,aAAa,CAAC,GAAI,GAAE,6BAA6B;;;;"}