{"version": 3, "file": "exports.js", "sources": ["../../../src/metrics/exports.ts"], "sourcesContent": ["import type { ClientOptions, MeasurementUnit, Primitive } from '@sentry/types';\nimport { logger } from '@sentry/utils';\nimport type { BaseClient } from '../baseclient';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { getClient, getCurrentScope } from '../exports';\nimport { spanToJSON } from '../utils/spanUtils';\nimport { COUNTER_METRIC_TYPE, DISTRIBUTION_METRIC_TYPE, GAUGE_METRIC_TYPE, SET_METRIC_TYPE } from './constants';\nimport { MetricsAggregator, metricsAggregatorIntegration } from './integration';\nimport type { MetricType } from './types';\n\ninterface MetricData {\n  unit?: MeasurementUnit;\n  tags?: Record<string, Primitive>;\n  timestamp?: number;\n}\n\nfunction addToMetricsAggregator(\n  metricType: MetricType,\n  name: string,\n  value: number | string,\n  data: MetricData | undefined = {},\n): void {\n  const client = getClient<BaseClient<ClientOptions>>();\n  const scope = getCurrentScope();\n  if (client) {\n    if (!client.metricsAggregator) {\n      DEBUG_BUILD &&\n        logger.warn('No metrics aggregator enabled. Please add the MetricsAggregator integration to use metrics APIs');\n      return;\n    }\n    const { unit, tags, timestamp } = data;\n    const { release, environment } = client.getOptions();\n    // eslint-disable-next-line deprecation/deprecation\n    const transaction = scope.getTransaction();\n    const metricTags: Record<string, string> = {};\n    if (release) {\n      metricTags.release = release;\n    }\n    if (environment) {\n      metricTags.environment = environment;\n    }\n    if (transaction) {\n      metricTags.transaction = spanToJSON(transaction).description || '';\n    }\n\n    DEBUG_BUILD && logger.log(`Adding value of ${value} to ${metricType} metric ${name}`);\n    client.metricsAggregator.add(metricType, name, value, unit, { ...metricTags, ...tags }, timestamp);\n  }\n}\n\n/**\n * Adds a value to a counter metric\n *\n * @experimental This API is experimental and might have breaking changes in the future.\n */\nexport function increment(name: string, value: number = 1, data?: MetricData): void {\n  addToMetricsAggregator(COUNTER_METRIC_TYPE, name, value, data);\n}\n\n/**\n * Adds a value to a distribution metric\n *\n * @experimental This API is experimental and might have breaking changes in the future.\n */\nexport function distribution(name: string, value: number, data?: MetricData): void {\n  addToMetricsAggregator(DISTRIBUTION_METRIC_TYPE, name, value, data);\n}\n\n/**\n * Adds a value to a set metric. Value must be a string or integer.\n *\n * @experimental This API is experimental and might have breaking changes in the future.\n */\nexport function set(name: string, value: number | string, data?: MetricData): void {\n  addToMetricsAggregator(SET_METRIC_TYPE, name, value, data);\n}\n\n/**\n * Adds a value to a gauge metric\n *\n * @experimental This API is experimental and might have breaking changes in the future.\n */\nexport function gauge(name: string, value: number, data?: MetricData): void {\n  addToMetricsAggregator(GAUGE_METRIC_TYPE, name, value, data);\n}\n\nexport const metrics = {\n  increment,\n  distribution,\n  set,\n  gauge,\n  /** @deprecated Use `metrics.metricsAggregratorIntegration()` instead. */\n  // eslint-disable-next-line deprecation/deprecation\n  MetricsAggregator,\n  metricsAggregatorIntegration,\n};\n"], "names": [], "mappings": ";;;;;;;AAgBA,SAAS,sBAAsB;AAC/B,EAAE,UAAU;AACZ,EAAE,IAAI;AACN,EAAE,KAAK;AACP,EAAE,IAAI,GAA2B,EAAE;AACnC,EAAQ;AACR,EAAE,MAAM,MAAA,GAAS,SAAS,EAA6B,CAAA;AACvD,EAAE,MAAM,KAAA,GAAQ,eAAe,EAAE,CAAA;AACjC,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;AACnC,MAAM,WAAY;AAClB,QAAQ,MAAM,CAAC,IAAI,CAAC,iGAAiG,CAAC,CAAA;AACtH,MAAM,OAAM;AACZ,KAAI;AACJ,IAAI,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,SAAA,EAAY,GAAE,IAAI,CAAA;AAC1C,IAAI,MAAM,EAAE,OAAO,EAAE,WAAA,EAAc,GAAE,MAAM,CAAC,UAAU,EAAE,CAAA;AACxD;AACA,IAAI,MAAM,WAAY,GAAE,KAAK,CAAC,cAAc,EAAE,CAAA;AAC9C,IAAI,MAAM,UAAU,GAA2B,EAAE,CAAA;AACjD,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,UAAU,CAAC,OAAQ,GAAE,OAAO,CAAA;AAClC,KAAI;AACJ,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,UAAU,CAAC,WAAY,GAAE,WAAW,CAAA;AAC1C,KAAI;AACJ,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,UAAU,CAAC,WAAA,GAAc,UAAU,CAAC,WAAW,CAAC,CAAC,WAAY,IAAG,EAAE,CAAA;AACxE,KAAI;AACJ;AACA,IAAI,eAAe,MAAM,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA,CAAA,CAAA;AACA,IAAA,MAAA,CAAA,iBAAA,CAAA,GAAA,CAAA,UAAA,EAAA,IAAA,EAAA,KAAA,EAAA,IAAA,EAAA,EAAA,GAAA,UAAA,EAAA,GAAA,IAAA,EAAA,EAAA,SAAA,CAAA,CAAA;AACA,GAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,SAAA,CAAA,IAAA,EAAA,KAAA,GAAA,CAAA,EAAA,IAAA,EAAA;AACA,EAAA,sBAAA,CAAA,mBAAA,EAAA,IAAA,EAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,YAAA,CAAA,IAAA,EAAA,KAAA,EAAA,IAAA,EAAA;AACA,EAAA,sBAAA,CAAA,wBAAA,EAAA,IAAA,EAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,GAAA,CAAA,IAAA,EAAA,KAAA,EAAA,IAAA,EAAA;AACA,EAAA,sBAAA,CAAA,eAAA,EAAA,IAAA,EAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,KAAA,CAAA,IAAA,EAAA,KAAA,EAAA,IAAA,EAAA;AACA,EAAA,sBAAA,CAAA,iBAAA,EAAA,IAAA,EAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA,MAAA,OAAA,GAAA;AACA,EAAA,SAAA;AACA,EAAA,YAAA;AACA,EAAA,GAAA;AACA,EAAA,KAAA;AACA;AACA;AACA,EAAA,iBAAA;AACA,EAAA,4BAAA;AACA;;;;"}