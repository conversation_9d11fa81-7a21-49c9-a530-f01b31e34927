{"version": 3, "file": "parameterize.js", "sources": ["../../../src/utils/parameterize.ts"], "sourcesContent": ["import type { ParameterizedString } from '@sentry/types';\n\n/**\n * Tagged template function which returns paramaterized representation of the message\n * For example: parameterize`This is a log statement with ${x} and ${y} params`, would return:\n * \"__sentry_template_string__\": 'This is a log statement with %s and %s params',\n * \"__sentry_template_values__\": ['first', 'second']\n * @param strings An array of string values splitted between expressions\n * @param values Expressions extracted from template string\n * @returns String with template information in __sentry_template_string__ and __sentry_template_values__ properties\n */\nexport function parameterize(strings: TemplateStringsArray, ...values: string[]): ParameterizedString {\n  const formatted = new String(String.raw(strings, ...values)) as ParameterizedString;\n  formatted.__sentry_template_string__ = strings.join('\\x00').replace(/%/g, '%%').replace(/\\0/g, '%s');\n  formatted.__sentry_template_values__ = values;\n  return formatted;\n}\n"], "names": [], "mappings": "AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,OAAO,EAAwB,GAAG,MAAM,EAAiC;AACtG,EAAE,MAAM,SAAA,GAAY,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,CAAE,EAAA;AAC/D,EAAE,SAAS,CAAC,0BAA2B,GAAE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;AACtG,EAAE,SAAS,CAAC,0BAA2B,GAAE,MAAM,CAAA;AAC/C,EAAE,OAAO,SAAS,CAAA;AAClB;;;;"}