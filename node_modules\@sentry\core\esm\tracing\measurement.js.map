{"version": 3, "file": "measurement.js", "sources": ["../../../src/tracing/measurement.ts"], "sourcesContent": ["import type { MeasurementUnit } from '@sentry/types';\n\nimport { getActiveTransaction } from './utils';\n\n/**\n * Adds a measurement to the current active transaction.\n */\nexport function setMeasurement(name: string, value: number, unit: MeasurementUnit): void {\n  // eslint-disable-next-line deprecation/deprecation\n  const transaction = getActiveTransaction();\n  if (transaction) {\n    // eslint-disable-next-line deprecation/deprecation\n    transaction.setMeasurement(name, value, unit);\n  }\n}\n"], "names": [], "mappings": ";;AAIA;AACA;AACA;AACO,SAAS,cAAc,CAAC,IAAI,EAAU,KAAK,EAAU,IAAI,EAAyB;AACzF;AACA,EAAE,MAAM,WAAA,GAAc,oBAAoB,EAAE,CAAA;AAC5C,EAAE,IAAI,WAAW,EAAE;AACnB;AACA,IAAI,WAAW,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;AACjD,GAAE;AACF;;;;"}