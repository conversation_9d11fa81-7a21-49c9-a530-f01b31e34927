{"version": 3, "file": "envelope.js", "sources": ["../../src/envelope.ts"], "sourcesContent": ["import type {\n  DsnComponents,\n  Event,\n  EventEnvelope,\n  EventItem,\n  SdkInfo,\n  SdkMetadata,\n  Session,\n  SessionAggregates,\n  SessionEnvelope,\n  SessionItem,\n} from '@sentry/types';\nimport {\n  createEnvelope,\n  createEventEnvelopeHeaders,\n  dsnToString,\n  getSdkMetadataForEnvelopeHeader,\n} from '@sentry/utils';\n\n/**\n * Apply SdkInfo (name, version, packages, integrations) to the corresponding event key.\n * Merge with existing data if any.\n **/\nfunction enhanceEventWithSdkInfo(event: Event, sdkInfo?: SdkInfo): Event {\n  if (!sdkInfo) {\n    return event;\n  }\n  event.sdk = event.sdk || {};\n  event.sdk.name = event.sdk.name || sdkInfo.name;\n  event.sdk.version = event.sdk.version || sdkInfo.version;\n  event.sdk.integrations = [...(event.sdk.integrations || []), ...(sdkInfo.integrations || [])];\n  event.sdk.packages = [...(event.sdk.packages || []), ...(sdkInfo.packages || [])];\n  return event;\n}\n\n/** Creates an envelope from a Session */\nexport function createSessionEnvelope(\n  session: Session | SessionAggregates,\n  dsn?: DsnComponents,\n  metadata?: SdkMetadata,\n  tunnel?: string,\n): SessionEnvelope {\n  const sdkInfo = getSdkMetadataForEnvelopeHeader(metadata);\n  const envelopeHeaders = {\n    sent_at: new Date().toISOString(),\n    ...(sdkInfo && { sdk: sdkInfo }),\n    ...(!!tunnel && dsn && { dsn: dsnToString(dsn) }),\n  };\n\n  const envelopeItem: SessionItem =\n    'aggregates' in session ? [{ type: 'sessions' }, session] : [{ type: 'session' }, session.toJSON()];\n\n  return createEnvelope<SessionEnvelope>(envelopeHeaders, [envelopeItem]);\n}\n\n/**\n * Create an Envelope from an event.\n */\nexport function createEventEnvelope(\n  event: Event,\n  dsn?: DsnComponents,\n  metadata?: SdkMetadata,\n  tunnel?: string,\n): EventEnvelope {\n  const sdkInfo = getSdkMetadataForEnvelopeHeader(metadata);\n\n  /*\n    Note: Due to TS, event.type may be `replay_event`, theoretically.\n    In practice, we never call `createEventEnvelope` with `replay_event` type,\n    and we'd have to adjut a looot of types to make this work properly.\n    We want to avoid casting this around, as that could lead to bugs (e.g. when we add another type)\n    So the safe choice is to really guard against the replay_event type here.\n  */\n  const eventType = event.type && event.type !== 'replay_event' ? event.type : 'event';\n\n  enhanceEventWithSdkInfo(event, metadata && metadata.sdk);\n\n  const envelopeHeaders = createEventEnvelopeHeaders(event, sdkInfo, tunnel, dsn);\n\n  // Prevent this data (which, if it exists, was used in earlier steps in the processing pipeline) from being sent to\n  // sentry. (Note: Our use of this property comes and goes with whatever we might be debugging, whatever hacks we may\n  // have temporarily added, etc. Even if we don't happen to be using it at some point in the future, let's not get rid\n  // of this `delete`, lest we miss putting it back in the next time the property is in use.)\n  delete event.sdkProcessingMetadata;\n\n  const eventItem: EventItem = [{ type: eventType }, event];\n  return createEnvelope<EventEnvelope>(envelopeHeaders, [eventItem]);\n}\n"], "names": ["getSdkMetadataForEnvelopeHeader", "dsnToString", "createEnvelope", "createEventEnvelopeHeaders"], "mappings": ";;;;AAmBA;AACA;AACA;AACA;AACA,SAAS,uBAAuB,CAAC,KAAK,EAAS,OAAO,EAAmB;AACzE,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF,EAAE,KAAK,CAAC,GAAI,GAAE,KAAK,CAAC,GAAA,IAAO,EAAE,CAAA;AAC7B,EAAE,KAAK,CAAC,GAAG,CAAC,OAAO,KAAK,CAAC,GAAG,CAAC,IAAA,IAAQ,OAAO,CAAC,IAAI,CAAA;AACjD,EAAE,KAAK,CAAC,GAAG,CAAC,UAAU,KAAK,CAAC,GAAG,CAAC,OAAA,IAAW,OAAO,CAAC,OAAO,CAAA;AAC1D,EAAE,KAAK,CAAC,GAAG,CAAC,YAAa,GAAE,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,YAAA,IAAgB,EAAE,CAAC,EAAE,IAAI,OAAO,CAAC,YAAA,IAAgB,EAAE,CAAC,CAAC,CAAA;AAC/F,EAAE,KAAK,CAAC,GAAG,CAAC,QAAS,GAAE,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,QAAA,IAAY,EAAE,CAAC,EAAE,IAAI,OAAO,CAAC,QAAA,IAAY,EAAE,CAAC,CAAC,CAAA;AACnF,EAAE,OAAO,KAAK,CAAA;AACd,CAAA;AACA;AACA;AACO,SAAS,qBAAqB;AACrC,EAAE,OAAO;AACT,EAAE,GAAG;AACL,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAmB;AACnB,EAAE,MAAM,OAAQ,GAAEA,qCAA+B,CAAC,QAAQ,CAAC,CAAA;AAC3D,EAAE,MAAM,kBAAkB;AAC1B,IAAI,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;AACrC,IAAI,IAAI,OAAQ,IAAG,EAAE,GAAG,EAAE,OAAQ,EAAC,CAAC;AACpC,IAAI,IAAI,CAAC,CAAC,MAAA,IAAU,GAAI,IAAG,EAAE,GAAG,EAAEC,iBAAW,CAAC,GAAG,CAAA,EAAG,CAAC;AACrD,GAAG,CAAA;AACH;AACA,EAAE,MAAM,YAAY;AACpB,IAAI,YAAA,IAAgB,OAAA,GAAU,CAAC,EAAE,IAAI,EAAE,UAAA,EAAY,EAAE,OAAO,CAAA,GAAI,CAAC,EAAE,IAAI,EAAE,SAAU,EAAC,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAA;AACvG;AACA,EAAE,OAAOC,oBAAc,CAAkB,eAAe,EAAE,CAAC,YAAY,CAAC,CAAC,CAAA;AACzE,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB;AACnC,EAAE,KAAK;AACP,EAAE,GAAG;AACL,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAiB;AACjB,EAAE,MAAM,OAAQ,GAAEF,qCAA+B,CAAC,QAAQ,CAAC,CAAA;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,SAAU,GAAE,KAAK,CAAC,QAAQ,KAAK,CAAC,IAAA,KAAS,cAAe,GAAE,KAAK,CAAC,IAAA,GAAO,OAAO,CAAA;AACtF;AACA,EAAE,uBAAuB,CAAC,KAAK,EAAE,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAA;AAC1D;AACA,EAAE,MAAM,eAAA,GAAkBG,gCAA0B,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC,CAAA;AACjF;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,KAAK,CAAC,qBAAqB,CAAA;AACpC;AACA,EAAE,MAAM,SAAS,GAAc,CAAC,EAAE,IAAI,EAAE,SAAU,EAAC,EAAE,KAAK,CAAC,CAAA;AAC3D,EAAE,OAAOD,oBAAc,CAAgB,eAAe,EAAE,CAAC,SAAS,CAAC,CAAC,CAAA;AACpE;;;;;"}