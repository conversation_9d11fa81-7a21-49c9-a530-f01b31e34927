{"version": 3, "file": "sessionflusher.js", "sources": ["../../src/sessionflusher.ts"], "sourcesContent": ["import type {\n  AggregationCounts,\n  Client,\n  RequestSessionStatus,\n  SessionAggregates,\n  SessionFlusherLike,\n} from '@sentry/types';\nimport { dropUndefinedKeys } from '@sentry/utils';\nimport { getCurrentScope } from './exports';\n\ntype ReleaseHealthAttributes = {\n  environment?: string;\n  release: string;\n};\n\n/**\n * @inheritdoc\n */\nexport class Session<PERSON><PERSON>her implements SessionF<PERSON>her<PERSON>ike {\n  public readonly flushTimeout: number;\n  private _pendingAggregates: Record<number, AggregationCounts>;\n  private _sessionAttrs: ReleaseHealthAttributes;\n  // Cast to any so that it can use Node.js timeout\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _intervalId: any;\n  private _isEnabled: boolean;\n  private _client: Client;\n\n  public constructor(client: Client, attrs: ReleaseHealthAttributes) {\n    this._client = client;\n    this.flushTimeout = 60;\n    this._pendingAggregates = {};\n    this._isEnabled = true;\n\n    // Call to setInterval, so that flush is called every 60 seconds.\n    this._intervalId = setInterval(() => this.flush(), this.flushTimeout * 1000);\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    if (this._intervalId.unref) {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      this._intervalId.unref();\n    }\n    this._sessionAttrs = attrs;\n  }\n\n  /** Checks if `pendingAggregates` has entries, and if it does flushes them by calling `sendSession` */\n  public flush(): void {\n    const sessionAggregates = this.getSessionAggregates();\n    if (sessionAggregates.aggregates.length === 0) {\n      return;\n    }\n    this._pendingAggregates = {};\n    this._client.sendSession(sessionAggregates);\n  }\n\n  /** Massages the entries in `pendingAggregates` and returns aggregated sessions */\n  public getSessionAggregates(): SessionAggregates {\n    const aggregates: AggregationCounts[] = Object.keys(this._pendingAggregates).map((key: string) => {\n      return this._pendingAggregates[parseInt(key)];\n    });\n\n    const sessionAggregates: SessionAggregates = {\n      attrs: this._sessionAttrs,\n      aggregates,\n    };\n    return dropUndefinedKeys(sessionAggregates);\n  }\n\n  /** JSDoc */\n  public close(): void {\n    clearInterval(this._intervalId);\n    this._isEnabled = false;\n    this.flush();\n  }\n\n  /**\n   * Wrapper function for _incrementSessionStatusCount that checks if the instance of SessionFlusher is enabled then\n   * fetches the session status of the request from `Scope.getRequestSession().status` on the scope and passes them to\n   * `_incrementSessionStatusCount` along with the start date\n   */\n  public incrementSessionStatusCount(): void {\n    if (!this._isEnabled) {\n      return;\n    }\n    const scope = getCurrentScope();\n    const requestSession = scope.getRequestSession();\n\n    if (requestSession && requestSession.status) {\n      this._incrementSessionStatusCount(requestSession.status, new Date());\n      // This is not entirely necessarily but is added as a safe guard to indicate the bounds of a request and so in\n      // case captureRequestSession is called more than once to prevent double count\n      scope.setRequestSession(undefined);\n      /* eslint-enable @typescript-eslint/no-unsafe-member-access */\n    }\n  }\n\n  /**\n   * Increments status bucket in pendingAggregates buffer (internal state) corresponding to status of\n   * the session received\n   */\n  private _incrementSessionStatusCount(status: RequestSessionStatus, date: Date): number {\n    // Truncate minutes and seconds on Session Started attribute to have one minute bucket keys\n    const sessionStartedTrunc = new Date(date).setSeconds(0, 0);\n    this._pendingAggregates[sessionStartedTrunc] = this._pendingAggregates[sessionStartedTrunc] || {};\n\n    // corresponds to aggregated sessions in one specific minute bucket\n    // for example, {\"started\":\"2021-03-16T08:00:00.000Z\",\"exited\":4, \"errored\": 1}\n    const aggregationCounts: AggregationCounts = this._pendingAggregates[sessionStartedTrunc];\n    if (!aggregationCounts.started) {\n      aggregationCounts.started = new Date(sessionStartedTrunc).toISOString();\n    }\n\n    switch (status) {\n      case 'errored':\n        aggregationCounts.errored = (aggregationCounts.errored || 0) + 1;\n        return aggregationCounts.errored;\n      case 'ok':\n        aggregationCounts.exited = (aggregationCounts.exited || 0) + 1;\n        return aggregationCounts.exited;\n      default:\n        aggregationCounts.crashed = (aggregationCounts.crashed || 0) + 1;\n        return aggregationCounts.crashed;\n    }\n  }\n}\n"], "names": ["dropUndefinedKeys", "getCurrentScope"], "mappings": ";;;;;AAeA;AACA;AACA;AACO,MAAM,gBAA6C;;AAI1D;AACA;;AAKA,GAAS,WAAW,CAAC,MAAM,EAAU,KAAK,EAA2B;AACrE,IAAI,IAAI,CAAC,OAAQ,GAAE,MAAM,CAAA;AACzB,IAAI,IAAI,CAAC,YAAa,GAAE,EAAE,CAAA;AAC1B,IAAI,IAAI,CAAC,kBAAmB,GAAE,EAAE,CAAA;AAChC,IAAI,IAAI,CAAC,UAAW,GAAE,IAAI,CAAA;AAC1B;AACA;AACA,IAAI,IAAI,CAAC,WAAY,GAAE,WAAW,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,YAAa,GAAE,IAAI,CAAC,CAAA;AAChF;AACA,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;AAChC;AACA,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;AAC9B,KAAI;AACJ,IAAI,IAAI,CAAC,aAAc,GAAE,KAAK,CAAA;AAC9B,GAAE;AACF;AACA;AACA,GAAS,KAAK,GAAS;AACvB,IAAI,MAAM,iBAAkB,GAAE,IAAI,CAAC,oBAAoB,EAAE,CAAA;AACzD,IAAI,IAAI,iBAAiB,CAAC,UAAU,CAAC,MAAA,KAAW,CAAC,EAAE;AACnD,MAAM,OAAM;AACZ,KAAI;AACJ,IAAI,IAAI,CAAC,kBAAmB,GAAE,EAAE,CAAA;AAChC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;AAC/C,GAAE;AACF;AACA;AACA,GAAS,oBAAoB,GAAsB;AACnD,IAAI,MAAM,UAAU,GAAwB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAa;AACtG,MAAM,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAA;AACnD,KAAK,CAAC,CAAA;AACN;AACA,IAAI,MAAM,iBAAiB,GAAsB;AACjD,MAAM,KAAK,EAAE,IAAI,CAAC,aAAa;AAC/B,MAAM,UAAU;AAChB,KAAK,CAAA;AACL,IAAI,OAAOA,uBAAiB,CAAC,iBAAiB,CAAC,CAAA;AAC/C,GAAE;AACF;AACA;AACA,GAAS,KAAK,GAAS;AACvB,IAAI,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;AACnC,IAAI,IAAI,CAAC,UAAW,GAAE,KAAK,CAAA;AAC3B,IAAI,IAAI,CAAC,KAAK,EAAE,CAAA;AAChB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,2BAA2B,GAAS;AAC7C,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAC1B,MAAM,OAAM;AACZ,KAAI;AACJ,IAAI,MAAM,KAAA,GAAQC,yBAAe,EAAE,CAAA;AACnC,IAAI,MAAM,cAAe,GAAE,KAAK,CAAC,iBAAiB,EAAE,CAAA;AACpD;AACA,IAAI,IAAI,cAAA,IAAkB,cAAc,CAAC,MAAM,EAAE;AACjD,MAAM,IAAI,CAAC,4BAA4B,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC,CAAA;AAC1E;AACA;AACA,MAAM,KAAK,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAA;AACxC;AACA,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAU,4BAA4B,CAAC,MAAM,EAAwB,IAAI,EAAgB;AACzF;AACA,IAAI,MAAM,mBAAA,GAAsB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAC/D,IAAI,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAE,GAAE,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAE,IAAG,EAAE,CAAA;AACrG;AACA;AACA;AACA,IAAI,MAAM,iBAAiB,GAAsB,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAA;AAC7F,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE;AACpC,MAAM,iBAAiB,CAAC,OAAQ,GAAE,IAAI,IAAI,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAA;AAC7E,KAAI;AACJ;AACA,IAAI,QAAQ,MAAM;AAClB,MAAM,KAAK,SAAS;AACpB,QAAQ,iBAAiB,CAAC,OAAQ,GAAE,CAAC,iBAAiB,CAAC,OAAA,IAAW,CAAC,IAAI,CAAC,CAAA;AACxE,QAAQ,OAAO,iBAAiB,CAAC,OAAO,CAAA;AACxC,MAAM,KAAK,IAAI;AACf,QAAQ,iBAAiB,CAAC,MAAO,GAAE,CAAC,iBAAiB,CAAC,MAAA,IAAU,CAAC,IAAI,CAAC,CAAA;AACtE,QAAQ,OAAO,iBAAiB,CAAC,MAAM,CAAA;AACvC,MAAM;AACN,QAAQ,iBAAiB,CAAC,OAAQ,GAAE,CAAC,iBAAiB,CAAC,OAAA,IAAW,CAAC,IAAI,CAAC,CAAA;AACxE,QAAQ,OAAO,iBAAiB,CAAC,OAAO,CAAA;AACxC,KAAI;AACJ,GAAE;AACF;;;;"}