{"version": 3, "file": "envelope.js", "sources": ["../../../src/metrics/envelope.ts"], "sourcesContent": ["import type { DsnComponents, MetricBucketItem, SdkMetadata, StatsdEnvelope, StatsdItem } from '@sentry/types';\nimport { createEnvelope, dsnToString } from '@sentry/utils';\nimport { serializeMetricBuckets } from './utils';\n\n/**\n * Create envelope from a metric aggregate.\n */\nexport function createMetricEnvelope(\n  metricBucketItems: Array<MetricBucketItem>,\n  dsn?: DsnComponents,\n  metadata?: SdkMetadata,\n  tunnel?: string,\n): StatsdEnvelope {\n  const headers: StatsdEnvelope[0] = {\n    sent_at: new Date().toISOString(),\n  };\n\n  if (metadata && metadata.sdk) {\n    headers.sdk = {\n      name: metadata.sdk.name,\n      version: metadata.sdk.version,\n    };\n  }\n\n  if (!!tunnel && dsn) {\n    headers.dsn = dsnToString(dsn);\n  }\n\n  const item = createMetricEnvelopeItem(metricBucketItems);\n  return createEnvelope<StatsdEnvelope>(headers, [item]);\n}\n\nfunction createMetricEnvelopeItem(metricBucketItems: MetricBucketItem[]): StatsdItem {\n  const payload = serializeMetricBuckets(metricBucketItems);\n  const metricHeaders: StatsdItem[0] = {\n    type: 'statsd',\n    length: payload.length,\n  };\n  return [metricHeaders, payload];\n}\n"], "names": ["dsnToString", "createEnvelope", "serializeMetricBuckets"], "mappings": ";;;;;AAIA;AACA;AACA;AACO,SAAS,oBAAoB;AACpC,EAAE,iBAAiB;AACnB,EAAE,GAAG;AACL,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAkB;AAClB,EAAE,MAAM,OAAO,GAAsB;AACrC,IAAI,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;AACrC,GAAG,CAAA;AACH;AACA,EAAE,IAAI,QAAA,IAAY,QAAQ,CAAC,GAAG,EAAE;AAChC,IAAI,OAAO,CAAC,GAAA,GAAM;AAClB,MAAM,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI;AAC7B,MAAM,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,OAAO;AACnC,KAAK,CAAA;AACL,GAAE;AACF;AACA,EAAE,IAAI,CAAC,CAAC,MAAO,IAAG,GAAG,EAAE;AACvB,IAAI,OAAO,CAAC,GAAA,GAAMA,iBAAW,CAAC,GAAG,CAAC,CAAA;AAClC,GAAE;AACF;AACA,EAAE,MAAM,IAAK,GAAE,wBAAwB,CAAC,iBAAiB,CAAC,CAAA;AAC1D,EAAE,OAAOC,oBAAc,CAAiB,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC,CAAA;AACxD,CAAA;AACA;AACA,SAAS,wBAAwB,CAAC,iBAAiB,EAAkC;AACrF,EAAE,MAAM,OAAQ,GAAEC,8BAAsB,CAAC,iBAAiB,CAAC,CAAA;AAC3D,EAAE,MAAM,aAAa,GAAkB;AACvC,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,MAAM,EAAE,OAAO,CAAC,MAAM;AAC1B,GAAG,CAAA;AACH,EAAE,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,CAAA;AACjC;;;;"}