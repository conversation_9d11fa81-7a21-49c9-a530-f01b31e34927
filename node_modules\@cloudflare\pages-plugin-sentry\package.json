{"name": "@cloudflare/pages-plugin-sentry", "version": "1.1.3", "homepage": "https://developers.cloudflare.com/pages/platform/functions/plugins/sentry/", "bugs": {"url": "https://github.com/cloudflare/pages-plugins/issues"}, "repository": {"type": "git", "url": "git+https://github.com/cloudflare/pages-plugins.git", "directory": "./packages/sentry"}, "license": "MIT", "type": "module", "exports": {".": {"types": "./index.d.ts", "default": "./dist/functions/index.js"}}, "main": "./dist/functions/index.js", "types": "./index.d.ts", "files": ["./CHANGELOG.md", "./index.d.ts", "./dist/"], "scripts": {"build": "npm run build:functions", "build:functions": "npx wrangler pages functions build --plugin --outdir=./dist/functions --sourcemap", "prepack": "npm run build"}, "devDependencies": {"toucan-js": "^3.0.0"}, "volta": {"node": "20.11.0", "npm": "10.2.4"}}