{"version": 3, "file": "instance.js", "sources": ["../../../src/metrics/instance.ts"], "sourcesContent": ["import type { MetricInstance } from '@sentry/types';\nimport { COUNTER_METRIC_TYPE, DISTRIBUTION_METRIC_TYPE, GAUGE_METRIC_TYPE, SET_METRIC_TYPE } from './constants';\nimport { simpleHash } from './utils';\n\n/**\n * A metric instance representing a counter.\n */\nexport class CounterMetric implements MetricInstance {\n  public constructor(private _value: number) {}\n\n  /** @inheritDoc */\n  public get weight(): number {\n    return 1;\n  }\n\n  /** @inheritdoc */\n  public add(value: number): void {\n    this._value += value;\n  }\n\n  /** @inheritdoc */\n  public toString(): string {\n    return `${this._value}`;\n  }\n}\n\n/**\n * A metric instance representing a gauge.\n */\nexport class GaugeMetric implements MetricInstance {\n  private _last: number;\n  private _min: number;\n  private _max: number;\n  private _sum: number;\n  private _count: number;\n\n  public constructor(value: number) {\n    this._last = value;\n    this._min = value;\n    this._max = value;\n    this._sum = value;\n    this._count = 1;\n  }\n\n  /** @inheritDoc */\n  public get weight(): number {\n    return 5;\n  }\n\n  /** @inheritdoc */\n  public add(value: number): void {\n    this._last = value;\n    if (value < this._min) {\n      this._min = value;\n    }\n    if (value > this._max) {\n      this._max = value;\n    }\n    this._sum += value;\n    this._count++;\n  }\n\n  /** @inheritdoc */\n  public toString(): string {\n    return `${this._last}:${this._min}:${this._max}:${this._sum}:${this._count}`;\n  }\n}\n\n/**\n * A metric instance representing a distribution.\n */\nexport class DistributionMetric implements MetricInstance {\n  private _value: number[];\n\n  public constructor(first: number) {\n    this._value = [first];\n  }\n\n  /** @inheritDoc */\n  public get weight(): number {\n    return this._value.length;\n  }\n\n  /** @inheritdoc */\n  public add(value: number): void {\n    this._value.push(value);\n  }\n\n  /** @inheritdoc */\n  public toString(): string {\n    return this._value.join(':');\n  }\n}\n\n/**\n * A metric instance representing a set.\n */\nexport class SetMetric implements MetricInstance {\n  private _value: Set<number | string>;\n\n  public constructor(public first: number | string) {\n    this._value = new Set([first]);\n  }\n\n  /** @inheritDoc */\n  public get weight(): number {\n    return this._value.size;\n  }\n\n  /** @inheritdoc */\n  public add(value: number | string): void {\n    this._value.add(value);\n  }\n\n  /** @inheritdoc */\n  public toString(): string {\n    return Array.from(this._value)\n      .map(val => (typeof val === 'string' ? simpleHash(val) : val))\n      .join(':');\n  }\n}\n\nexport const METRIC_MAP = {\n  [COUNTER_METRIC_TYPE]: CounterMetric,\n  [GAUGE_METRIC_TYPE]: GaugeMetric,\n  [DISTRIBUTION_METRIC_TYPE]: DistributionMetric,\n  [SET_METRIC_TYPE]: SetMetric,\n};\n"], "names": ["simpleHash", "COUNTER_METRIC_TYPE", "GAUGE_METRIC_TYPE", "DISTRIBUTION_METRIC_TYPE", "SET_METRIC_TYPE"], "mappings": ";;;;;AAIA;AACA;AACA;AACO,MAAM,eAAwC;AACrD,GAAS,WAAW,EAAS,MAAM,EAAU,CAAC,IAAA,CAAA,MAAA,GAAA,MAAA,CAAA,CAAA;AAC9C;AACA;AACA,GAAS,IAAI,MAAM,GAAW;AAC9B,IAAI,OAAO,CAAC,CAAA;AACZ,GAAE;AACF;AACA;AACA,GAAS,GAAG,CAAC,KAAK,EAAgB;AAClC,IAAI,IAAI,CAAC,MAAO,IAAG,KAAK,CAAA;AACxB,GAAE;AACF;AACA;AACA,GAAS,QAAQ,GAAW;AAC5B,IAAI,OAAO,CAAC,EAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AACA,GAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,MAAA,WAAA,EAAA;;AAOA,GAAA,WAAA,CAAA,KAAA,EAAA;AACA,IAAA,IAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACA,IAAA,IAAA,CAAA,IAAA,GAAA,KAAA,CAAA;AACA,IAAA,IAAA,CAAA,IAAA,GAAA,KAAA,CAAA;AACA,IAAA,IAAA,CAAA,IAAA,GAAA,KAAA,CAAA;AACA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA;AACA,GAAA,IAAA,MAAA,GAAA;AACA,IAAA,OAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA;AACA,GAAA,GAAA,CAAA,KAAA,EAAA;AACA,IAAA,IAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACA,IAAA,IAAA,KAAA,GAAA,IAAA,CAAA,IAAA,EAAA;AACA,MAAA,IAAA,CAAA,IAAA,GAAA,KAAA,CAAA;AACA,KAAA;AACA,IAAA,IAAA,KAAA,GAAA,IAAA,CAAA,IAAA,EAAA;AACA,MAAA,IAAA,CAAA,IAAA,GAAA,KAAA,CAAA;AACA,KAAA;AACA,IAAA,IAAA,CAAA,IAAA,IAAA,KAAA,CAAA;AACA,IAAA,IAAA,CAAA,MAAA,EAAA,CAAA;AACA,GAAA;AACA;AACA;AACA,GAAA,QAAA,GAAA;AACA,IAAA,OAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,EAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AACA,GAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,MAAA,kBAAA,EAAA;;AAGA,GAAA,WAAA,CAAA,KAAA,EAAA;AACA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA;AACA,GAAA,IAAA,MAAA,GAAA;AACA,IAAA,OAAA,IAAA,CAAA,MAAA,CAAA,MAAA,CAAA;AACA,GAAA;AACA;AACA;AACA,GAAA,GAAA,CAAA,KAAA,EAAA;AACA,IAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA;AACA,GAAA,QAAA,GAAA;AACA,IAAA,OAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AACA,GAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,MAAA,SAAA,EAAA;;AAGA,GAAA,WAAA,EAAA,KAAA,EAAA,CAAA,IAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACA,IAAA,IAAA,CAAA,MAAA,GAAA,IAAA,GAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA;AACA,GAAA,IAAA,MAAA,GAAA;AACA,IAAA,OAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA;AACA,GAAA;AACA;AACA;AACA,GAAA,GAAA,CAAA,KAAA,EAAA;AACA,IAAA,IAAA,CAAA,MAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA;AACA,GAAA,QAAA,GAAA;AACA,IAAA,OAAA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA;AACA,OAAA,GAAA,CAAA,GAAA,KAAA,OAAA,GAAA,KAAA,QAAA,GAAAA,gBAAA,CAAA,GAAA,CAAA,GAAA,GAAA,CAAA,CAAA;AACA,OAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AACA,GAAA;AACA,CAAA;AACA;AACA,MAAA,UAAA,GAAA;AACA,EAAA,CAAAC,6BAAA,GAAA,aAAA;AACA,EAAA,CAAAC,2BAAA,GAAA,WAAA;AACA,EAAA,CAAAC,kCAAA,GAAA,kBAAA;AACA,EAAA,CAAAC,yBAAA,GAAA,SAAA;AACA;;;;;;;;"}